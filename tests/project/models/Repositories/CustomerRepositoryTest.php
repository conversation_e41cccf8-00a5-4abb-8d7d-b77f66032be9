<?php

namespace Repositories;

use CustomerModule\Entities\Events\DeletedEvent;
use Entities\Customer;
use Entities\CustomerLog;
use OrmModule\Iterators\DoctrineIterator;
use TestModule\Annotations\Inject;
use TestModule\Helpers\DatabaseHelper;
use TestModule\PhpUnit\TestCase;
use tests\helpers\CustomerHelper;
use tests\helpers\CustomerLogHelper;
use Utils\Date;
use tests\helpers\ObjectHelper;
use Entities\Cashback;

class CustomerRepositoryTest extends TestCase
{

    /**
     * @var DatabaseHelper
     */
    private $databaseHelper;

    /**
     * @var CustomerRepository
     */
    private $customerRepository;

    /**
     * @var CustomerLogRepository
     */
    private $customerLogRepository;

    /**
     * @Inject({
     *     "databaseHelper"="test_module.helpers.database_helper",
     *     "customerRepository"="repositories.customer_repository",
     *     "customerLogRepository": "repositories.customer_log_repository"
     * })
     */
    public function setUpDependencies(
        DatabaseHelper $databaseHelper,
        CustomerRepository $customerRepository,
        CustomerLogRepository $customerLogRepository
    )
    {
        $this->databaseHelper = $databaseHelper;
        $this->customerRepository = $customerRepository;
        $this->customerLogRepository = $customerLogRepository;
        $this->databaseHelper->emptyTables([TBL_CUSTOMERS, TBL_CASHBACK, TBL_CUSTOMER_LOG]);
    }

    public function testChangeToDeleted()
    {
        $customer = Customer::temporary('test1');
        $this->assertFalse($customer->isDeleted());

        $event = $this->customerRepository->changeToDeleted($customer, 'phpunit');

        $this->assertStringContainsString('DELETED', $customer->getEmail());
        $this->assertTrue($customer->isDeleted());
        $this->assertInstanceOf(DeletedEvent::class, $event);
        $this->assertEquals('phpunit', $event->getActionBy());
    }

    /**
     * @dataProvider fixExistingCasesCustomerProvider
     */
    public function testGetCustomersThatNeedsCashBackFix(
        int $count,
        ?string $cashBackType,
        ?string $logType
    )
    {
        $customer = $this->createCustomer($cashBackType);

        if ($logType) {
            $this->createLog($customer, $logType);
            $this->databaseHelper->saveEntity($customer);
        }

        $this->assertEquals($count, iterator_count($this->getCustomers()));
    }

    public function fixExistingCasesCustomerProvider()
    {
        return [
            [1, Customer::CASHBACK_ON_BANK_ACCOUNT, NULL],
            [1, Customer::CASHBACK_ON_BANK_ACCOUNT, CustomerLog::DETAILS_CHANGED],
            [0, NULL, NULL],
            [0, NULL, CustomerLog::CASHBACK_PREF_CREATED],
            [0, Customer::CASHBACK_ON_BANK_ACCOUNT, CustomerLog::CASHBACK_PREF_CREATED],
        ];
    }

    public function testGetCustomersWithEligibleCashBacks()
    {
        //matches eligible cash backs criteria
        $customer1 = $this->createAndPersistCustomer(Customer::CASHBACK_ON_CREDITS);
        $this->createCashBack(Cashback::STATUS_ELIGIBLE, $customer1);

        //doesn't matches eligible cash backs criteria
        $customer2 = $this->createAndPersistCustomer(Customer::CASHBACK_ON_BANK_ACCOUNT);
        $this->createCashBack(Cashback::STATUS_ELIGIBLE, $customer2);

        $customer3 = $this->createAndPersistCustomer(Customer::CASHBACK_ON_CREDITS);
        $this->createCashBack(Cashback::STATUS_PAID, $customer3);

        $customer4 = $this->createAndPersistCustomer();
        $this->createCashBack(Cashback::STATUS_ELIGIBLE, $customer4);

        $this->assertCount(
            1,
            iterator_to_array($this->customerRepository->getCustomersWithEligibleCashBacks(Customer::CASHBACK_ON_CREDITS))
        );
    }

    private function createCustomer(?string $cashBackType): Customer
    {
        $customer = CustomerHelper::createCustomer('<EMAIL>');
        $customer->setCashbackType($cashBackType);
        $this->customerRepository->save($customer);

        return $customer;
    }

    private function createAndPersistCustomer(string $cashBackType = NULL): Customer
    {
        $customer = ObjectHelper::createCustomer(uniqid('email_', true));
        $customer->setCashbackType($cashBackType);

        $this->databaseHelper->saveEntity($customer);

        return $customer;
    }

    private function createLog(Customer $customer, string $customerLog): CustomerLog
    {
        $customerLog = CustomerLogHelper::createLog(
            $customer,
            $customerLog,
            new Date()
        );

        $this->customerLogRepository->saveEntity($customerLog);

        return $customerLog;
    }

    private function getCustomers(): DoctrineIterator
    {
        return $this->customerRepository->getCustomersWithCashBackWithoutCashBackLog();
    }

    private function createCashBack(string $cashBackStatusId, Customer $customer)
    {
        $cashBack = ObjectHelper::createRetailCashback($cashBackStatusId, $customer);

        $this->databaseHelper->saveEntity($cashBack);
    }
}
