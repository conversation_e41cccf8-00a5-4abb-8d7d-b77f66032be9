<?php

namespace EmailModule\Gateways;

use EmailModule\EmailContainer;
use EmailModule\Providers\AttachmentProvider;
use org\bovigo\vfs\vfsStream;
use org\bovigo\vfs\vfsStreamDirectory;
use TestModule\PhpUnit\TestCase;
use Utils\Directory;

class AttachmentProviderTest extends TestCase
{
    /**
     * @var vfsStreamDirectory
     */
    private $root;
    
    public function setupWithMocks()
    {
        $this->root = vfsStream::setup('tmp', NULL, ['file1.pdf' => 'content', 'file2.pdf' => 'content']);
    }

    /**
     * @covers EmailModule\Providers\AttachmentProvider::getAttachments - files
     */
    public function testProvideFileAttachments()
    {
        $email = EmailContainer::fromBasic('<EMAIL>', 'test', 'message', '<EMAIL>');
        $email->addAttachment(vfsStream::url('tmp/file1.pdf'));
        $email->addAttachment(vfsStream::url('tmp/file2.pdf'));

        $this->assertEquals(
            [
                [
                   'filePath' => vfsStream::url('tmp/file1.pdf')
                ],
                [
                    'filePath' => vfsStream::url('tmp/file2.pdf')
                ],
            ],
            AttachmentProvider::getAttachments($email)
        );
    }

    /**
     * @covers EmailModule\Providers\AttachmentProvider::getAttachments - content
     */
    public function testProvideContentAttachments()
    {
        $email = EmailContainer::fromBasic('<EMAIL>', 'test', 'message', '<EMAIL>');
        $email->addAttachment('file3.pdf', 'content');
        $email->addAttachment('file4.pdf', 'content');

        $this->assertEquals(
            [
                [
                    'filename' => 'file3.pdf',
                    'fileContent' => 'content'
                ],
                [
                    'filename' => 'file4.pdf',
                    'fileContent' => 'content'
                ],
            ],
            AttachmentProvider::getAttachments($email)
        );
    }

    /**
     * @covers EmailModule\Providers\AttachmentProvider::getAttachments - files and content
     */
    public function testGetAttachments()
    {
        $email = EmailContainer::fromBasic('<EMAIL>', 'test', 'message', '<EMAIL>');
        $email->addAttachment(vfsStream::url('tmp/file1.pdf'));
        $email->addAttachment(vfsStream::url('tmp/file2.pdf'));
        $email->addAttachment('file3.pdf', 'content');
        $email->addAttachment('file4.pdf', 'content');

        $this->assertEquals(
            [
                [
                    'filePath' => vfsStream::url('tmp/file1.pdf')
                ],
                [
                    'filePath' => vfsStream::url('tmp/file2.pdf')
                ],
                [
                    'filename' => 'file3.pdf',
                    'fileContent' => 'content'
                ],
                [
                    'filename' => 'file4.pdf',
                    'fileContent' => 'content'
                ],
            ],
            AttachmentProvider::getAttachments($email)
        );
    }
}
