<?php

namespace tests\project\BusinessDataModule\Command;

use BusinessDataModule\Commands\SubmitLeadsCommand;
use BusinessDataModule\Entities\FollowUp;
use BusinessDataModule\Entities\Lead as BdgLead;
use BusinessDataModule\Gateways\ApiGateway;
use BusinessDataModule\Helpers\ErrorHandler;
use BusinessDataModule\Repositories\LeadsToProcessCriteriaFactory;
use BusinessDataModule\Requests\LeadRequestFactory;
use BusinessDataModule\Responses\Error\Error;
use BusinessDataModule\Responses\ErrorResponse;
use BusinessDataModule\Responses\LeadResponse;
use BusinessDataModule\Updaters\LeadUpdater;
use BusinessServicesModule\Entities\Lead;
use BusinessServicesModule\Entities\PartnerServicesInformation;
use BusinessServicesModule\Repositories\LeadRepository;
use Entities\Company;
use Entities\Customer;
use Exception;
use HttpClient\Exceptions\RequestException;
use HttpClient\Requests\Request;
use Legacy\Logger\ILogger;
use Psr\Log\LoggerInterface;
use TestModule\Annotations\Inject;
use TestModule\Helpers\DatabaseHelper;
use TestModule\PhpUnit\TestCase;
use tests\helpers\CompanyHouse\IncorporationHelper;
use tests\helpers\EntityHelper;
use Utils\Date;

class SubmitLeadsCommandTest extends TestCase
{
    /**
     * @var Customer
     */
    private $customer;

    /**
     * @var LeadRepository
     */
    private $leadRepository;

    /**
     * @var SubmitLeadsCommand
     */
    private $command;

    /**
     * @var Company
     */
    private $company;

    /**
     * @var array
     */
    private $leads;

    /**
     * @var ApiGateway
     */
    private $gateway;

    /**
     * @var ILogger
     */
    private $logger;

    /**
     * @var LeadRequestFactory
     */
    private $leadRequestFactory;

    /**
     * @var ErrorHandler
     */
    private $errorHandler;

    /**
     * @var DatabaseHelper
     */
    private $databaseHelper;

    /**
     * @Inject({
     *     "leadRepository" = "business_services_module.repositories.lead_repository",
     *     "criteriaFactory" = "business_data_module.repositories.leads_to_process_criteria_factory",
     *     "leadRequestFactory" = "business_data_module.requests.lead_request_factory",
     *     "leadUpdater" = "business_data_module.updaters.lead_updater",
     *     "databaseHelper"="test_module.helpers.database_helper"
     * })
     */
    public function setupDependencies(
        LeadRepository $leadRepository,
        LeadsToProcessCriteriaFactory $criteriaFactory,
        LoggerInterface $logger,
        ApiGateway $gateway,
        LeadRequestFactory $leadRequestFactory,
        LeadUpdater $leadUpdater,
        ErrorHandler $errorHandler,
        DatabaseHelper $databaseHelper
    )
    {
        $this->leadRepository = $leadRepository;
        $this->gateway = $gateway;
        $this->logger = $logger;
        $this->leadRequestFactory = $leadRequestFactory;
        $this->errorHandler = $errorHandler;
        $this->databaseHelper = $databaseHelper;
        $this->command = new SubmitLeadsCommand(
            $leadRepository,
            $criteriaFactory,
            $logger,
            $gateway,
            $leadRequestFactory,
            $leadUpdater,
            $errorHandler
        );

        $this->clearTables();
    }

    public function tearDown(): void
    {
        $this->clearTables();
    }

    public function testRun()
    {
        $this->init();
        $this->command->run(false);
        $this->assertTrue($this->leads[0]->isProcessed());
        $this->assertNotTrue($this->leads[1]->isProcessed());
    }

    public function testRunWithException()
    {
        $this->init();
        $this->leads[1]->setValid(true);
        $this->leadRepository->saveEntity($this->leads[1]);

        $requestData = $this->leadRequestFactory->createRequest($this->leads[0]);
        $requestData2 = $this->leadRequestFactory->createRequest($this->leads[1]);

        $e = new RequestException(new Request('/leads'), new Exception());

        $this->gateway
            ->expects($this->exactly(2))
            ->method('sendLead')
            ->withConsecutive([$this->leads[0], $requestData], [$this->leads[1], $requestData2])
            ->willReturnOnConsecutiveCalls(
                new LeadResponse(200, 'id', 20, 'Received'),
                $this->throwException($e)
            );

        $this->errorHandler->expects($this->once())
            ->method('handleSendLeadError')
            ->with($e, $this->leads[1]);

        $this->command->run(false);
        $this->assertTrue($this->leads[0]->isProcessed());
        $this->assertFalse($this->leads[1]->isProcessed());

    }

    private function clearTables(): void
    {
        EntityHelper::emptyTables(EntityHelper::$tables);
    }

    private function init(): void
    {
        $this->customer = EntityHelper::createCustomer('<EMAIL>');

        $entities = IncorporationHelper::createReadyToBeIncorporated($this->customer, Company::COMPANY_CATEGORY_BYSHR);
        /** @var Company $company */
        $company = $entities['company'];
        $company->setCompanyNumber('10000001');
        $company->setIncorporationDate(new Date('-1 month'));

        $this->databaseHelper->saveEntities($entities);
        $this->databaseHelper->clearEntities();
        $this->company = $this->databaseHelper->find(Company::class, $company->getId());

        $l1 = new Lead($this->company, 14, true);
        $l2 = new Lead($this->company, 14, false);
        $this->leads = [$l1, $l2];
        $this->databaseHelper->saveEntities($this->leads);

        $bdgLead1 = new BdgLead(
            $l1,
            'advert-id',
            'advertiser',
            new FollowUp('type', 'value'),
            true,
            true
        );
        $this->databaseHelper->saveEntity($bdgLead1);
        $bdgLead2 = new BdgLead(
            $l2,
            'advert-id',
            'advertiser',
            new FollowUp('type', 'value'),
            true,
            true
        );
        $this->databaseHelper->saveEntity($bdgLead2);

        $info = new PartnerServicesInformation();
        $info->setLead($l1);
        $info->setFirstName('Tester');
        $l1->setPartnerServicesInformation($info);

        $info = new PartnerServicesInformation();
        $info->setLead($l2);
        $info->setFirstName('Tester');
        $l2->setPartnerServicesInformation($info);

        $l1->setBdgLead($bdgLead1);
        $l2->setBdgLead($bdgLead2);
        $this->databaseHelper->saveEntities($this->leads);
    }
}
