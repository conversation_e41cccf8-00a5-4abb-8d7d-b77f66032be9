<?php

namespace tests\helpers;

use CustomerModule\Entities\BusinessInformation;
use CustomerModule\Entities\CompanyInformation;
use CustomerModule\Entities\InvoiceAddress;
use Doctrine\DBAL\Exception as DBALException;
use Entities\Customer;
use Exception;
use FormModule\Dto\Address;
use TestModule\Helpers\DatabaseHelper;
use Utils\Date;

final class CustomerHelper
{
    /**
     * @param DatabaseHelper $databaseHelper
     * @throws DBALException
     */
    public static function clearRelatedTables(DatabaseHelper $databaseHelper)
    {
        $databaseHelper->emptyTables([TBL_CUSTOMERS, TBL_CUSTOMER_ADDRESS, TBL_CUSTOMER_BUSINESS_INFO]);
    }

    /**
     * @param string $email
     * @return Customer
     * @throws Exception
     */
    public static function createCustomer($email = NULL)
    {
        return ObjectHelper::createCustomer($email);
    }

    /**
     * @param string $email
     * @return Customer
     * @throws Exception
     */
    public static function createCustomerWithInvoice($email = NULL)
    {
        $customer = ObjectHelper::createCustomer($email);

        $address = Address::createDefault();
        $address->setRecipientName('Invoice Name');
        $address->setAddress1('125 St Johns');
        $address->setCity('London');
        $address->setCountryIso('GB');
        $address->setPostcode('KW1 4YT');
        $address = InvoiceAddress::fromAddress($address);
        $customer->setInvoiceAddress($address);
        $customer->setDateOfBirth(new Date('2000-01-01'));
        return $customer;
    }

    /**
     * @throws Exception
     */
    public static function createRegulatedBodyCustomer(string $email = null)
    {
        $customer = self::createCustomer($email);
        $businessInformation = new BusinessInformation(
            $customer,
            BusinessInformation::PROFESSIONAL_COMPANY_FORMATION,
            CompanyInformation::fromRegulatedBody(
                true,
                'regulated body',
                12345678
            )
        );
        $customer->setBusinessInformation($businessInformation);
        return $customer;
    }
}