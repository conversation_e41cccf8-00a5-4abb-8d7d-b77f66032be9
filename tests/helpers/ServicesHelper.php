<?php

namespace tests\helpers;

use Entities\Order;
use Entities\Service;
use Entities\OrderItem;
use Models\Products\Product;
use Models\Products\Package;
use Entities\Customer;
use Entities\Company;
use DateTime;

class ServicesHelper
{
    /**
     * @param string $type
     * @return Service
     */
    public static function getService($type = Service::TYPE_REGISTERED_OFFICE, $package = FALSE, Company $company = NULL)
    {
        $company =  $company ? $company : new Company(new Customer('test', 'test'), 'test');
        $product = $package ? new Package(0) : new Product(0);
        
        /** @noinspection PhpUnhandledExceptionInspection */
        $product->setNodeName('service_'.random_int(1,100));
        
        $order = ObjectHelper::createOrder($company->getCustomer());
        $service = new Service(
            $type,
            $product,
            $company,
            $order->getItems()->first()
        );
        $company->addService($service);
        $service->setStateId(Service::STATE_ENABLED);
        return $service;
    }

    public static function createService(OrderItem $orderItem, $type, $service, Company $company = NULL, $parent = NULL, $productRenewal = NULL)
    {
        $service = new Service(
            $type,
            $service,
            $company,
            $orderItem
        );

        $service->setRenewalProduct($productRenewal);
        $company->addService($service);

        if ($parent) {
            $service->setParent($parent);
        }

        $service->setStateId(Service::STATE_ENABLED);
        return $service;
    }

    /**
     * @return Service
     */
    public static function getOverDueService()
    {
        $service = self::getService();
        $service->setDtStart(new DateTime('-1 year'));
        $service->setDtExpires(new DateTime('-1 day'));
        return $service;
    }

    public static function getActiveService(Company $company = NULL, string $serviceType = Service::TYPE_REGISTERED_OFFICE): Service
    {
        $service = self::getService($serviceType, FALSE, $company);
        $service->setDtStart(new DateTime('-1 day'));
        $service->setDtExpires(new DateTime('+1 day'));
        return $service;
    }


    public static function getActiveForCompany(Company $company, string $serviceType = Service::TYPE_REGISTERED_OFFICE): Service
    {
        $service = self::getActiveService($company, $serviceType);
        return $service;
    }

    /**
     * @param string $type
     * @param bool $isPackage
     * @return Service
     */
    public static function getActiveServiceWithType($type = Service::TYPE_REGISTERED_OFFICE, $isPackage = FALSE)
    {
        $service = self::getService($type, $isPackage);
        $service->setDtStart(new DateTime('-1 day'));
        $service->setDtExpires(new DateTime('+1 day'));
        return $service;
    }
}
