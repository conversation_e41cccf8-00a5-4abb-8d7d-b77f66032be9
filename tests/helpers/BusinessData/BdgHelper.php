<?php

namespace tests\helpers\BusinessData;

use BusinessServicesModule\Entities\Events\EmailEvent;
use BusinessServicesModule\Entities\Lead;
use Doctrine\ORM\OptimisticLockException;
use Framework\FEmail;
use LoggableModule\Entities\EmailLog;
use TestModule\Helpers\DatabaseHelper;

final class BdgHelper
{
    /**
     * @throws OptimisticLockException
     */
    public static function addEmailEventEvent(
        DatabaseHelper $databaseHelper,
        Lead $lead,
        string $eventKey,
        Femail $email
    ): void
    {
        $emailLog = EmailLog::fromEmailNode($email, $lead->getCompany()->getCustomer());
        $emailEvent = new EmailEvent($lead, $emailLog, $eventKey);
        $databaseHelper->saveEntities([$emailLog, $emailEvent]);
    }
}