import {from, Observable, of} from "rxjs";
import axios from "axios";
import {
    catchError, map,
    share,
    switchMap,
    throttleTime,
    withLatestFrom
} from "rxjs/operators";
import type { ValidationStatus } from "@/modules/idcheck/types/validationStatus";
import { CredasPersonalDetails } from "@/lib/dto";
import { CredasPersonalDetailsToFormData } from "@/lib/utils";
import { createFailure, mapResponse } from "@/modules/idcheck/observables/validationStatus";

const axiosConfig = {headers: {'Content-Type': 'multipart/form-data'}};

const createFormData = (pd?: CredasPersonalDetails): FormData => pd ? CredasPersonalDetailsToFormData(pd) : new FormData()

export default function (email$: Observable<string>, url$: Observable<{ url: string, pd?: CredasPersonalDetails }>): Observable<ValidationStatus> {
    return email$.pipe(
        throttleTime(30000),
        withLatestFrom(url$),
        switchMap(
            ([email, {url, pd}]) => from(axios.post(url, createFormData(pd), axiosConfig)).pipe(
                map(mapResponse),
                catchError(
                    e => of(createFailure(
                        "<span class=\"red\">Email failed!</span>",
                        "fa fa-envelope-o fa-3x red",
                        e.message,
                        true)
                    ),
                )
            ),
        ),
        share()
    );
}
