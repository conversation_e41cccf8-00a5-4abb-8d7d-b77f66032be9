<template>
    <div :class="slug" class="ghost-wrapper" :id="'ghost-'+ slug">
        <div class="ghost-content" v-if="haveContent(slug) && renderable !== ''" v-html="renderable"></div>
        <div class="ghost-content-loader text-center" v-else>
            <i class="fas fa-spinner fa-spin fa-3x"></i>
        </div>
    </div>
</template>

<script lang="ts">
    import Vue from "vue";
    import GhostCMS from '../../services/ghostApi';

    const BASE_URL = 'https://cms.companiesmadesimple.com/';

    export default Vue.extend({
        props: {
            slug: {
                type: String,
                required: false,
                default: ''
            },
        },
        data: function () {
            return {
                renderable: '',
                loading: false
            };
        },
        methods: {
            async content(slug) {
                GhostCMS.content(slug)
                    .then((res) => {
                        this.renderable = res;
                    })
                    .catch((res) => {
                        this.renderable = res;
                        return false;
                    })
                    .finally(() => {
                        this.loading = false;
                    });
                return false;
            },
            async haveContent(slug) {
                await this.content(slug);
                return this.renderable === '';
            }
        },
        mounted() {
            let comment = document.createComment(`To update this content go to: ${BASE_URL + this.slug}/`);
            document.getElementById(`ghost-${this.slug}`).appendChild(comment);
        }
    })
</script>
