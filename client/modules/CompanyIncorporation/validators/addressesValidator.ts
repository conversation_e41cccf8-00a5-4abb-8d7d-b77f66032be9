import { isCountryUk, isPostCodeValid } from "../helpers/addressHelper";
import {
  englishKeyboardValidator,
  findInvalidChar
} from "@/modules/CompanyIncorporation/validators/englishKeyboardValidator";

interface Address {
  premise?: string;
  street?: string;
  thoroughfare?: string;
  postTown?: string;
  postcode?: string;
  county?: string;
  country?: string;
}

export class AddressValidator {
  address: Address;
  errors: Record<string, string> = {};

  constructor(address: Address) {
    this.address = address;
  }

  validate(): boolean {
    this.errors = {};
    this.validateRequiredFields();
    this.validatePostcode();
    this.validateEnglishKeyboard();
    return Object.keys(this.errors).length === 0;
  }

  private addError(field: string, message: string) {
    this.errors[field] = message;
  }

  private validateRequiredFields() {
    const { premise, street, postTown, postcode, country } = this.address;

    if (!premise?.trim()) {
      this.addError("premise", "Premise is required.");
    }

    if (!street?.trim()) {
      this.addError("street", "Street is required.");
    }

    if (!postTown?.trim()) {
      this.addError("postTown", "Post Town is required.");
    }

    if (!postcode?.trim()) {
      this.addError("postcode", "Postcode is required.");
    }

    if (!country?.trim()) {
      this.addError("country", "Country is required.");
    }
  }

  private validatePostcode() {
    const { postcode, country } = this.address;

    if (country && isCountryUk(country) && postcode) {
      if (!isPostCodeValid(postcode)) {
        this.addError("postcode", "Postcode is invalid.");
      }
    }
  }

  private validateEnglishKeyboard() {
    for (let key in this.address) {
      if (this.address.hasOwnProperty(key)) {
        if (!englishKeyboardValidator(this.address[key])) {
          this.addError(key, `Invalid character (${findInvalidChar(this.address[key])}). Please only use characters found on an English keyboard`)
        }
      }
    }
  }
}
