<template>
    <div>
        <div class="input-group has-search position-relative" id="filter-wrapper">
            <span class="fa fa-search form-control-feedback"></span>
            <input
                id="member-details-prefill-search"
                class="form-control search-input rounded-5"
                type="text"
                placeholder="Enter an officer’s name"
                v-model="searchTerm"
                @keyup="triggerSearch"
            />
            <span id="clear-search" class="input-group-text bg-transparent border-0 ms-0">
                <a href="javascript:;" id="clearSearchButton" @click="clearSearch">
                    <span class="fw-semibold text-decoration-underline cms-link">Clear Search</span>
                </a>
            </span>
        </div>
        <div v-if="members.length > 0" class="row mt-1 position-relative">
            <div class="col-12 prefill-members-details position-absolute">
                <div class="col-12 mt-2 mb-1" v-for="(member, index) in members" :key="index">
                    <div class="fw-normal my-1" @click="setPrefill(index)">
                        {{ member.forename }} {{ member.surname }}
                    </div>
                </div>
            </div>
        </div>
        <div v-else-if="isLoading" class="row mt-1 position-relative">
            <div class="col-12 prefill-members-details position-absolute text-center py-1 text-muted">
                Loading...
            </div>
        </div>
        <div v-else-if="isCompleted" class="row mt-1 position-relative">
            <div class="col-12 prefill-members-details position-absolute text-center py-1 text-muted">
                No results found
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import { defineComponent, ref, watch } from 'vue';
import api from "@/modules/CompanyIncorporation/Api";

interface MemberDetails {
    forename: string;
    surname: string;
    middle_name: string|null;
    corporate_name: string|null;
    dob: string|null;
    nationality: string|null;
    occupation: string|null;
    country_of_residence: string|null;
    identification_type: string|null;
    place_registered: string|null;
    registration_number: string|null;
    law_governed: string|null;
    legal_form: string|null;
    country_or_state: string|null;
}

export default defineComponent({
    props: {
        companyId: {
            type: Number,
            required: true
        },
        isCorporate: {
            type: Boolean,
            required: true
        }
    },
    setup(props, { emit }) {
        const searchTerm = ref<string>('');
        const members = ref<MemberDetails[]>([]);
        const isLoading = ref<boolean>(false);
        const isCompleted = ref<boolean>(false);
        const error = ref<string>('');
        const searchInput = ref<HTMLInputElement | null>(null);
        const selectedMember = ref<MemberDetails | null>(null);

        const debounce = (func: Function, wait: number) => {
            let timeout: number;
            const debounced = function (...args: any) {
                const context = this;
                clearTimeout(timeout);
                timeout = window.setTimeout(() => {
                    func.apply(context, args);
                }, wait);
            };
            debounced.cancel = () => {
                clearTimeout(timeout);
            };
            return debounced;
        };

        const debouncedSearch = debounce(async (name: string) => {
            if (!name.trim()) {
                members.value = [];
                isLoading.value = false;
                error.value = '';
                return;
            }

            const formData = new FormData();
            formData.append("memberName", name);
            formData.append("isCorporate", props.isCorporate);
            await api.post<MemberDetails[]>(`/api/incorporation/${props.companyId}/appointments/get_prefill_member_details_from_customer_other_companies/`, formData)
            .then((response) => {
                members.value = JSON.parse(response.data['membersDetails']);
            })
            .catch ((error) => {
                //
            })
            .finally(() => {
                isCompleted.value = true;
                isLoading.value = false;
            });

        }, 600);

        watch(searchTerm, (newVal) => {
            debouncedSearch(newVal);
        });

        const triggerSearch = () => {
            debouncedSearch.cancel();
            isLoading.value = true;
            isCompleted.value = false;
            debouncedSearch(searchTerm.value);
        };

        const setPrefill = (index: number) => {
            selectedMember.value = members.value[index];
            clearSearch();
            emit('setPrefillMemberDetails', selectedMember);
        };

        const clearSearch = () => {
            searchTerm.value = '';
            members.value = [];
            error.value = '';
            isLoading.value = false;
            isCompleted.value = false;
            debouncedSearch.cancel();
            document.getElementById('member-details-prefill-search').focus();
        };

        return {
            searchTerm,
            members,
            isLoading,
            error,
            clearSearch,
            triggerSearch,
            searchInput,
            selectedMember,
            setPrefill,
            isCompleted
        };
    },
    emits: ['setPrefillMemberDetails'],
});
</script>

<style scoped>
    .prefill-members-details {
        width: calc(100% - 135px);
        margin-left: 15px;
        position: absolute;
        height: min-content;
        background-color: white;
        border: 1px solid #e5e5e5;
        border-radius: 5px;
        box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.2);
        z-index: 99999999999;
    }
    .prefill-members-details > div {
        cursor: pointer;
    }
    .prefill-members-details > div:not(:last-child) {
        border-bottom: 1px solid #e5e5e5;
    }

</style>