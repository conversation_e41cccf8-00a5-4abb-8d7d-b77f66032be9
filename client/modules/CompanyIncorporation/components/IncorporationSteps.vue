<template>
  <div class="row p-0">
    <div class="col-12">
      <ul class="nav d-flex flex-nowrap gap-4 align-items-end nip-step-list">
        <template v-for="step in steps">
          <li class="nav-item p-0 step-item">
            <a
              class="float-start p-0 nav-link mb-2 "
              :class="step.title === currentStep ? 'fw-bold nip-step-link-active ' : step.enabled ? 'fw-normal nip-step-link-active' : 'fw-normal nip-step-link-inactive'"
              :href="step.link"
              :id="getLinkId(step.title)"
            >
              {{ step.title }}
            </a>
            <hr
              :class="step.valid ? 'nip-step-border-active' : 'nip-step-border-inactive'"
              class="border border-3 mt-0 ms-0 me-5 w-100"
            />
          </li>
        </template>
        </ul>
    </div>
  </div>
</template>

<script>

import Vue from "vue";
export default Vue.extend({
    props: {
        steps: Object,
        currentStep: String,
    },
    methods: {
        isStepCompleted(stepTitle) {
            for (let step in this.steps) {
                if (this.steps[step].valid) {
                    return true;
                }
            }
            return false;
        },
        getLinkId(title) {
            return `breadcrumb_${this.snakeCase(title)}_link`
        },
        snakeCase(string)
        {
            return string.replace(/\W+/g, " ")
                .split(/ |\B(?=[A-Z])/)
                .map(word => word.toLowerCase())
                .join('_');
        },
    }
});
</script>