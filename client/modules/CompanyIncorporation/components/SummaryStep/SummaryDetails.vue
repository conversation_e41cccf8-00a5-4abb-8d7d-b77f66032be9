<template>
    <div class="my-3">
        <div class="card nip-inner-card-board">
            <div class="p-3">
                <div class="row g-0">
                    <div class="p-4 col-12">
                        <span class="fw-semibold fs-3 mb-4 text-black">Summary</span>

<!--                        <summary-warning-->
<!--                            :warning-view="companyDetails.warningView"-->
<!--                            :entities-with-id-invalid="entitiesWithIdInvalid"-->
<!--                            :entity-id-checks-url="entityIdChecksUrl"-->
<!--                            :company-name-url="companyNameUrl"-->
<!--                        />-->

                        <div class="card nip-inner-card-board mt-3">
                            <div class="card-body p-0">
                                <div class="row g-0">
                                    <div class="px-4 pt-3 pb-4 col-12">
                                        <p>
                                            <span class="fw-semibold fs-5 card-title me-4">Company Name</span>
                                            <a :href="companyNameUrl" id="change_company_name_link" class="nip-link">Change</a>
                                        </p>

                                        <div class="mt-4 mb-3">
                                        <span class="nip-text-color">
                                            Our system attempts to detect if you have added LTD or LIMITED to your name and if it does not think you have, it will append LTD to the name. Please check below as this is exactly how your name will be sent to Companies House.
                                        </span>
                                        </div>

                                        <div class="row">
                                            <div class="col-sm-4 fw-semibold">Company Name</div>
                                            <div class="col-sm-6 col-12 nip-text-color">
                                                {{ companyDetails.companyInformation.companyName }}
                                            </div>
                                        </div>

                                        <hr class="mt-4 mb-3"/>

                                        <p class="mt-3">
                                            <span class="fw-semibold fs-5 card-title me-4">Company Details</span>
                                            <a :href="companyFormationUrl" id="change_company_details_link"
                                               class="nip-link">Change</a>
                                        </p>
                                        <div class="row mb-3">
                                            <div class="col-sm-4 fw-semibold">Company Type</div>
                                            <div class="col-sm-6 col-12 nip-text-color">
                                                {{ companyDetails.companyInformation.companyType }}
                                            </div>
                                        </div>
                                        <div class="row mb-3">
                                            <div class="col-sm-4 fw-semibold">Registered Office Address</div>
                                            <div class="col-sm-6 col-12 nip-text-color">
                                                {{ companyDetails.registeredOffice.address.premise }} <br/>
                                                {{ companyDetails.registeredOffice.address.street }} <br/>
                                                <template v-if="companyDetails.registeredOffice.address.thoroughfare">{{ companyDetails.registeredOffice.address.thoroughfare }} <br/></template>
                                                {{ companyDetails.registeredOffice.address.postTown }} <br/>
                                                {{ companyDetails.registeredOffice.address.postcode }} <br/>
                                                <template v-if="companyDetails.registeredOffice.address.county">{{ companyDetails.registeredOffice.address.county }} <br/></template>
                                            </div>
                                        </div>
                                        <div class="row mb-3">
                                            <div class="col-sm-4 fw-semibold">Country of Registration</div>
                                            <div class="col-sm-6 col-12 nip-text-color">
                                                {{ getCountry(companyDetails.registeredOffice.address.country) }}
                                            </div>
                                        </div>
                                        <div class="row mb-3" v-if="!isLlpType">
                                            <div class="col-sm-4 fw-semibold">Nature of Business (SIC Codes)</div>
                                            <div class="col-sm-6 col-12 nip-text-color">
                                                <p v-for="sicCode in companyDetails.companyInformation.sicCodes">
                                                    {{ sicCode.Code }} - {{ sicCode.Description }}
                                                </p>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-4 fw-semibold">Registered Email Address</div>
                                            <div class="col-sm-6 col-12 nip-text-color">
                                                {{ companyDetails.companyInformation.registeredEmailAddress }}
                                            </div>
                                        </div>

                                        <template v-if="companyDetails.companyInformation.capitals">
                                          <hr class="mt-4 mb-3"/>
                                          <p class="mt-3">
                                            <span class="fw-semibold fs-5 card-title me-4">Company Capitals</span>
                                          </p>

                                          <div class="row mt-2" v-for="capital in companyDetails.companyInformation.capitals">
                                            <div class="col-sm-4 fw-semibold">Capital</div>
                                            <div class="col-sm-6 col-12 nip-text-color">
                                              <span class="fw-semibold">{{ capital.numberOfIssuedShares }} total shares issued with an aggregated value of {{ capital.totalAggregateNominalValue }} {{ capital.currency }}</span>
                                              <ul>
                                              <template v-for="share in capital.shares">
                                                <li>
                                                <small>
                                                  {{ share.numShares }} shares of {{ share.shareClass }} class with an aggregated value of {{ share.aggregateNominalValue }} {{ capital.currency }}
                                                  <br><small>({{ share.prescribedParticulars }})</small>
                                                </small>
                                                </li>
                                              </template>
                                              </ul>
                                            </div>
                                          </div>
                                        </template>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card nip-inner-card-board mt-4">
                            <div class="card-body p-0">
                                <div class="row g-0">
                                    <div class="px-4 pt-3 pb-4 col-12">
                                        <div class="d-flex justify-content-between mt-3">
                                            <span class="fw-semibold fs-5 card-title me-4">Appointments</span>
                                            <a :href="appointmentsUrl" id="appointments_link" class="nip-link">Add another appointment</a>
                                        </div>
                                        <appointment-details
                                            v-for="(member, index) in members"
                                            :key="index"
                                            :member="member"
                                            :appointmentsUrl="appointmentsUrl"
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div v-if="!isLlpType || hasReservedWord || companyDetails.warningView.reservedWords?.length > 0" class="card nip-inner-card-board mt-4">
                            <div class="card-body p-0">
                                <div class="row g-0">
                                    <div class="px-4 pt-3 pb-4 col-12">

                                        <p>
                                            <span class="fw-semibold fs-5 card-title me-4">Articles of Association</span>
                                            <a :href="memorandumAndArticlesUrl" id="maa_link" class="nip-link">Change</a>
                                        </p>

                                        <p v-if="!isLlpType" class="nip-text-color">
                                            This document is mandatory for all companies.
                                            To make the process easier, we have provided a default template for your use.
                                        </p>

                                        <p class="nip-text-color">
                                            Need to upload a custom article or supporting document?
                                            <a :href="memorandumAndArticlesUrl" id="article_upload_link" class="nip-link">Click here</a>
                                        </p>

                                        <div class="row" v-if="companyDetails.articlesView.memorandumAndArticles?.filename">
                                            <div class="col-sm-4 fw-semibold">Article</div>
                                            <div class="col-sm-6 col-12">
                                                <a
                                                    id="article_download_link" class="nip-link"
                                                    @click="downloadArticle(companyDetails.articlesView.memorandumAndArticles?.id)"
                                                >
                                                    {{ companyDetails.articlesView.memorandumAndArticles?.filename }}
                                                </a>
                                            </div>
                                        </div>

                                        <div class="row mt-2" v-if="companyDetails.articlesView.supportingDocument?.filename">
                                            <div class="col-sm-4 fw-semibold">Support Document</div>
                                            <div class="col-sm-6 col-12">
                                                <a
                                                    id="support_doc_download_link" class="nip-link"
                                                    @click="downloadArticle(companyDetails.articlesView.supportingDocument?.id)"
                                                >
                                                    {{ companyDetails.articlesView.supportingDocument?.filename }}
                                                </a>
                                            </div>
                                        </div>

                                        <div v-if="hasMultipleShareClasses" class="alert alert-primary mb-0 mt-3" role="alert">
                                            <h4 class="alert-heading">Multiple share classes detected</h4>
                                            <p>
                                                The Model Articles provided by Companies House are not sufficient when forming a company
                                                with multiple share classes.
                                                We have adopted a set of Articles which still rely on the solid base of the Model
                                                Articles but with added
                                                clauses to handle multiple share classes.
                                            </p>
                                            <p>
                                                We have also included clause 2.13 which is intended to preserve your Entrepreneurs'
                                                Relief entitlement
                                                (you will pay less tax when you sell all or part of your company).
                                                <a href="https://support.companiesmadesimple.com/hc/en-us/articles/360020751353-Article-2-13-and-Entrepreneurs-Relief-Multiple-Share-Classes-"
                                                   target="_blank"
                                                   id="213_learn_more_link">
                                                    Learn More
                                                </a>
                                            </p>
                                            <p class="my-2">
                                                <a href="https://companiesmadesimple.com/static/multiple_share_classes_model_articles.pdf"
                                                   target="_blank" id="share_classes_link">
                                                    Download and view the Articles
                                                </a>
                                                that will be used.
                                            </p>
                                            <p>
                                                Proceed with the incorporation if you want to use these Articles.
                                                Otherwise, <a :href="memorandumAndArticlesUrl" id="maa_link2">click here</a> to upload
                                                your custom one.
                                            </p>
                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card nip-inner-card-board mt-4">
                            <div class="card-body p-0">
                                <div class="row g-0">
                                    <div class="px-4 pt-3 pb-4 col-12">

                                        <p class="mt-3">
                                            <span class="fw-semibold fs-4 card-title">Request History</span>
                                        </p>
                                        <div class="row">
                                            <div class="col-sm-1 fw-semibold">ID</div>
                                            <div class="col-sm-6 fw-semibold">Type</div>
                                            <div class="col-sm-2 fw-semibold">Status</div>
                                            <div class="col-sm-2 fw-semibold">Date</div>
                                            <div class="col-sm-1 fw-semibold">Action</div>
                                        </div>
                                        <div v-for="history in companyDetails.requestsHistoryView" class="row">
                                            <div class="col-sm-1">{{ history.id }}</div>
                                            <div class="col-sm-6">{{ history.type }}</div>
                                            <div class="col-sm-2">{{ history.status }}</div>
                                            <div class="col-sm-2">{{ parseDate(history.date.date) }}</div>
                                            <div class="col-sm-1"><a :href="requestHistoryUrl(history.id)"
                                                                     :id="'view_history_'+history.id"
                                                                     class="link-dark text-decoration-underline">view</a></div>
                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>

                        <RegistrationReview v-if="companyDetails.registrationReviewView.shouldShowSummaryReview
                                                    && !companyDetails.registrationReviewView.hasRegistrationReviewAlready"
                                            :product-registration-review="productRegistrationReview"/>

                        <CHExtraFeeCharge v-if="companyDetails.chIncorporationExtraFeeView.isExtraFeeChargeApplicable"
                                          :product-c-h-extra-fee="productCHExtraFee"/>

                        <summary-warning
                            :warning-view="companyDetails.warningView"
                            :entities-with-id-invalid="entitiesWithIdInvalid"
                            :entity-id-checks-url="entityIdChecksUrl"
                            :company-name-url="companyNameUrl"
                        />

                      <div class="d-flex mt-4" v-if="showSubmitButton">
                            <input
                                type="checkbox"
                                class="nip-checkbox"
                                value="1"
                                id="lawfulPurposeStatement"
                                name="lawfulPurposeStatement"
                                v-model="isLawfulPurposeStatement"
                            />
                            <label for="lawfulPurposeStatement"
                                   class="fw-semibold ml-2">I confirm that the intended future activities of the company are lawful
                            </label>
                        </div>

                        <div class="row mt-3 mt-1">
                            <div class="col-md-3 col-12 mb-1">
                                <button type="button"
                                        v-if="showSubmitButton"
                                        id="summary_submit_button_18485949"
                                        @click="onSubmit"
                                        class="btn nip-btn-orange nip-next-step-button d-flex align-items-center"
                                        :disabled="!canSubmitIncorporation">
                                        Submit & Continue
                                    <span class="arrow">
                                        <span class="arrow-shaft arrow-shaft-color-override-white"></span>
                                        <span class="arrow-head arrow-head-color-override-white"></span>
                                    </span>
                                </button>
                            </div>
                        </div>
                        <div class="alert alert-danger" v-if="errorMessage">
                            {{ errorMessage }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import {defineComponent} from "vue";
import AppointmentDetails from "@/modules/CompanyIncorporation/components/SummaryStep/AppointmentDetails.vue";
import SummaryWarning from "@/modules/CompanyIncorporation/components/SummaryStep/SummaryWarning.vue";
import CHExtraFeeCharge from "@/modules/CompanyIncorporation/components/SummaryStep/CHExtraFeeCharge.vue";
import RegistrationReview from "@/modules/CompanyIncorporation/components/SummaryStep/RegistrationReview.vue";
import api from "@/modules/CompanyIncorporation/Api";
import {downloadArticleEndpoint} from "@/modules/CompanyIncorporation/helpers/memorandumAndArticlesUrlHelper";
import summaryWarning from "@/modules/CompanyIncorporation/components/SummaryStep/SummaryWarning.vue";

const countries = {
    'GB-ENG': 'England and Wales',
    'GB-WLS': 'England and Wales',
    'GB-SCT': 'Scotland',
    'GB-NIR': 'Northern Ireland'
}

export default defineComponent({
    components: {AppointmentDetails, SummaryWarning, CHExtraFeeCharge, RegistrationReview},
    props: {
        company: {
            type: Object,
            required: true
        },
        companyDetails: {
            type: Object,
            required: true
        },
        members: {
            type: Object,
            required: true
        },
        companyNameUrl: {
            type: String,
            required: true
        },
        entitiesWithIdInvalid: {
            type: Array,
            required: true
        },
        entityIdChecksUrl: {
            type: String,
            required: true
        },
        hasMultipleShareClasses: {
            type: Boolean,
            required: true,
        },
        productCHExtraFee: {
            type: Object,
            required: true
        },
        productRegistrationReview: {
            type: Object,
            required: true
        },
        hasReservedWord: {
            type: Boolean,
            required: true
        },
    },
    data() {
        return {
            hasRegistrationReview: false,
            canSubmitIncorporation: true,
            errorMessage: '',
            isLawfulPurposeStatement: '',
        }
    },
    methods: {
        getCountry(country) {
            return countries[country]
        },
        parseDate(date) {
            try {
                return date.slice(0, 10).split('-').reverse().join('/')
            } catch (e) {
                return ''
            }
        },
        requestHistoryUrl(historyId) {
            return `/page203en.html?company_id=${this.company.id}&request_id=${historyId}`
        },
        listenForInlinePaymentEvent() {
            document.addEventListener('inline-payment-succeeded', (event) => {
                if (event.detail.productIdList.includes(this.productCHExtraFee.productId)) {
                    this.canSubmitIncorporation = true
                }

                if (event.detail.productIdList.includes(this.productRegistrationReview.productId)) {
                    this.hasRegistrationReview = true
                }
            })
        },
        onSubmit() {
            if (confirm('Are you sure?')) {
                this.errorMessage = ''

                if (!this.isLawfulPurposeStatement) {
                    this.errorMessage = 'Please confirm that the intended future activities of the company are lawful.';
                    return
                }

                const formData = new FormData();
                formData.append("isLawfulPurposeStatement", this.isLawfulPurposeStatement)

                api.post(this.submitUrl, formData)
                    .then(() => {
                        return window.location.href = `/exclusive-offers/?companyId=${this.company.id}`
                    })
                    .catch(error => {
                        if (error.response && error.response.data && error.response.data.error) {
                            this.errorMessage = error.response.data.error;
                            return;
                        }

                        this.errorMessage = error.message;
                    })
            }
        },
        async downloadArticle(articleId: string) {
            try {
              window.open(downloadArticleEndpoint(this.company.id, articleId), '_blank');
            } catch (error) {
                console.error('Failed to open the PDF:', error);
                this.errorMessage = 'Failed to open the PDF.';
            }
        },
    },
    computed: {
        summaryWarning() {
            return summaryWarning
        },
        memorandumAndArticlesUrl() {
            return `/incorporation/${this.company.id}/memorandum_and_articles/`
        },
        submitIncorporationUrl() {
            return `/api/incorporation/summary/submit_incorporation/${this.company.id}/`
        },
        submitForReviewUrl() {
            return `/api/incorporation/summary/submit_review/${this.company.id}/`
        },
        appointmentsUrl() {
            return `/incorporation/${this.company.id}/appointments/`
        },
        companyFormationUrl() {
            return `/incorporation/${this.company.id}/company_formation/`
        },
        submitUrl() {
            return this.hasRegistrationReview ? this.submitForReviewUrl : this.submitIncorporationUrl
        },
        showSubmitButton() {
            const warningView = this.companyDetails.warningView
            return !warningView.hasDefaultCompanyName
                && !warningView.isSubmissionPending
                && !warningView.hasPostcodeConflicts
        },
        isLlpType() {
            return this.companyDetails.companyInformation.isLlpType
        },
    },
    mounted() {
        this.hasRegistrationReview = this.companyDetails.registrationReviewView.hasRegistrationReviewAlready
        this.canSubmitIncorporation = !this.companyDetails.chIncorporationExtraFeeView.isExtraFeeChargeApplicable
        this.isLawfulPurposeStatement = this.companyDetails.companyInformation.isLawfulPurposeStatement
        this.listenForInlinePaymentEvent()
    }
})
</script>
