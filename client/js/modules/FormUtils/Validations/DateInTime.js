// @flow
import * as FormHelper from '../helpers';
import $ from 'jquery';
import { isNumeric } from '../../Utils/NumberHelper';

export class DateInTime {
  static validDate (yearStr: string, monthStr: string, dayStr: string): boolean {
    if (!isNumeric(yearStr) || !isNumeric(monthStr) || !isNumeric(dayStr)) {
      return false;
    }
    const year = parseInt(yearStr);
    const month = parseInt(monthStr);
    const day = parseInt(dayStr);
    const testYear = new Date();
    if (!(year >= testYear.getFullYear() - 150)) {
      return false;
    }
    if (!(month >= 1 && month <= 12)) {
      return false;
    }
    const testDay = new Date(year, month, 0);
    if (isNaN(testDay.valueOf()) || !(day >= 1 && day <= testDay.getDate())) {
      return false;
    }
    const date = new Date(year, month, day);
    return !isNaN(date.valueOf());
  }

  static dateValidation (date: {year: string, month: string, day: string}, form: string, mainElement: string): Object {
    const events = 'change.dateintime.' + mainElement.replace(/[^0-9a-z]/gi, '');
    const elements = [date.year, date.month, date.day];
    // we want to validate when additional elements change
    $(FormHelper.namesToQuery(elements), form).not(mainElement).off(events).on(events, function () {
      if (FormHelper.hasValues(elements)) {
        FormHelper.revalidateElement($(form).data('validator'), mainElement);
      }
    });
    return {
      depends: function (element: string) {
        return FormHelper.hasValues(elements);
      },
      param: {
        getValues: function (): ?string[] {
          if (!FormHelper.hasValues(elements)) {
            return null;
          }
          return FormHelper.elementValues(elements);
        }
      }
    };
  }
}
