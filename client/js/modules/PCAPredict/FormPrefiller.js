import $ from 'jquery';

export class FormPrefiller {
  static prefill (data, context) {
    for (const key in data) {
      if (key in data) {
        if (key !== 'country') {
          $('[data-pca-form="' + key + '"]', context).val(data[key]);
        } else {
          const value = this.mapUkCountry(data[key]);
          $('[data-pca-form="' + key + '"]', context).val(value);
        }
      }
    }
  }

  static clean (context) {
    $('[data-pca-form]', context).val('');
  }

  static mapUkCountry = (country) => {
    switch (country) {
      case 'England':
        return 'GB-ENG';
      case 'Scotland':
        return 'GB-SCT';
      case 'Wales':
        return 'GB-WLS';
      case 'Northern Ireland':
        return 'GB-NIR';
      default:
        return 'GBR'; // United Kingdom
    }
  }
}
