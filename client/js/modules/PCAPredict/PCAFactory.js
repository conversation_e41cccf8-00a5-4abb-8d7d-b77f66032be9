import $ from 'jquery';
import { pca } from '../external';

export class PCAFactory {
  static createUk (key, context) {
    const element = $('[data-pca="search-input"]', context).attr('id');

    const fields = [
      { element: element, field: '', mode: pca.fieldMode.SEARCH | pca.fieldMode.POPULATE }
    ];

    const options = {
      key: key,
      search: { countries: 'GBR' }
    };

    return new pca.Address(fields, options);
  }
}
