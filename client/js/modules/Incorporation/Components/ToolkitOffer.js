export class ToolkitOffer {
  constructor (selectedOffers) {
    this.useObjectContext = true;
    this.selected = false;
    this.selectedOffers = {};

    this.checkSelectedOffers(selectedOffers);
  }

  select (event, element) {
    this.selectedOffers[element.getAttribute('data-offer')] = true;
  }

  unSelect (event, element) {
    this.selectedOffers[element.getAttribute('data-offer')] = false;
  }

  checkSelectedOffers (selectedOffers) {
    const self = this;
    selectedOffers.forEach(function (offer) {
      self.selectedOffers[offer] = true;
    });
  }
}
