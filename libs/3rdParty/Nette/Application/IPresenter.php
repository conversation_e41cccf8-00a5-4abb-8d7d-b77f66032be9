<?php

/**
 * Nette Framework
 *
 * Copyright (c) 2004, 2009 <PERSON> (http://davidgrudl.com)
 *
 * This source file is subject to the "Nette license" that is bundled
 * with this package in the file license.txt.
 *
 * For more information please see http://nettephp.com
 *
 * @copyright  Copyright (c) 2004, 2009 <PERSON>
 * @license    http://nettephp.com/license  Nette license
 * @link       http://nettephp.com
 * @category   Nette
 * @package    Nette\Application
 * @version    $Id: IPresenter.php 182 2008-12-31 00:28:33Z <EMAIL> $
 */

namespace Legacy\Nette\Application;

/**
 * Defines method that must be implemented to allow a component to act like a presenter.
 *
 * <AUTHOR>
 * @copyright  Copyright (c) 2004, 2009 <PERSON>
 * @package    Nette\Application
 */
interface IPresenter
{

	/**
	 * @return void
	 */
	function run();

}
