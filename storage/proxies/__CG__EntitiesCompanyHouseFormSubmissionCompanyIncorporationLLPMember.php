<?php

namespace CMS\Proxy\__CG__\Entities\CompanyHouse\FormSubmission\CompanyIncorporation;

/**
 * DO NOT EDIT THIS FILE - IT WAS CREATED BY DOCTRINE'S PROXY GENERATOR
 */
class LLPMember extends \Entities\CompanyHouse\FormSubmission\CompanyIncorporation\LLPMember implements \Doctrine\ORM\Proxy\Proxy
{
    /**
     * @var \Closure the callback responsible for loading properties in the proxy object. This callback is called with
     *      three parameters, being respectively the proxy object to be initialized, the method that triggered the
     *      initialization process and an array of ordered parameters that were passed to that method.
     *
     * @see \Doctrine\Common\Persistence\Proxy::__setInitializer
     */
    public $__initializer__;

    /**
     * @var \Closure the callback responsible of loading properties that need to be copied in the cloned object
     *
     * @see \Doctrine\Common\Persistence\Proxy::__setCloner
     */
    public $__cloner__;

    /**
     * @var boolean flag indicating if this object was already initialized
     *
     * @see \Doctrine\Common\Persistence\Proxy::__isInitialized
     */
    public $__isInitialized__ = false;

    /**
     * @var array properties to be lazy loaded, with keys being the property
     *            names and values being their default values
     *
     * @see \Doctrine\Common\Persistence\Proxy::__getLazyProperties
     */
    public static $lazyPropertiesDefaults = [];



    /**
     * @param \Closure $initializer
     * @param \Closure $cloner
     */
    public function __construct($initializer = null, $cloner = null)
    {

        $this->__initializer__ = $initializer;
        $this->__cloner__      = $cloner;
    }

    /**
     * {@inheritDoc}
     * @param string $name
     */
    public function & __get($name)
    {
        $this->__initializer__ && $this->__initializer__->__invoke($this, '__get', [$name]);

        return parent::__get($name);
    }

    /**
     * {@inheritDoc}
     * @param string $name
     * @param mixed  $value
     */
    public function __set($name, $value)
    {
        $this->__initializer__ && $this->__initializer__->__invoke($this, '__set', [$name, $value]);

        return parent::__set($name, $value);
    }

    /**
     * {@inheritDoc}
     * @param  string $name
     * @return boolean
     */
    public function __isset($name)
    {
        $this->__initializer__ && $this->__initializer__->__invoke($this, '__isset', [$name]);

        return parent::__isset($name);

    }

    /**
     * 
     * @return array
     */
    public function __sleep()
    {
        if ($this->__isInitialized__) {
            return ['__isInitialized__', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\CompanyIncorporation\\LLPMember' . "\0" . 'type', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\CompanyIncorporation\\LLPMember' . "\0" . 'designatedInd', 'title', 'forename', 'middleName', 'surname', 'dob', 'premise', 'street', 'thoroughfare', 'postTown', 'county', 'country', 'postcode', 'residential_premise', 'residential_street', 'residential_thoroughfare', 'residential_postTown', 'residential_county', 'residential_country', 'residential_postcode', 'residential_secure_address_ind', 'careOfName', 'poBox', 'officer'];
        }

        return ['__isInitialized__', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\CompanyIncorporation\\LLPMember' . "\0" . 'type', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\CompanyIncorporation\\LLPMember' . "\0" . 'designatedInd', 'title', 'forename', 'middleName', 'surname', 'dob', 'premise', 'street', 'thoroughfare', 'postTown', 'county', 'country', 'postcode', 'residential_premise', 'residential_street', 'residential_thoroughfare', 'residential_postTown', 'residential_county', 'residential_country', 'residential_postcode', 'residential_secure_address_ind', 'careOfName', 'poBox', 'officer'];
    }

    /**
     * 
     */
    public function __wakeup()
    {
        if ( ! $this->__isInitialized__) {
            $this->__initializer__ = function (LLPMember $proxy) {
                $proxy->__setInitializer(null);
                $proxy->__setCloner(null);

                $existingProperties = get_object_vars($proxy);

                foreach ($proxy->__getLazyProperties() as $property => $defaultValue) {
                    if ( ! array_key_exists($property, $existingProperties)) {
                        $proxy->$property = $defaultValue;
                    }
                }
            };

        }
    }

    /**
     * 
     */
    public function __clone()
    {
        $this->__cloner__ && $this->__cloner__->__invoke($this, '__clone', []);
    }

    /**
     * Forces initialization of the proxy
     */
    public function __load()
    {
        $this->__initializer__ && $this->__initializer__->__invoke($this, '__load', []);
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __isInitialized()
    {
        return $this->__isInitialized__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitialized($initialized)
    {
        $this->__isInitialized__ = $initialized;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitializer(\Closure $initializer = null)
    {
        $this->__initializer__ = $initializer;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __getInitializer()
    {
        return $this->__initializer__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setCloner(\Closure $cloner = null)
    {
        $this->__cloner__ = $cloner;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific cloning logic
     */
    public function __getCloner()
    {
        return $this->__cloner__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     * @static
     */
    public function __getLazyProperties()
    {
        return self::$lazyPropertiesDefaults;
    }

    
    /**
     * {@inheritDoc}
     */
    public function getDesignatedInd()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getDesignatedInd', []);

        return parent::getDesignatedInd();
    }

    /**
     * {@inheritDoc}
     */
    public function setDesignatedInd($designatedInd)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setDesignatedInd', [$designatedInd]);

        return parent::setDesignatedInd($designatedInd);
    }

    /**
     * {@inheritDoc}
     */
    public function setFormSubmission(\Entities\CompanyHouse\FormSubmission\CompanyIncorporation $formSubmission)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setFormSubmission', [$formSubmission]);

        return parent::setFormSubmission($formSubmission);
    }

    /**
     * {@inheritDoc}
     */
    public function getOfficer()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getOfficer', []);

        return parent::getOfficer();
    }

    /**
     * {@inheritDoc}
     */
    public function setOfficer(\Entities\CompanyHouse\Helper\BaseOfficer $officer)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setOfficer', [$officer]);

        return parent::setOfficer($officer);
    }

    /**
     * {@inheritDoc}
     */
    public function hasConsentToAct()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'hasConsentToAct', []);

        return parent::hasConsentToAct();
    }

    /**
     * {@inheritDoc}
     */
    public function setConsentToAct($consentToAct)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setConsentToAct', [$consentToAct]);

        return parent::setConsentToAct($consentToAct);
    }

    /**
     * {@inheritDoc}
     */
    public function getMemberId()
    {
        if ($this->__isInitialized__ === false) {
            return (int)  parent::getMemberId();
        }


        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getMemberId', []);

        return parent::getMemberId();
    }

    /**
     * {@inheritDoc}
     */
    public function getNominee()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getNominee', []);

        return parent::getNominee();
    }

    /**
     * {@inheritDoc}
     */
    public function setNominee($nominee)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setNominee', [$nominee]);

        return parent::setNominee($nominee);
    }

    /**
     * {@inheritDoc}
     */
    public function getTitle()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getTitle', []);

        return parent::getTitle();
    }

    /**
     * {@inheritDoc}
     */
    public function getForename()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getForename', []);

        return parent::getForename();
    }

    /**
     * {@inheritDoc}
     */
    public function getMiddleName()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getMiddleName', []);

        return parent::getMiddleName();
    }

    /**
     * {@inheritDoc}
     */
    public function getSurname()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getSurname', []);

        return parent::getSurname();
    }

    /**
     * {@inheritDoc}
     */
    public function getDob()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getDob', []);

        return parent::getDob();
    }

    /**
     * {@inheritDoc}
     */
    public function getNationality()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getNationality', []);

        return parent::getNationality();
    }

    /**
     * {@inheritDoc}
     */
    public function getCorporateName()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCorporateName', []);

        return parent::getCorporateName();
    }

    /**
     * {@inheritDoc}
     */
    public function getPremise()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getPremise', []);

        return parent::getPremise();
    }

    /**
     * {@inheritDoc}
     */
    public function getStreet()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getStreet', []);

        return parent::getStreet();
    }

    /**
     * {@inheritDoc}
     */
    public function getThoroughfare()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getThoroughfare', []);

        return parent::getThoroughfare();
    }

    /**
     * {@inheritDoc}
     */
    public function getPostTown()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getPostTown', []);

        return parent::getPostTown();
    }

    /**
     * {@inheritDoc}
     */
    public function getCounty()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCounty', []);

        return parent::getCounty();
    }

    /**
     * {@inheritDoc}
     */
    public function getCountry()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCountry', []);

        return parent::getCountry();
    }

    /**
     * {@inheritDoc}
     */
    public function getPostcode()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getPostcode', []);

        return parent::getPostcode();
    }

    /**
     * {@inheritDoc}
     */
    public function getCountryOfResidence()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCountryOfResidence', []);

        return parent::getCountryOfResidence();
    }

    /**
     * {@inheritDoc}
     */
    public function getResidentialPremise()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getResidentialPremise', []);

        return parent::getResidentialPremise();
    }

    /**
     * {@inheritDoc}
     */
    public function getResidentialStreet()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getResidentialStreet', []);

        return parent::getResidentialStreet();
    }

    /**
     * {@inheritDoc}
     */
    public function getResidentialThoroughfare()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getResidentialThoroughfare', []);

        return parent::getResidentialThoroughfare();
    }

    /**
     * {@inheritDoc}
     */
    public function getResidentialPostTown()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getResidentialPostTown', []);

        return parent::getResidentialPostTown();
    }

    /**
     * {@inheritDoc}
     */
    public function getResidentialCounty()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getResidentialCounty', []);

        return parent::getResidentialCounty();
    }

    /**
     * {@inheritDoc}
     */
    public function getResidentialCountry()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getResidentialCountry', []);

        return parent::getResidentialCountry();
    }

    /**
     * {@inheritDoc}
     */
    public function getResidentialPostcode()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getResidentialPostcode', []);

        return parent::getResidentialPostcode();
    }

    /**
     * {@inheritDoc}
     */
    public function getResidentialSecureAddressInd()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getResidentialSecureAddressInd', []);

        return parent::getResidentialSecureAddressInd();
    }

    /**
     * {@inheritDoc}
     */
    public function getPlaceRegistered()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getPlaceRegistered', []);

        return parent::getPlaceRegistered();
    }

    /**
     * {@inheritDoc}
     */
    public function getRegistrationNumber()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getRegistrationNumber', []);

        return parent::getRegistrationNumber();
    }

    /**
     * {@inheritDoc}
     */
    public function getLawGoverned()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getLawGoverned', []);

        return parent::getLawGoverned();
    }

    /**
     * {@inheritDoc}
     */
    public function getLegalForm()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getLegalForm', []);

        return parent::getLegalForm();
    }

    /**
     * {@inheritDoc}
     */
    public function getCountryOrState()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCountryOrState', []);

        return parent::getCountryOrState();
    }

    /**
     * {@inheritDoc}
     */
    public function getCareOfName()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCareOfName', []);

        return parent::getCareOfName();
    }

    /**
     * {@inheritDoc}
     */
    public function getPoBox()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getPoBox', []);

        return parent::getPoBox();
    }

    /**
     * {@inheritDoc}
     */
    public function getType()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getType', []);

        return parent::getType();
    }

    /**
     * {@inheritDoc}
     */
    public function getCorporate()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCorporate', []);

        return parent::getCorporate();
    }

    /**
     * {@inheritDoc}
     */
    public function setCorporate($corporate)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCorporate', [$corporate]);

        return parent::setCorporate($corporate);
    }

    /**
     * {@inheritDoc}
     */
    public function setResidentialPremise($residential_premise)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setResidentialPremise', [$residential_premise]);

        return parent::setResidentialPremise($residential_premise);
    }

    /**
     * {@inheritDoc}
     */
    public function setResidentialStreet($residential_street)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setResidentialStreet', [$residential_street]);

        return parent::setResidentialStreet($residential_street);
    }

    /**
     * {@inheritDoc}
     */
    public function setResidentialThoroughfare($residential_thoroughfare)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setResidentialThoroughfare', [$residential_thoroughfare]);

        return parent::setResidentialThoroughfare($residential_thoroughfare);
    }

    /**
     * {@inheritDoc}
     */
    public function setResidentialPostTown($residential_postTown)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setResidentialPostTown', [$residential_postTown]);

        return parent::setResidentialPostTown($residential_postTown);
    }

    /**
     * {@inheritDoc}
     */
    public function setResidentialCounty($residential_county)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setResidentialCounty', [$residential_county]);

        return parent::setResidentialCounty($residential_county);
    }

    /**
     * {@inheritDoc}
     */
    public function setResidentialCountry($residential_country)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setResidentialCountry', [$residential_country]);

        return parent::setResidentialCountry($residential_country);
    }

    /**
     * {@inheritDoc}
     */
    public function setResidentialPostcode($residential_postcode)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setResidentialPostcode', [$residential_postcode]);

        return parent::setResidentialPostcode($residential_postcode);
    }

    /**
     * {@inheritDoc}
     */
    public function setResidentialSecureAddressInd($residential_secure_address_ind)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setResidentialSecureAddressInd', [$residential_secure_address_ind]);

        return parent::setResidentialSecureAddressInd($residential_secure_address_ind);
    }

    /**
     * {@inheritDoc}
     */
    public function extractOfficer()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'extractOfficer', []);

        return parent::extractOfficer();
    }

    /**
     * {@inheritDoc}
     */
    public function extractIdentification()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'extractIdentification', []);

        return parent::extractIdentification();
    }

    /**
     * {@inheritDoc}
     */
    public function extractAddress($address)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'extractAddress', [$address]);

        return parent::extractAddress($address);
    }

    /**
     * {@inheritDoc}
     */
    public function hydrateOfficer()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'hydrateOfficer', []);

        return parent::hydrateOfficer();
    }

    /**
     * {@inheritDoc}
     */
    public function hydrateIdentification()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'hydrateIdentification', []);

        return parent::hydrateIdentification();
    }

    /**
     * {@inheritDoc}
     */
    public function getHydrateAddress()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getHydrateAddress', []);

        return parent::getHydrateAddress();
    }

    /**
     * {@inheritDoc}
     */
    public function isCorporate()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isCorporate', []);

        return parent::isCorporate();
    }

    /**
     * {@inheritDoc}
     */
    public function getFields()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getFields', []);

        return parent::getFields();
    }

    /**
     * {@inheritDoc}
     */
    public function getFullName()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getFullName', []);

        return parent::getFullName();
    }

    /**
     * {@inheritDoc}
     */
    public function getTypeName(): string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getTypeName', []);

        return parent::getTypeName();
    }

    /**
     * {@inheritDoc}
     */
    public function __call($name, $args)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, '__call', [$name, $args]);

        return parent::__call($name, $args);
    }

    /**
     * {@inheritDoc}
     */
    public function __unset($name)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, '__unset', [$name]);

        return parent::__unset($name);
    }

}
