<?php

use MigrationModule\Dto\Node;
use MigrationModule\Dto\Page;
use MigrationModule\Dto\Property;
use MigrationModule\Helpers\NodeMigrationHelper;
use Phinx\Migration\AbstractMigration;

class MonthlyRenewalRo extends AbstractMigration
{
    const PRODUCT_NAME = 'registered_office_n1_monthly_renewal_08_2023';
    const JARED = 8;

    public function up()
    {
        $helper = new NodeMigrationHelper($this);

        if ($nodeId = $helper->getExistingNodeId(self::PRODUCT_NAME)) {
            return $nodeId;
        }

        $page = new Page('Registered Office - N1 (Monthly Renewal Fee)');

        $properties = [
            new Property('additional', ''),
            new Property('associatedDescription', ''),
            new Property('associatedIconClass', ''),
            new Property('associatedImageId', ''),
            new Property('associatedPrice', '9.99'),
            new Property('associatedProducts3', ''),
            new Property('associatedText', ''),
            new Property('availableForPayByPhone', 'RENEWALS'),
            new Property('bankingEnabled', ''),
            new Property('bankingOptions', ''),
            new Property('bankingRequired', ''),
            new Property('basketText', ''),
            new Property('blacklistedContainedProducts', ''),
            new Property('blacklistedEquivalentProducts', ''),
            new Property('cashBackAmount', '0'),
            new Property('conditionedById', ''),
            new Property('customAssociatedIcon', ''),
            new Property('duration', '+1 month'),
            new Property('emailCmsFileAttachments', ''),
            new Property('emailText', $this->getEmailText()),
            new Property('isAutoRenewalAllowed', '1'),
            new Property('isFeefoEnabled', '0'),
            new Property('isIdCheckRequired', '1'),
            new Property('isRenewalProduct', '1'),
            new Property('isVoServiceEligible', ''),
            new Property('lockCompany', ''),
            new Property('markUp', ''),
            new Property('maxQuantityOne', '1'),
            new Property('nonVatableDescription', ''),
            new Property('nonVatableValue', '0'),
            new Property('notApplyVat', '0'),
            new Property('offerPrice', '9.99'),
            new Property('offerProductId', ''),
            new Property('onlyOneItem', ''),
            new Property('onlyOurCompanies', '1'),
            new Property('optionalRenewalProductId', '334'),
            new Property('price', '9.99'),
            new Property('printedCertificateOptionEnabled', ''),
            new Property('productValue', '9.99'),
            new Property('registrationReviewEnabled', ''),
            new Property('removableFromBasket', '1'),
            new Property('removeFromBasketConfirmation', ''),
            new Property('renewalDtStartBehavior', ''),
            new Property('renewalEmailPrice', '9.99'),
            new Property('requiredCompanyNumber', '1'),
            new Property('requiredCorePackage', '0'),
            new Property('requiredIncorporatedCompanyNumber', '0'),
            new Property('responsibleEmails', ''),
            new Property('sageNominalCode', ''),
            new Property('saveToCompany', ''),
            new Property('serviceTypeId', 'REGISTERED_OFFICE'),
            new Property('showInMyServicesPage', ''),
            new Property('specialPrice', '0'),
            new Property('toolkitOfferTypes', ''),
            new Property('typeId', 'BYSHR'),
            new Property('upgradeDescription', $this->getUpgradeDescription()),
            new Property('upgradeImageId', ''),
            new Property('upgradeProductId', ''),
            new Property('voServiceDurationInMonths', '0'),
            new Property('wholesalePrice', '9.99'),
        ];

        $node = new Node(
            $page,
            $properties,
            self::PRODUCT_NAME,
            1862,
            'DefaultControler',
            'ProductAdminControler',
            50
        );

        $helper->create($node);
        $nodeId = $helper->getExistingNodeId(self::PRODUCT_NAME);
        $helper->createProperties($nodeId, [new Property('renewalProductId', $nodeId)]);
        $legacyROMonthlyRenewalId = $helper->getExistingNodeId(CreateAnnualRegisteredOfficeMonthlyRecurrence::PRODUCT_NAME);
        $this->updatePage($legacyROMonthlyRenewalId);
        $roMonthlyId = $helper->getExistingNodeId(CreateRegisteredOfficeMonthlyRecurrence::PRODUCT_NAME);
        $this->execute(
            sprintf("UPDATE %s SET `value`= '%s' WHERE `nodeId` = %s AND `name` IN (%s)", TBL_PROPERTIES,
                '9.99', $roMonthlyId, "'associatedPrice', 'offerPrice', 'price', 'productValue', 'wholesalePrice'")
        );

        $this->execute(
            sprintf("UPDATE %s SET `value`= '' WHERE `nodeId` = %s AND `name` = '%s'", TBL_PROPERTIES,
                $legacyROMonthlyRenewalId, 'availableForPayByPhone')
        );

        $this->execute(
            sprintf("UPDATE %s SET `value`= '%s' WHERE `nodeId` = %s AND `name` = '%s'", TBL_PROPERTIES,
                $nodeId, $roMonthlyId, 'renewalProductId')
        );
    }

    public function down()
    {
        $helper = new NodeMigrationHelper($this);
        $nodeId = $helper->getExistingNodeId(self::PRODUCT_NAME);

        $this->execute(sprintf("DELETE FROM `%s` WHERE (`node_id` = '%s');", TBL_NODES, $nodeId));
        $this->execute(sprintf("DELETE FROM `%s` WHERE (`node_id` = '%s');", TBL_PAGES, $nodeId));
        $this->execute(sprintf("DELETE FROM `%s` WHERE (`nodeId` = '%s');", TBL_PROPERTIES, $nodeId));
    }

    private function getUpgradeDescription(): string
    {
        return <<<EOD
<p>&#160;</p>
EOD;
    }

    private function getEmailText(): string
    {
        return <<<EOD
<p>
    <strong>Registered Office Renewal</strong><br /> 
    Thank you for your purchase of the renewal for: Registered Office. 
    Nothing more is needed on your part. We will update our database within the next 24 hours 
    and you should stop receiving reminders for the renewal of this service.
</p>
EOD;
    }

    private function getUpgradeText(): string
    {
        return <<<EOD
Do you know how to file your company's Confirmation Statement? Upgrade to Comprehensive and we will cover all your statutory requirements, including the filing of your return.
EOD;
    }

    private function updatePage(int $nodeId)
    {
        $newTitle = '[DEPRECATED 07/2023] - Registered Office - N1 (Monthly Renewal Fee)';
        $this->execute(
            sprintf("UPDATE %s SET `title`= '%s' WHERE `node_id` = %s", TBL_PAGES,
                $newTitle, $nodeId)
        );
    }
}
