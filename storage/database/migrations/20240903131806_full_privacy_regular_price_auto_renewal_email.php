<?php

use Phinx\Migration\AbstractMigration;
use MigrationModule\Dto\Node;
use MigrationModule\Dto\Page;
use MigrationModule\Dto\Property;
use MigrationModule\Helpers\NodeMigrationHelper;
use ServiceModule\Mailers\FullPrivacyRegularPriceRenewalEmailer;

final class FullPrivacyRegularPriceAutoRenewalEmail extends AbstractMigration
{
    public function up()
    {
        if ($this->getNodeId() !== null) return;

        $page = new Page('Full Privacy Auto-Renewal (Regular Price) Email');

        $properties = [
            new Property('from', '<EMAIL>'),
            new Property('fromName', 'Companies Made Simple'),
            new Property('subject', 'Registered Office Address service renewal'),
            new Property(
                'templateName',
                FullPrivacyRegularPriceRenewalEmailer::FULL_PRIVACY_REGULAR_PRICE_AUTO_RENEWAL_EMAIL
            ),
        ];

        $node = new Node(
            $page,
            $properties,
            FullPrivacyRegularPriceRenewalEmailer::FULL_PRIVACY_REGULAR_PRICE_AUTO_RENEWAL_EMAIL,
            1369,
            null,
            'EmailAdminControler',
            100
        );

        $helper = new NodeMigrationHelper($this);

        $helper->create($node);
    }

    public function down()
    {
        $nodeId = $this->getNodeId();
        $this->execute(sprintf("DELETE FROM `%s` WHERE (`node_id` = '%s');", TBL_NODES, $nodeId));
        $this->execute(sprintf("DELETE FROM `%s` WHERE (`node_id` = '%s');", TBL_PAGES, $nodeId));
        $this->execute(sprintf("DELETE FROM `%s` WHERE (`nodeId` = '%s');", TBL_PROPERTIES, $nodeId));
    }

    private function getNodeId()
    {
        $node = $this->fetchRow(
            sprintf(
                "SELECT node_id FROM %s WHERE `name`='%s'",
                TBL_NODES,
                FullPrivacyRegularPriceRenewalEmailer::FULL_PRIVACY_REGULAR_PRICE_AUTO_RENEWAL_EMAIL
            )
        );

        return $node['node_id'] ?? null;
    }
}
