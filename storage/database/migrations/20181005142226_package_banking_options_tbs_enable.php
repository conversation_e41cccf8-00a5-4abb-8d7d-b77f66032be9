<?php

use Phinx\Migration\AbstractMigration;

class PackageBankingOptionsTbsEnable extends AbstractMigration
{
    const OPTIONS_UP = 'BARCLAYS,TSB,TIDE';
    const OPTIONS_DOWN = 'BARCLAYS,TIDE';

    private static $packages = [1317, 1315, 1316, 1314, 1313, 379, 1175, 1668, 1684, 1694];

    public function up()
    {
        foreach (self::$packages as $packageId) {
            $this->execute($this->getSql($packageId, self::OPTIONS_UP));
        }
    }

    public function down()
    {
        foreach (self::$packages as $packageId) {
            $this->execute($this->getSql($packageId, self::OPTIONS_DOWN));
        }
    }

    private function getSql(int $packageId, string $options): string
    {
        $sql = <<<SQL
UPDATE `cms2_properties` SET
`value` = '%s'
WHERE `nodeId` = %d 
AND `name` = 'bankingOptions';
SQL;
        return sprintf($sql, $options, $packageId);
    }
}
