<?php

use Entities\Service;
use MigrationModule\Dto\Node;
use MigrationModule\Dto\Page;
use MigrationModule\Dto\Property;
use MigrationModule\Helpers\NodeMigrationHelper;
use Phinx\Migration\AbstractMigration;

class MailForwardingProducts extends AbstractMigration
{
    const MAIL_FORWARDING_UPSELL = 'mail-forwarding-upsell'; // clone: 1625
    const MAIL_FORWARDING_RENEWAL = 'mail-forwarding-renewal'; // clone: 334

    const MAIL_FORWARDING_FOLDER = 442;

    public function up()
    {
        $helper = new NodeMigrationHelper($this);
        $upsellNodeId = $this->createUpsell($helper);
        $renewalNodeId = $this->createRenewal($helper);

        $this->updateRenewalProduct($upsellNodeId, $renewalNodeId);
        $this->updateRenewalProduct($renewalNodeId, $renewalNodeId);
    }

    public function down()
    {

    }

    private function createUpsell(NodeMigrationHelper $helper): int
    {
        if ($nodeId = $helper->getExistingNodeId(self::MAIL_FORWARDING_UPSELL)) {
            return $nodeId;
        }

        $page = new Page('Mail Forwarding Address - Upsell');

        $properties = [
            new Property('price', 10),
            new Property('associatedPrice', 10),
            new Property('wholesalePrice', 10),
            new Property('productValue', 10),
            new Property('requiredCompanyNumber', 0),
            new Property('requiredIncorporatedCompanyNumber', 0),
            new Property('onlyOurCompanies', 0),
            new Property('onlyOneItem', 0),
            new Property('maxQuantityOne', 0),
            new Property('responsibleEmails', '<EMAIL>'),
            new Property('notApplyVat', 1),
            new Property('emailText', '<p><strong>Mail Forwarding</strong><br />
Thank you for choosing to try out our 3 month Mail Forwarding trial offer. Your account will be set up  within 48 hours (after ID confirmation and excluding weekends) after which you will get an email confirming the set up and your mail forwarding account details. We hope you enjoy your new prestigious Central London address.</p>'),
            new Property('isAutoRenewalAllowed', 1),
            new Property('lockCompany', 0),
            new Property('isIdCheckRequired', 1),
            new Property('serviceTypeId', Service::TYPE_MAIL_FORWARDING),
            new Property('duration', Service::DURATION_THREE_MONTHS),
            new Property('isInitialProduct', '1'),
            new Property('renewalProductId', ''),
        ];

        $node = new Node(
            $page,
            $properties,
            self::MAIL_FORWARDING_UPSELL,
            self::MAIL_FORWARDING_FOLDER,
            'ProductControler',
            'ProductAdminControler',
            40
        );

        return $helper->create($node);
    }

    private function createRenewal(NodeMigrationHelper $helper): int
    {
        $price = '19.99';

        if ($nodeId = $helper->getExistingNodeId(self::MAIL_FORWARDING_RENEWAL)) {
            return $nodeId;
        }

        $page = new Page('Mail Forwarding Address - Renewal');
        $properties = [
            new Property('price', $price),
            new Property('productValue', $price),
            new Property('associatedPrice', $price),
            new Property('wholesalePrice', $price),
            new Property('isIdCheckRequired', 1),
            new Property('requiredCompanyNumber', '1'),
            new Property('onlyOurCompanies', '1'),
            new Property('maxQuantityOne', '1'),
            new Property('emailText', '<p><strong>Registered Office Renewal</strong><br />
Thank you for your purchase of the renewal for: Registered Office. Nothing more is needed on your part. We will update our database within the next 24 hours and you should stop receiving reminders for the renewal of this service.</p>'),
            new Property('serviceTypeId', Service::TYPE_MAIL_FORWARDING),
            new Property('duration', Service::DURATION_ONE_MONTH),
            new Property('isAutoRenewalAllowed', 1),
            new Property('isRenewalProduct', 1),
            new Property('renewalProductId', ''),
        ];
        $node = new Node(
            $page,
            $properties,
            self::MAIL_FORWARDING_RENEWAL,
            self::MAIL_FORWARDING_FOLDER,
            'ProductControler',
            'ProductAdminControler',
            40
        );

        return $helper->create($node);
    }

    private function updateRenewalProduct(int $nodeId, int $productId)
    {
        $sql = sprintf(
            "UPDATE `%s` SET `value` = %d WHERE `nodeId` = %d AND `name` = 'renewalProductId'",
            TBL_PROPERTIES,
            $productId,
            $nodeId
        );
        $this->execute($sql);
    }
}
