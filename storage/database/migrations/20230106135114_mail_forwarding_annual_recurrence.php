<?php

use Phinx\Migration\AbstractMigration;
use MigrationModule\Dto\Node;
use MigrationModule\Dto\Page;
use MigrationModule\Dto\Property;
use MigrationModule\Helpers\NodeMigrationHelper;

class MailForwardingAnnualRecurrence extends AbstractMigration
{
    public const PRODUCT_NAME = 'mail_forwarding_annual_recurrence';

    private $currentServiceTypeIds = [
        'PACKAGE_PRIVACY',
        'PACKAGE_COMPREHENSIVE_ULTIMATE',
        'PACKAGE_BY_GUARANTEE',
        'PACKAGE_SOLE_TRADER_PLUS',
        'RESERVE_COMPANY_NAME',
        'DORMANT_COMPANY_ACCOUNTS',
        'APOSTILLED_DOCUMENTS',
        'CERTIFICATE_OF_GOOD_STANDING',
        'REGISTERED_OFFICE',
        'SERVICE_ADDRESS',
        'NOMINEE',
        'ANNUAL_RETURN',
        'PSC_ONLINE_REGISTER',
        'BUNDLE_PSC_ONLINE_REGISTER_CONFIRMATION_STATEMENT',
        'CONFIRMATION_STATEMENT',
        'FRAUD_PROTECTION',
        'PACKAGE_COMPREHENSIVE',
        'PACKAGE_ULTIMATE',
        'SECRETARIAL_SERVICE'
    ];

    private $newServiceTypeIds = [
        'MAIL_FORWARDING'
    ];

    public function up()
    {
        $this->updateServiceTypeIdFields();

        $helper = new NodeMigrationHelper($this);
        if ($nodeId = $helper->getExistingNodeId(self::PRODUCT_NAME)) {
            return $nodeId;
        }

        $page = new Page('Mail Forwarding (Annual)');

        $properties = [
            new Property('duration', '+12 months'),
            new Property('serviceTypeId', 'MAIL_FORWARDING'),
        ];

        $node = new Node(
            $page,
            $properties,
            self::PRODUCT_NAME,
            442,
            'ProductControler',
            'ProductAdminControler',
            30
        );

        $helper = new NodeMigrationHelper($this);
        $helper->create($node);
    }

    public function down()
    {
        $nodeId = $this->getNodeId();
        $this->execute(sprintf("DELETE FROM `%s` WHERE (`node_id` = '%s');", TBL_NODES, $nodeId));
        $this->execute(sprintf("DELETE FROM `%s` WHERE (`node_id` = '%s');", TBL_PAGES, $nodeId));
        $this->execute(sprintf("DELETE FROM `%s` WHERE (`nodeId` = '%s');", TBL_PROPERTIES, $nodeId));
    }

    private function getNodeId()
    {
        $node = $this->fetchRow(sprintf("SELECT node_id FROM %s WHERE `name`='%s'", TBL_NODES, self::PRODUCT_NAME));
        return $node['node_id'] ?? null;
    }

    private function updateServiceTypeIdFields()
    {
        $newServiceTypeIds = array_merge($this->currentServiceTypeIds, $this->newServiceTypeIds);

        $this->table(TBL_SERVICES)
            ->changeColumn(
                'serviceTypeId',
                'enum',
                [
                    'values' => $newServiceTypeIds,
                    'after' => 'parentId',
                ]
            )
            ->update();

        $this->table(TBL_SERVICE_SETTINGS)
            ->changeColumn(
                'serviceTypeId',
                'enum',
                [
                    'values' => $newServiceTypeIds,
                    'after' => 'serviceSettingId',
                ]
            )
            ->update();
    }
}
