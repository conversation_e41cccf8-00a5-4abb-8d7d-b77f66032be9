<?php

use MigrationModule\Dto\Node;
use MigrationModule\Dto\Page;
use MigrationModule\Dto\Property;
use MigrationModule\Helpers\NodeMigrationHelper;
use Phinx\Migration\AbstractMigration;

class ShelfCompany2023ProductStandard extends AbstractMigration
{
    const PRODUCT_NAME = 'product_shelf_company_2023_standard';
    const JARED = 8;

    public function up()
    {
        $helper = new NodeMigrationHelper($this);
        if ($nodeId = $helper->getExistingNodeId(self::PRODUCT_NAME)) {
            return $nodeId;
        }

        $page = new Page('Shelf Company 2023');
        $page->setLanguagePk('EN');
        $page->setAlternativeTitle('');
        $page->setSeoTitle(null);
        $page->setKeywords(null);
        $page->setDescription(null);
        $page->setAbstract('<p>&#160;</p>');
        $page->setText($this->getPageText());

        $properties = [
            new Property('price', '99'),
            new Property('nonVatableValue', ''),
            new Property('associatedPrice', '99'),
            new Property('wholesalePrice', '99'),
            new Property('offerPrice', '99'),
            new Property('productValue', '99'),
            new Property('requiredCompanyNumber', '0'),
            new Property('requiredIncorporatedCompanyNumber', '0'),
            new Property('onlyOurCompanies', '0'),
            new Property('saveToCompany', ''),
            new Property('onlyOneItem', ''),
            new Property('maxQuantityOne', ''),
            new Property('conditionedById', ''),
            new Property('responsibleEmails', '<EMAIL>'),
            new Property('notApplyVat', '0'),
            new Property('emailText', $this->getEmailText()),
            new Property('basketText', ''),
            new Property('additional', ''),
            new Property('upgradeDescription', $this->getUpgradeDescription()),
            new Property('upgradeImageId', ''),
            new Property('markUp', ''),
            new Property('associatedImageId', ''),
            new Property('associatedText', ''),
            new Property('associatedDescription', ''),
            new Property('associatedIconClass', ''),
            new Property('isFeefoEnabled', ''),
            new Property('isAutoRenewalAllowed', '0'),
            new Property('availableForPayByPhone', 'NOT_AVAILABLE'),
            new Property('toolkitOfferTypes', ''),
            new Property('emailCmsFileAttachments', ''),
            new Property('blacklistedEquivalentProducts', ''),
            new Property('blacklistedContainedProducts', ''),
            new Property('associatedProducts3', ''),
            new Property('offerProductId', ''),
            new Property('lockCompany', ''),
            new Property('serviceTypeId', ''),
            new Property('duration', ''),
            new Property('renewalProductId', ''),
            new Property('isVoServiceEligible', ''),
            new Property('voServiceDurationInMonths', '0'),
            new Property('isIdCheckRequired', '0'),
            new Property('bankingEnabled', ''),
            new Property('bankingOptions', ''),
            new Property('bankingRequired', ''),
            new Property('cashBackAmount', '0'),
            new Property('removableFromBasket', '1'),
            new Property('removeFromBasketConfirmation', ''),
            new Property('isInitialProduct', ''),
            new Property('isRenewalProduct', ''),
            new Property('businessServicesEnabled', '0'),
            new Property('businessServicesOptions', ''),
            new Property('sageNominalCode', ''),
            new Property('printedCertificateOptionEnabled', ''),
            new Property('registrationReviewEnabled', ''),
            new Property('nonVatableDescription', ''),
            new Property('specialPrice', ''),
        ];

        $node = new Node(
            $page,
            $properties,
            self::PRODUCT_NAME,
            431,
            'DefaultControler',
            'ProductAdminControler',
            951
        );
        $node->setLevel(5);
        $node->isDeleted(0);
        $node->setStatusId(2);
        $node->setAuthorId(self::JARED);
        $node->setEditorId(self::JARED);

        return $helper->create($node);
    }

    public function down()
    {
        $helper = new NodeMigrationHelper($this);
        $nodeId = $helper->getExistingNodeId(self::PRODUCT_NAME);

        $this->execute(sprintf("DELETE FROM `%s` WHERE (`node_id` = '%s');", TBL_NODES, $nodeId));
        $this->execute(sprintf("DELETE FROM `%s` WHERE (`node_id` = '%s');", TBL_PAGES, $nodeId));
        $this->execute(sprintf("DELETE FROM `%s` WHERE (`nodeId` = '%s');", TBL_PROPERTIES, $nodeId));
    }

    private function getPageText(): string
    {
        return <<<EOD
<p>&#160;</p>
EOD;
    }

    private function getUpgradeDescription(): string
    {
        return <<<EOD
<p>&#160;</p>
EOD;
    }

    private function getEmailText(): string
    {
        return <<<EOD
<p><strong>Shelf Company Purchase</strong></p>
<p>Thank you for your purchase of a Shelf (ready made) Company 2023</p>
<p><strong>Please complete the following MadeSimple Shelf Company Form via this link:</strong> https://goo.gl/forms/iZBcoXfpdKm8Sbt23</p>
<p>List of company names currently available:&#160;</p>
<p><a href="https://support.companiesmadesimple.com/article/957-shelf-companies-available-names">https://support.companiesmadesimple.com/article/957-shelf-companies-available-names</a></p>
<p>In the form, you will be able to advise us which company you would like to purchase from our list, which is kept up to date on a daily basis. Please complete the form as soon as possible to avoid someone else possibly choosing the same shelf company. Chosen companies are designated on a first come first serve basis.&#160;&#160;</p>
<p>As soon as the completed form is received by our team, we shall make the necessary appointments and send you the relevant company documents to the address provided on the 'My Details' section in your account, unless instructed otherwise.&#160;</p>
<p>Not the name you want? You can change the company name from just £49.99, including the Companies House filing fee, certificate of change of name and online filing. <a href="http://www.companiesmadesimple.com/company-name-change.html">Find out more about company name change</a>.</p>
EOD;
    }
}
