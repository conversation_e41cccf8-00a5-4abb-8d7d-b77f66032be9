<?php

use Phinx\Migration\AbstractMigration;

class PearlTracking extends AbstractMigration
{
    public function up()
    {
        $this->table(
          'cms2_pearl_tracking_lead',
          ['id' => 'id', 'primary_key' => 'id']
        )
          ->addColumn('firstName', 'string')
          ->addColumn('lastName', 'string')
          ->addColumn('email', 'string')
          ->addColumn('phone', 'string')
          ->addColumn('postcode', 'string')
          ->addColumn('companyName', 'string')
          ->addColumn('dtc', 'datetime')
          ->addColumn('dtm', 'datetime')
          ->create();
    }

    public function down()
    {
        $this->dropTable('cms2_pearl_tracking_lead');
    }
}
