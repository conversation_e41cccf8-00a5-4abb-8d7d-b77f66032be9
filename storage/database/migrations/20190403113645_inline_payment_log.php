<?php

use Phinx\Migration\AbstractMigration;

class InlinePaymentLog extends AbstractMigration
{
    public function change()
    {
        $this->table('cms2_inline_payment_log', ['id' => false])
            ->addColumn('id', 'string')
            ->addColumn('type', 'string')
            ->addColumn('companyId', 'integer')
            ->addColumn('customerId', 'integer')
            ->addColumn('productId', 'integer')
            ->addColumn('tokenId', 'integer', ['null' => true])
            ->addColumn('orderId', 'integer', ['null' => true])
            ->addColumn('amount', 'string', ['null' => true])
            ->addColumn('url', 'string')
            ->addColumn('layout', 'string', ['null' => true])
            ->addColumn('ctaLabel', 'string', ['null' => true])
            ->addColumn('dtc', 'datetime')
            ->create();
    }
}
