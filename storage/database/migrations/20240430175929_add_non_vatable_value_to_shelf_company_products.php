<?php

use Phinx\Migration\AbstractMigration;
use MigrationModule\Helpers\NodeMigrationHelper;

class AddNonVatableValueToShelfCompanyProducts extends AbstractMigration
{
    const PROPERTY_NAME = 'nonVatableValue';

    public function up()
    {
        $migrationHelper = new NodeMigrationHelper($this);
        $pricesToUpdate = [ // product name => [new non-vatable value]
            'product_shelf_company_2023_standard' => ['10'],
            'product_shelf_company_2023_premium' => ['44'],
            'product_shelf_company_2024_standard' => ['10'],
            'product_shelf_company_2024_premium' => ['44']
        ];

        foreach ($pricesToUpdate as $productName => $newValues) {
            $nodeId = $migrationHelper->getExistingNodeId($productName);

            if (empty($nodeId)) throw new Exception(sprintf('Node with name %s does not exist!', $productName));

            [$newNonVatableValue] = $newValues;

            $migrationHelper->updateProperty($nodeId, self::PROPERTY_NAME, $newNonVatableValue);
        }
    }
    public function down()
    {
        $migrationHelper = new NodeMigrationHelper($this);
        $pricesToUpdate = [ // product name => [old non-vatable value]
            'product_shelf_company_2023_standard' => [''],
            'product_shelf_company_2023_premium' => [''],
            'product_shelf_company_2024_standard' => [''],
            'product_shelf_company_2024_premium' => ['']
        ];

        foreach ($pricesToUpdate as $productName => $oldValues) {
            $nodeId = $migrationHelper->getExistingNodeId($productName);

            if (empty($nodeId)) throw new Exception(sprintf('Node with name %s does not exist!', $productName));

            [$oldNonVatableValue] = $oldValues;

            $migrationHelper->updateProperty($nodeId, self::PROPERTY_NAME, $oldNonVatableValue);
        }
    }
}
