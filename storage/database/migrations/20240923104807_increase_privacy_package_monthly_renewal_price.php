<?php

use MigrationModule\Dto\Node;
use MigrationModule\Dto\Page;
use MigrationModule\Dto\Property;
use MigrationModule\Helpers\NodeMigrationHelper;
use Models\Products\Package;
use Phinx\Migration\AbstractMigration;

final class IncreasePrivacyPackageMonthlyRenewalPrice extends AbstractMigration
{
    private const NEW_PRICE = '8.99';
    private const NEW_WHOLESALE_PRICE = '7.99';
    private const SPECIAL_PRICE = '39.99';
    private const CASH_BACK_AMOUNT = '55';


    public function up(): void
    {
        $helper = new NodeMigrationHelper($this);

        if ($nodeId = $helper->getExistingNodeId(Package::PACKAGE_PRIVACY_RENEWAL_MONTHLY)) {
            throw new Exception(sprintf('Node with name %s already exists!', Package::PACKAGE_PRIVACY_RENEWAL_MONTHLY));
        }

        $page = new Page('Privacy Package Renewal (Monthly)');
        $properties = [
            new Property('associatedPrice', self::NEW_PRICE),
            new Property('offerPrice', self::NEW_PRICE),
            new Property('price', self::NEW_PRICE),
            new Property('productValue', self::NEW_PRICE),
            new Property('specialPrice', self::SPECIAL_PRICE),
            new Property('wholesalePrice', self::NEW_WHOLESALE_PRICE),
            new Property('cashBackAmount', self::CASH_BACK_AMOUNT),
            new Property('additional', ''),
            new Property('associatedDescription', ''),
            new Property('associatedIconClass', ''),
            new Property('associatedImageId', '1018'),
            new Property('associatedProducts1', '1867,1796'),
            new Property('associatedProducts2', '589,443,782,569,567,301,302,331,299,298,325'),
            new Property('associatedProducts3', '1867,1705,567,443,299,298'),
            new Property('associatedText', ''),
            new Property('availableForPayByPhone', 'RENEWALS'),
            new Property('bankingEnabled', ''),
            new Property('bankingOptions', ''),
            new Property('bankingRequired', ''),
            new Property('basketText', 'UK Private Company Limited by Shares. After purchase you will need to enter your company details and submit it for incorporation.'),
            new Property('blacklistedContainedProducts', '165,475'),
            new Property('blacklistedEquivalentProducts', '379,436,1175,1430,1598,400,401'),
            new Property('businessServicesEnabled', '1'),
            new Property('businessServicesOptions', '1,3,4'),
            new Property('conditionedById', ''),
            new Property('customAssociatedIcon', ''),
            new Property('disabledLeftColumn', '1'),
            new Property('disabledRightColumn', '1'),
            new Property('duration', '+1 month'),
            new Property('emailCmsFileAttachments', ''),
            new Property('emailText', $this->getEmailText()),
            new Property('includedProducts', '165,202,475'),
            new Property('isAutoRenewalAllowed', '1'),
            new Property('isFeefoEnabled', '1'),
            new Property('isIdCheckRequired', '1'),
            new Property('isInternational', ''),
            new Property('isRenewalProduct', '1'),
            new Property('isVoServiceEligible', ''),
            new Property('lockCompany', ''),
            new Property('markUp', ''),
            new Property('maxQuantityOne', '1'),
            new Property('nonVatableDescription', ''),
            new Property('nonVatableValue', '0'),
            new Property('notApplyVat', ''),
            new Property('offerOfTheMonthProductId', '331'),
            new Property('offerProductId', '1705'),
            new Property('onlyOneItem', ''),
            new Property('onlyOurCompanies', '1'),
            new Property('optionalRenewalProductId', '1353'),
            new Property('printedCertificateOptionEnabled', ''),
            new Property('registrationReviewEnabled', ''),
            new Property('removableFromBasket', '1'),
            new Property('removeFromBasketConfirmation', ''),
            new Property('renewalDtStartBehavior', ''),
            new Property('renewalEmailPrice', '0'),
            new Property('requiredCompanyNumber', '1'),
            new Property('requiredCorePackage', '0'),
            new Property('requiredIncorporatedCompanyNumber', '0'),
            new Property('responsibleEmails', ''),
            new Property('sageNominalCode', ''),
            new Property('saveToCompany', ''),
            new Property('serviceTypeId', 'PACKAGE_PRIVACY'),
            new Property('showInMyServicesPage', ''),
            new Property('toolkitOfferTypes', ''),
            new Property('typeId', 'BYSHR'),
            new Property('upgradeDescription', $this->getUpgradeDescription()),
            new Property('upgradeImageId', ''),
            new Property('upgradeProductId', ''),
            new Property('upgradeText', $this->getUpgradeText()),
            new Property('voServiceDurationInMonths', '0'),
        ];

        $node = new Node(
            $page,
            $properties,
            Package::PACKAGE_PRIVACY_RENEWAL_MONTHLY,
            1862,
            'DefaultControler',
            'RenewalPackageAdminControler',
            50
        );

        $helper->create($node);
        $nodeId = $helper->getExistingNodeId(Package::PACKAGE_PRIVACY_RENEWAL_MONTHLY);
        $helper->createProperties($nodeId, [new Property('renewalProductId', $nodeId)]);
        $legacyPrivacyMonthlyRenewalId = $helper->getExistingNodeId(MonthlyRenewalPrivacyPackage::PRODUCT_NAME);
        $this->updatePage($legacyPrivacyMonthlyRenewalId);

        $privacyPackageRenewalNodeId = $helper->getExistingNodeId(Package::PACKAGE_RENEWAL_PRIVACY);

        $this->execute(
            sprintf("UPDATE %s SET `value`= '' WHERE `nodeId` = %s AND `name` = '%s'", TBL_PROPERTIES,
                $legacyPrivacyMonthlyRenewalId, 'availableForPayByPhone')
        );
        $this->execute(
            sprintf("UPDATE %s SET `value`= '%s' WHERE `nodeId` = %s AND `name` = '%s'", TBL_PROPERTIES,
                $nodeId, $privacyPackageRenewalNodeId, 'optionalRenewalProductId')
        );
    }

    public function down(): void
    {
        $helper = new NodeMigrationHelper($this);
        $nodeId = $helper->getExistingNodeId(Package::PACKAGE_PRIVACY_RENEWAL_MONTHLY);

        $this->execute(sprintf("DELETE FROM `%s` WHERE (`node_id` = '%s');", TBL_NODES, $nodeId));
        $this->execute(sprintf("DELETE FROM `%s` WHERE (`node_id` = '%s');", TBL_PAGES, $nodeId));
        $this->execute(sprintf("DELETE FROM `%s` WHERE (`nodeId` = '%s');", TBL_PROPERTIES, $nodeId));
    }


    private function getUpgradeDescription(): string
    {
        return <<<EOD
<p>&#160;</p>
EOD;
    }

    private function getEmailText(): string
    {
        return <<<EOD
<p>&#160;</p>
EOD;
    }

    private function getUpgradeText(): string
    {
        return <<<EOD
Want to protect your residential address from junk mail? Upgrade to Privacy to use our Registered Office. We'll forward on all official government post and keep your address private.
EOD;
    }

    private function updatePage(int $nodeId): void
    {

        $this->execute(
            sprintf("UPDATE %s SET `title`= '%s' WHERE `node_id` = %s", TBL_PAGES,
                '[DEPRECATED 09/2024] - Privacy Package Renewal (Monthly)', $nodeId)
        );
        $this->execute(
            sprintf("UPDATE %s SET `alternative_title`= '%s' WHERE `node_id` = %s", TBL_PAGES,
                'Privacy Package Renewal (Monthly)', $nodeId)
        );
    }
}

