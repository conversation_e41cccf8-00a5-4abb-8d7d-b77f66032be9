<?php

use Phinx\Migration\AbstractMigration;
use MigrationModule\Dto\Node;
use MigrationModule\Dto\Page;
use MigrationModule\Dto\Property;
use MigrationModule\Helpers\NodeMigrationHelper;

class CreatePrivacyPackageRenewalMonthlyRecurrence extends AbstractMigration
{
    public const PRODUCT_NAME = 'privacy_package_renewal_monthly';

    public function up()
    {
        $helper = new NodeMigrationHelper($this);
        if ($nodeId = $helper->getExistingNodeId(self::PRODUCT_NAME)) {
            return $nodeId;
        }

        $page = new Page('Privacy Package Renewal (Monthly)');

        $properties = [
            new Property('additional', ''),
            new Property('associatedDescription', ''),
            new Property('associatedIconClass', ''),
            new Property('associatedImageId', '1018'),
            new Property('associatedPrice', '29.99'),
            new Property('associatedProducts1', '201,1796'),
            new Property('associatedProducts2', '589,443,782,569,567,301,302,331,299,298,325'),
            new Property('associatedProducts3', '201,1705,567,443,299,298'),
            new Property('associatedText', ''),
            new Property('availableForPayByPhone', 'COMPANY_FORMATION'),
            new Property('bankingEnabled', '1'),
            new Property('bankingOptions', 'BARCLAYS,TSB,TIDE'),
            new Property('bankingRequired', '0'),
            new Property('basketText', 'UK Private Company Limited by Shares. After purchase you will need to enter your company details and submit it for incorporation.'),
            new Property('blacklistedContainedProducts', '165,475'),
            new Property('blacklistedEquivalentProducts', '379,436,1175,1430,1598,400,401'),
            new Property('businessServicesEnabled', '1'),
            new Property('businessServicesOptions', '1,3,4'),
            new Property('cashBackAmount', '55'),
            new Property('conditionedById', ''),
            new Property('disabledLeftColumn', '1'),
            new Property('disabledRightColumn', '1'),
            new Property('duration', '+1 month'),
            new Property('emailCmsFileAttachments', ''),
            new Property('emailText', $this->getEmailText()),
            new Property('includedProducts', '165,202,475'),
            new Property('isAutoRenewalAllowed', '1'),
            new Property('isFeefoEnabled', '1'),
            new Property('isIdCheckRequired', '1'),
            new Property('isInitialProduct', '1'),
            new Property('isInternational', ''),
            new Property('isRenewalProduct', ''),
            new Property('isVoServiceEligible', ''),
            new Property('lockCompany', '0'),
            new Property('markUp', ''),
            new Property('maxQuantityOne', '0'),
            new Property('nonVatableDescription', ''),
            new Property('nonVatableValue', '10'),
            new Property('notApplyVat', '0'),
            new Property('offerOfTheMonthProductId', '331'),
            new Property('offerPrice', '19.99'),
            new Property('offerProductId', '1705'),
            new Property('onlyOneItem', '0'),
            new Property('onlyOurCompanies', '0'),
            new Property('price', ''),
            new Property('printedCertificateOptionEnabled', ''),
            new Property('productValue', '134.94'),
            new Property('registrationReviewEnabled', '1'),
            new Property('removableFromBasket', '1'),
            new Property('removeFromBasketConfirmation', ''),
            new Property('requiredCompanyNumber', '0'),
            new Property('requiredIncorporatedCompanyNumber', '0'),
            new Property('responsibleEmails', ''),
            new Property('sageNominalCode', ''),
            new Property('saveToCompany', ''),
            new Property('serviceTypeId', 'PACKAGE_PRIVACY'),
            new Property('specialPrice', '39.99'),
            new Property('toolkitOfferTypes', 'adwordsVoucher,ebook,facebookGroup,pearl,xeinadin,yell'),
            new Property('typeId', 'BYSHR'),
            new Property('upgradeDescription', $this->getUpgradeDescription()),
            new Property('upgradeImageId', ''),
            new Property('upgradeProductId', '1316'),
            new Property('upgradeText', $this->getUpgradeText()),
            new Property('voServiceDurationInMonths', '0'),
            new Property('wholesalePrice', '19.99')
        ];

        $node = new Node(
            $page,
            $properties,
            self::PRODUCT_NAME,
            1312,
            'CrePackageControler',
            'PackageAdminControler',
            30
        );

        $helper = new NodeMigrationHelper($this);
        $helper->create($node);
    }

    private function getEmailText(): string
    {
        return <<<EOD
<p>Thank you for purchasing the Privacy Package.</p>
<ul>
    <li><strong>Please note that your application is not yet complete. You still need to appoint your director(s) and shareholder(s) for the company and assign a registered office. </strong></li>
</ul>
<p>Please log in to your account to complete your application (if you have not done so already) at <a href="http://www.companiesmadesimple.com/login.html">http://www.companiesmadesimple.com/login.html</a>.</p>
<p><strong>Your package features</strong></p>
<ul>
    <li style="margin-bottom: 8px;">Digital Certificate of Incorporation, Share Certificates and M&amp;As - will be accessible once your company is formed</li>
    <li style="margin-bottom: 8px;">Business bank account with cash back - accessible during the formation process or after your company is formed</li>
    <li style="margin-bottom: 8px;"><a href="https://www.companiesmadesimple.com/page676en.html">29 statutory templates</a> (and once your company is formed you can find your First Board Meeting Minutes by clicking on your company name and scrolling to the very bottom of the page)</li>
    <li style="margin-bottom: 8px;">Your package comes with our registered office and service address for 1 year, which will be pre-populated for you in the registration process.<span style="font-size: 10pt; font-family: Arial; color: rgb(34, 34, 34); background-color: transparent; font-variant-numeric: normal; font-variant-east-asian: normal; vertical-align: baseline; white-space: pre-wrap;">    </span></li>
</ul>
<p>IMPORTANT - With this service we will scan and then email the following mail:</p>
<ul>
    <li>
    <p>Companies House</p>
    </li>
    <li>
    <p>Government Gateway</p>
    </li>
    <li>
    <p>HM Revenue &amp; Customs (HMRC)</p>
    </li>
    <li>
    <p>Court documents (including Tribunals Services)</p>
    </li>
</ul>
<p>If you wish to have all other mail forwarded please purchase a Mail Forwarding Service via <a href="https://www.londonpresence.com/mail-forwarding/?utm_source=purchase+confirmation&amp;utm_medium=email&amp;utm_content=mail">Virtual Office MadeSimple</a>.</p>
EOD;
    }

    private function getUpgradeDescription(): string
    {
        return <<<EOD
<p>&#160;</p>
EOD;
    }

    private function getUpgradeText(): string
    {
        return "Want to protect your residential address from junk mail? Upgrade to Privacy to use our Registered Office. We'll forward on all official government post and keep your address private.";
    }

    public function down()
    {
        $nodeId = $this->getNodeId();
        $this->execute(sprintf("DELETE FROM `%s` WHERE (`node_id` = '%s');", TBL_NODES, $nodeId));
        $this->execute(sprintf("DELETE FROM `%s` WHERE (`node_id` = '%s');", TBL_PAGES, $nodeId));
        $this->execute(sprintf("DELETE FROM `%s` WHERE (`nodeId` = '%s');", TBL_PROPERTIES, $nodeId));
    }

    private function getNodeId()
    {
        $node = $this->fetchRow(sprintf("SELECT node_id FROM %s WHERE `name`='%s'", TBL_NODES, self::PRODUCT_NAME));
        return $node['node_id'] ?? null;
    }
}
