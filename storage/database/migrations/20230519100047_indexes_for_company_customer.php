<?php

use Phinx\Migration\AbstractMigration;

class IndexesForCompanyCustomer extends AbstractMigration
{
    public function up()
    {
        $this->table(TBL_COMPANY_CUSTOMER)
            ->addIndex(['bankTypeId'])
            ->update();
        $this->table('ch_tide_leads')
            ->addIndex(['companyId'])
            ->update();
    }

    public function down()
    {
        $this->table(TBL_COMPANY_CUSTOMER)
            ->removeIndex(['bankTypeId'])
            ->update();
        $this->table('ch_tide_leads')
            ->removeIndex(['companyId'])
            ->update();
    }
}
