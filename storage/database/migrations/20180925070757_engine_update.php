<?php

use Phinx\Migration\AbstractMigration;

class EngineUpdate extends AbstractMigration
{
    public function up()
    {

//ALTER TABLE ch_address_change ENGINE=InnoDB;
//ALTER TABLE ch_annual_return ENGINE=InnoDB;
//ALTER TABLE ch_annual_return_officer ENGINE=InnoDB;
//ALTER TABLE ch_annual_return_shareholder ENGINE=InnoDB;
//ALTER TABLE ch_annual_return_shareholding ENGINE=InnoDB;
//ALTER TABLE ch_annual_return_shares ENGINE=InnoDB;
//ALTER TABLE ch_annual_return_transfer ENGINE=InnoDB;
//ALTER TABLE ch_barclays ENGINE=InnoDB;
//ALTER TABLE ch_barclays_soletrader ENGINE=InnoDB;
//ALTER TABLE ch_card_one ENGINE=InnoDB;
//ALTER TABLE ch_change_accounting_reference_date ENGINE=InnoDB;
//ALTER TABLE ch_change_company_name ENGINE=InnoDB;
//ALTER TABLE ch_company ENGINE=InnoDB;
//ALTER TABLE ch_company_capital ENGINE=InnoDB;
//ALTER TABLE ch_company_capital_shares ENGINE=InnoDB;
//ALTER TABLE ch_company_documents ENGINE=InnoDB;
//ALTER TABLE ch_company_incorporation ENGINE=InnoDB;
//ALTER TABLE ch_company_member ENGINE=InnoDB;
//ALTER TABLE ch_dividend ENGINE=InnoDB;
//ALTER TABLE ch_dividend_share ENGINE=InnoDB;
//ALTER TABLE ch_dividend_shareholder ENGINE=InnoDB;
//ALTER TABLE ch_document ENGINE=InnoDB;
//ALTER TABLE ch_form_submission ENGINE=InnoDB;
//ALTER TABLE ch_form_submission_error ENGINE=InnoDB;
//ALTER TABLE ch_hsbc ENGINE=InnoDB;
//ALTER TABLE ch_incorporation_capital ENGINE=InnoDB;
//ALTER TABLE ch_incorporation_member ENGINE=InnoDB;
//ALTER TABLE ch_officer_appointment ENGINE=InnoDB;
//ALTER TABLE ch_officer_change ENGINE=InnoDB;
//ALTER TABLE ch_officer_resignation ENGINE=InnoDB;
//ALTER TABLE ch_regus ENGINE=InnoDB;
//ALTER TABLE ch_reminder ENGINE=InnoDB;
//ALTER TABLE ch_return_allotment ENGINE=InnoDB;
//ALTER TABLE ch_return_of_allotment_shares ENGINE=InnoDB;
//ALTER TABLE ch_return_shares ENGINE=InnoDB;
//ALTER TABLE cms2_affiliate_products ENGINE=InnoDB;
//ALTER TABLE cms2_affiliates ENGINE=InnoDB;
//ALTER TABLE cms2_annual_return_service ENGINE=InnoDB;
//ALTER TABLE cms2_answers ENGINE=InnoDB;
//ALTER TABLE cms2_barclays_soletrader ENGINE=InnoDB;
//ALTER TABLE cms2_company_customer ENGINE=InnoDB;
//ALTER TABLE cms2_credits_added ENGINE=InnoDB;
//ALTER TABLE cms2_customer_log ENGINE=InnoDB;
//ALTER TABLE cms2_customer_notes ENGINE=InnoDB;
//ALTER TABLE cms2_customer_reviews ENGINE=InnoDB;
//ALTER TABLE cms2_customers ENGINE=InnoDB;
//ALTER TABLE cms2_feefo ENGINE=InnoDB;
//ALTER TABLE cms2_files ENGINE=InnoDB;
//ALTER TABLE cms2_journey_customer_products ENGINE=InnoDB;
//ALTER TABLE cms2_journey_customers ENGINE=InnoDB;
//ALTER TABLE cms2_journey_products_questions ENGINE=InnoDB;
//ALTER TABLE cms2_languages ENGINE=InnoDB;
//ALTER TABLE cms2_mr_site_codes ENGINE=InnoDB;
//ALTER TABLE cms2_nodes ENGINE=InnoDB;
//ALTER TABLE cms2_pages ENGINE=InnoDB;
//ALTER TABLE cms2_properties ENGINE=InnoDB;
//ALTER TABLE cms2_reserved_words ENGINE=InnoDB;
//ALTER TABLE cms2_settings ENGINE=InnoDB;
//ALTER TABLE cms2_signups ENGINE=InnoDB;
//ALTER TABLE cms2_static_texts ENGINE=InnoDB;
//ALTER TABLE cms2_static_texts_langs ENGINE=InnoDB;
//ALTER TABLE cms2_tax_assist_lead_tracking ENGINE=InnoDB;
//ALTER TABLE cms2_users ENGINE=InnoDB;
//ALTER TABLE cms2_users_logs ENGINE=InnoDB;
//ALTER TABLE cms2_users_roles ENGINE=InnoDB;
//ALTER TABLE entities_log ENGINE=InnoDB;
//ALTER TABLE remove_services_table ENGINE=InnoDB;

        $tables = $this->query('show table status where Engine != \'InnoDB\'');
        foreach ($tables as $table) {
            echo $table['Name'] . ',';
            $this->execute(sprintf('ALTER TABLE %s ENGINE=InnoDB', $table['Name']));
        }
    }

    public function down()
    {
        //no going back
    }
}
