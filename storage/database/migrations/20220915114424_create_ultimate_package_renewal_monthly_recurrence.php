<?php

use Phinx\Migration\AbstractMigration;
use MigrationModule\Dto\Node;
use MigrationModule\Dto\Page;
use MigrationModule\Dto\Property;
use MigrationModule\Helpers\NodeMigrationHelper;

class CreateUltimatePackageRenewalMonthlyRecurrence extends AbstractMigration
{
    public const PRODUCT_NAME = 'ultimate_package_renewal_monthly';

    public function up()
    {
        $helper = new NodeMigrationHelper($this);
        if ($nodeId = $helper->getExistingNodeId(self::PRODUCT_NAME)) {
            return $nodeId;
        }

        $page = new Page('Ultimate Package Renewal (Monthly)');

        $properties = [
            new Property('additional', ''),
            new Property('associatedDescription', ''),
            new Property('associatedIconClass', ''),
            new Property('associatedImageId', '1018'),
            new Property('associatedPrice', '99.99'),
            new Property('associatedProducts1', '201,1796'),
            new Property('associatedProducts2', '302,331,589,782,298,172,325'),
            new Property('associatedProducts3', '201,1705,567,298,331'),
            new Property('associatedText', ''),
            new Property('availableForPayByPhone', 'COMPANY_FORMATION'),
            new Property('bankingEnabled', '1'),
            new Property('bankingOptions', 'BARCLAYS,TSB,TIDE'),
            new Property('bankingRequired', ''),
            new Property('basketText', 'UK Private Company Limited by Shares. After purchase you will need to enter your company details and submit it for incorporation.'),
            new Property('blacklistedContainedProducts', '165,475'),
            new Property('blacklistedEquivalentProducts', '379,436,1175,1430,1598,400,401'),
            new Property('businessServicesEnabled', '1'),
            new Property('businessServicesOptions', '1,3,4'),
            new Property('cashBackAmount', '75'),
            new Property('conditionedById', ''),
            new Property('disabledLeftColumn', '1'),
            new Property('disabledRightColumn', '1'),
            new Property('duration', '+1 month'),
            new Property('emailCmsFileAttachments', '1536'),
            new Property('emailText', $this->getEmailText()),
            new Property('includedProducts', '165,299,445,475,1831'),
            new Property('isAutoRenewalAllowed', '1'),
            new Property('isFeefoEnabled', '1'),
            new Property('isIdCheckRequired', '1'),
            new Property('isInitialProduct', '1'),
            new Property('isInternational', ''),
            new Property('isRenewalProduct', ''),
            new Property('isVoServiceEligible', '1'),
            new Property('lockCompany', ''),
            new Property('markUp', ''),
            new Property('maxQuantityOne', ''),
            new Property('nonVatableDescription', ''),
            new Property('nonVatableValue', '10'),
            new Property('notApplyVat', ''),
            new Property('offerOfTheMonthProductId', '331'),
            new Property('offerPrice', '99.99'),
            new Property('offerProductId', '1705'),
            new Property('onlyOneItem', ''),
            new Property('onlyOurCompanies', '0'),
            new Property('price', ''),
            new Property('printedCertificateOptionEnabled', '1'),
            new Property('productValue', '495.99'),
            new Property('registrationReviewEnabled', '1'),
            new Property('removableFromBasket', '1'),
            new Property('removeFromBasketConfirmation', ''),
            new Property('requiredCompanyNumber', '0'),
            new Property('requiredIncorporatedCompanyNumber', '0'),
            new Property('responsibleEmails', ''),
            new Property('sageNominalCode', ''),
            new Property('saveToCompany', ''),
            new Property('serviceTypeId', 'PACKAGE_ULTIMATE'),
            new Property('specialPrice', '0'),
            new Property('toolkitOfferTypes', 'adwordsVoucher,ebook,facebookGroup,pearl,xeinadin,yell'),
            new Property('typeId', 'BYSHR'),
            new Property('upgradeDescription', $this->getUpgradeDescription()),
            new Property('upgradeImageId', ''),
            new Property('upgradeProductId', ''),
            new Property('upgradeText', $this->getUpgradeText()),
            new Property('voServiceDurationInMonths', '1'),
            new Property('wholesalePrice', '99.99'),
        ];

        $node = new Node(
            $page,
            $properties,
            self::PRODUCT_NAME,
            1312,
            'CrePackageControler',
            'PackageAdminControler',
            50
        );

        $helper = new NodeMigrationHelper($this);
        $helper->create($node);
    }

    private function getEmailText(): string
    {
        return <<<EOD
<p>Thank you for purchasing the Ultimate Package.</p>
<ul>
    <li><strong>Please note that your application is not yet complete. You still need to appoint your director(s) and shareholder(s) for the company and assign a registered office. </strong></li>
</ul>
<p>Please log in to  your account to complete your application (if you have not done so already) at <a href="http://www.companiesmadesimple.com/login.html">http://www.companiesmadesimple.com/login.html</a>.</p>
<p>The Ultimate Package you've ordered includes the VAT Registration Assistance service. &#160;To check if you need to register for VAT, please visit: https://www.gov.uk/vat-registration/when-to-register</p>
<p><b>For the VAT Registration Assistance Service please complete the online form at the following link:</b></p>
<p>https://goo.gl/forms/sZVZWlXgltrkn4vf1</p>
<p>Please complete fully and submit it online to us so we can review accordingly. The VAT Guidance Notes are also attached to help you answer the questions on the VAT form.&#160;</p>
<p><strong>Important note: </strong>Please do not provide our address as the Business Address, as virtual office and registered office addresses are not acceptable.&#160;</p>
<p><strong>Non UK residents</strong> must provide one piece of primary evidence which consists of a copy of government issued photo identification which can include:</p>
<ul>
    <li>passport</li>
    <li>photo drivers licence</li>
    <li>national identity card</li>
</ul>
<p>You must also provide two additional pieces of evidence which can be copies of:</p>
<ul>
    <li>a mortgage statement</li>
    <li>a lease/rental agreement</li>
    <li>a birth certificate</li>
    <li>a marriage or civil partnership certificate</li>
    <li>a decree absolute or decree of dissolution certificate</li>
    <li>an official document from an employer that contains your name, date of birth and Tax Identification Number</li>
</ul>
<p>Once we have received and checked over your completed VAT form, we will submit your application online. We will then e-mail you HMRC's Acknowledgement Reference Number as confirmation of submission.</p>
<p>It generally takes 4-6 weeks for HMRC to process the application. If you have any questions regarding this service, please feel free to get in touch.</p>
<p><strong>Your package features</strong></p>
<ul>
    <li style="margin-bottom: 8px;">Digital Certificate of Incorporation, Share Certificates and M&amp;As - will be accessible once your company is formed</li>
    <li style="margin-bottom: 8px;">Business bank account with cash back - accessible during the formation process or after your company is formed</li>
    <li style="margin-bottom: 8px;"><a href="https://www.companiesmadesimple.com/page676en.html">29 statutory templates</a> (and once your company is formed you can find your First Board Meeting Minutes by clicking on your company name and scrolling to the very bottom of the page)</li>
    <li style="margin-bottom: 8px;">Your printed documents will be sent out within 5 working days of your company being formed.</li>
    <li style="margin-bottom: 8px;">Your package comes with our registered office and service address for 1 year, which will be pre-populated for you in the registration process.</li>
    <li style="margin-bottom: 8px;">Your company's annual return will be prepared by us towards the end of the year and we will contact you once it's done.</li>
    <li style="margin-bottom: 8px;">Virtual Office Mail Forwarding - Will be set up within 48 hours and you will be emailed directly from our sister site londonpresence.com to confirm the setup.</li>
</ul>
<p>If you have any questions, please feel free to get in touch.</p>
EOD;
    }

    private function getUpgradeDescription(): string
    {
        return <<<EOD
<p>&#160;</p>
EOD;
    }

    private function getUpgradeText(): string
    {
        return "Want complete peace of mind? Upgrade to Ultimate and we'll register your company for VAT. We'll also give your company a prestigious London based mail forwarding address.";
    }

    public function down()
    {
        $nodeId = $this->getNodeId();
        $this->execute(sprintf("DELETE FROM `%s` WHERE (`node_id` = '%s');", TBL_NODES, $nodeId));
        $this->execute(sprintf("DELETE FROM `%s` WHERE (`node_id` = '%s');", TBL_PAGES, $nodeId));
        $this->execute(sprintf("DELETE FROM `%s` WHERE (`nodeId` = '%s');", TBL_PROPERTIES, $nodeId));
    }

    private function getNodeId()
    {
        $node = $this->fetchRow(sprintf("SELECT node_id FROM %s WHERE `name`='%s'", TBL_NODES, self::PRODUCT_NAME));
        return $node['node_id'] ?? null;
    }
}
