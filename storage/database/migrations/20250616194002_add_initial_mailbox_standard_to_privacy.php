<?php

use Entities\Service;
use MigrationModule\Dto\Node;
use MigrationModule\Dto\Page;
use MigrationModule\Dto\Property;
use MigrationModule\Helpers\NodeMigrationHelper;
use Models\Products\Package;
use Models\Products\Product;
use Phinx\Migration\AbstractMigration;

class AddInitialMailboxStandardToPrivacy extends AbstractMigration
{

    private const ASSOCIATED_PRODUCTS_1 = 'associatedProducts1';

    public function up()
    {
        $helper = new NodeMigrationHelper($this);

        if ($nodeId = $helper->getExistingNodeId(Product::PRODUCT_MAILBOX_STANDARD_PRIVACY_INITIAL_1_MONTH)) {
            return $nodeId;
        }

        $page = new Page('Mailbox Standard Privacy Initial (1 Month)');

        $renewalNodeId = $helper->getExistingNodeId(Product::PRODUCT_MAILBOX_STANDARD_RENEWAL);

        $properties = [
            new Property('address1', '20'),
            new Property('address2', 'Wenlock Road'),
            new Property('address3', ''),
            new Property('additional', ''),
            new Property('associatedDescription', "The registered office is the official address of a limited company and must be a physical location in the UK. Our service is ideal if you want to protect your residential address from being shown on the public register or if you're an overseas customer who needs a UK address. NB. This is not a full mail forwarding service. Only mail from Companies House, HMRC and other Government bodies will be forwarded (this mail is forwarded to you free of charge)"),
            new Property('associatedIconClass', 'fa-building'),
            new Property('associatedImageId', '1187'),
            new Property('associatedPrice', '0'),
            new Property('associatedProducts3', ''),
            new Property('associatedText', "Use our prestigious N1 London address as your company's registered office and keep your residential address off the public register."),
            new Property('availableForPayByPhone', 'NOT_AVAILABLE'),
            new Property('bankingEnabled', ''),
            new Property('bankingOptions', ''),
            new Property('bankingRequired', ''),
            new Property('basketText', ''),
            new Property('blacklistedContainedProducts', ''),
            new Property('blacklistedEquivalentProducts', implode(',', $this->getEquivalentProducts($helper))),
            new Property('cashBackAmount', '0'),
            new Property('conditionedById', ''),
            new Property('countryId', 'GB-ENG'),
            new Property('county', ''),
            new Property('customAssociatedIcon', ''),
            new Property('duration', '+1 month'),
            new Property('emailCmsFileAttachments', '4'),
            new Property('emailText', $this->getEmailText()),
            new Property('isAutoRenewalAllowed', '1'),
            new Property('isFeefoEnabled', ''),
            new Property('isIdCheckRequired', '1'),
            new Property('isInitialProduct', '1'),
            new Property('isRenewalProduct', ''),
            new Property('isVoServiceEligible', ''),
            new Property('lockCompany', ''),
            new Property('markUp', ''),
            new Property('maxQuantityOne', ''),
            new Property('nonVatableDescription', ''),
            new Property('nonVatableValue', ''),
            new Property('notApplyVat', ''),
            new Property('offerPrice', '0'),
            new Property('offerProductId', ''),
            new Property('onlyOne', '0'),
            new Property('onlyOneItem', ''),
            new Property('onlyOurCompanies', '0'),
            new Property('optionalRenewalProductId', ''),
            new Property('postcode', 'N1 7GU'),
            new Property('price', '0'),
            new Property('printedCertificateOptionEnabled', ''),
            new Property('productValue', '0'),
            new Property('registrationReviewEnabled', ''),
            new Property('removableFromBasket', '0'),
            new Property('removeFromBasketConfirmation', ''),
            new Property('renewalDtStartBehavior', ''),
            new Property('renewalProductId', $renewalNodeId),
            new Property('requiredCompanyNumber', '1'),
            new Property('requiredCorePackage', ''),
            new Property('requiredIncorporatedCompanyNumber', '0'),
            new Property('responsibleEmails', ''),
            new Property('sageNominalCode', ''),
            new Property('saveToCompany', '0'),
            new Property('serviceTypeId', Service::TYPE_MAILBOX_STANDARD),
            new Property('showInMyServicesPage', ''),
            new Property('specialPrice', '0'),
            new Property('toolkitOfferTypes', ''),
            new Property('town', 'London'),
            new Property('upgradeDescription', $this->getUpgradeDescription()),
            new Property('upgradeImageId', '425'),
            new Property('voServiceDurationInMonths', '0'),
            new Property('wholesalePrice', '0'),
        ];

        $node = new Node(
            $page,
            $properties,
            Product::PRODUCT_MAILBOX_STANDARD_PRIVACY_INITIAL_1_MONTH,
            164,
            'DefaultControler',
            'RegisterOfficeAdminControler',
            110
        );

        $node->setExcludeFromSitemap(false);
        $helper = new NodeMigrationHelper($this);
        $createdNodeId = $helper->create($node);

        $helper->updateProperty(
            $helper->getExistingNodeId((string) Package::PACKAGE_PRIVACY),
            self::ASSOCIATED_PRODUCTS_1,
            sprintf(
                '%s,%s',
                $helper->getPropertyFromExistingNodeId(
                    self::ASSOCIATED_PRODUCTS_1,
                    $helper->getExistingNodeId((string) Package::PACKAGE_PRIVACY)
                ),
                $createdNodeId
            )
        );
    }

    public function down()
    {
        $helper = new NodeMigrationHelper($this);
        $nodeId = $helper->getExistingNodeId(Product::PRODUCT_MAILBOX_STANDARD_PRIVACY_INITIAL_1_MONTH);

        $this->execute(sprintf("DELETE FROM `%s` WHERE (`node_id` = '%s');", TBL_NODES, $nodeId));
        $this->execute(sprintf("DELETE FROM `%s` WHERE (`node_id` = '%s');", TBL_PAGES, $nodeId));
        $this->execute(sprintf("DELETE FROM `%s` WHERE (`nodeId` = '%s');", TBL_PROPERTIES, $nodeId));

        $helper->updateProperty(
            $helper->getExistingNodeId((string) Package::PACKAGE_ULTIMATE),
            self::ASSOCIATED_PRODUCTS_1,
            str_replace(
                sprintf(',%s', $nodeId),
                '',
                $helper->getPropertyFromExistingNodeId(
                    self::ASSOCIATED_PRODUCTS_1,
                    $helper->getExistingNodeId((string) Package::PACKAGE_ULTIMATE)
                )
            )
        );
    }

    public function getEquivalentProducts(NodeMigrationHelper $helper): array
    {
        return [
            $helper->getExistingNodeId((string) Product::PRODUCT_REGISTERED_OFFICE),
            $helper->getExistingNodeId(Product::PRODUCT_REGISTERED_OFFICE_BASKET_UPSELL),
            $helper->getExistingNodeId(Product::PRODUCT_REGISTERED_OFFICE_N1_MONTHLY),
            $helper->getExistingNodeId(Product::PRODUCT_REGISTERED_OFFICE_N_1_MONTHLY_RENEWAL_FEE),
            $helper->getExistingNodeId((string) Product::PRODUCT_RENEWAL_REGISTERED_OFFICE),
            $helper->getExistingNodeId((string) Product::PRODUCT_REGISTERED_OFFICE_SERVICE),
            $helper->getExistingNodeId((string) Product::PRODUCT_REGISTERED_OFFICE_SERVICE_ADDRESS_BUNDLE),

            $helper->getExistingNodeId(Product::PRODUCT_MAILBOX_STANDARD_INITIAL),

            $helper->getExistingNodeId((string) Package::PACKAGE_FULL_PRIVACY),
            $helper->getExistingNodeId(Package::PACKAGE_FULL_PRIVACY_MONTHLY_8_2023),
            $helper->getExistingNodeId(Package::PACKAGE_FULL_PRIVACY_MONTHLY_REGULAR_PRICE),
            $helper->getExistingNodeId(Package::PACKAGE_FULL_PRIVACY_RENEWAL_MONTHLY_8_2023),
        ];
    }

    private function getEmailText(): string
    {
        return <<<EOD
This is a 'Mailbox Standard Privacy Initial (1 Month)' placeholder for the email text
EOD;
    }

    private function getUpgradeDescription(): string
    {
        return <<<EOD
This is a 'Mailbox Standard Privacy Initial (1 Month)' placeholder for the upgrade description text
EOD;
    }
}
