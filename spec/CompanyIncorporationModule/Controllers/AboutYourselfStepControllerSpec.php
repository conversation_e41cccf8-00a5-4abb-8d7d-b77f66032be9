<?php

declare(strict_types=1);

namespace spec\CompanyIncorporationModule\Controllers;

use CompanyFormationModule\Providers\StepProvider;
use CompanyIncorporationModule\Controllers\AboutYourselfStepController;
use CompanyIncorporationModule\Dto\Enum\Step;
use CompanyIncorporationModule\Dto\Enum\SubStep;
use CompanyIncorporationModule\Helpers\CustomerIdDetailsHelper;
use CompanyIncorporationModule\Services\IncorporationProcessLogService;
use CompanyIncorporationModule\Services\StepService;
use CompanyIncorporationModule\Services\StepUrlService;
use CustomerModule\Dto\CustomerIdDetails;
use Entities\Company;
use Entities\Customer;
use FrontModule\controlers\CompaniesCustomerControler;
use PhpSpec\ObjectBehavior;
use PhpSpec\Wrapper\Collaborator;
use Prophecy\Argument;
use Psr\Log\LoggerInterface;
use RouterModule\Helpers\IControllerHelper;
use RouterModule\Helpers\IMessageHelper;
use Services\EventService;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Intl\Countries;
use TemplateModule\Renderers\IRenderer;
use UserModule\Helpers\HowHeardOptions;
use UserModule\Helpers\IndustryOptions;

class AboutYourselfStepControllerSpec extends ObjectBehavior
{
    private Collaborator|IRenderer $renderer;
    private Collaborator|IControllerHelper $controllerHelper;
    private Collaborator|LoggerInterface $logger;
    private Collaborator|EventService $eventService;
    private Collaborator|StepService $stepService;
    private Collaborator|StepUrlService $stepUrlService;
    private Collaborator|IncorporationProcessLogService $incorporationProcessLogService;
    private Collaborator|CustomerIdDetailsHelper $customerIdDetailsHelper;

    public function let(
        IRenderer $renderer,
        IControllerHelper $controllerHelper,
        LoggerInterface $logger,
        EventService $eventService,
        StepService $stepService,
        StepUrlService $stepUrlService,
        IncorporationProcessLogService $incorporationProcessLogService,
        CustomerIdDetailsHelper $customerIdDetailsHelper,
    ) {
        $this->renderer = $renderer;
        $this->controllerHelper = $controllerHelper;
        $this->logger = $logger;
        $this->eventService = $eventService;
        $this->stepService = $stepService;
        $this->stepUrlService = $stepUrlService;
        $this->incorporationProcessLogService = $incorporationProcessLogService;
        $this->customerIdDetailsHelper = $customerIdDetailsHelper;

        $this->beConstructedWith(
            $renderer,
            $controllerHelper,
            $logger,
            $eventService,
            $stepService,
            $stepUrlService,
            $incorporationProcessLogService,
            $customerIdDetailsHelper
        );
    }

    public function it_is_initializable()
    {
        $this->shouldHaveType(AboutYourselfStepController::class);
    }

    public function it_renders_the_about_yourself_page_successfully(
        Company $company,
        Response $response,
    ) {
        $companyId = 1;
        $company->getId()->willReturn($companyId);
        $company->getSimplifiedCompanyData()->willReturn(['company_name' => 'Test Company']);
        $customer = new Customer('<EMAIL>', 'mypassword');
        $company->getCustomer()->willReturn($customer);
        $this->customerIdDetailsHelper->fromCustomer($customer)->willReturn(new CustomerIdDetails());

        $this->stepService->getTabSteps($company)->willReturn([]);
        $tabSteps = ['tab1', 'tab2'];
        $this->stepService->getTabs($company)->willReturn($tabSteps);
        $this->stepService->getSerializedCurrentStep($tabSteps, StepProvider::STEP_ABOUT_YOURSELF)->willReturn(['serializedCurrentStep']);
        $this->stepUrlService->getAboutYourselfUrl($companyId)->willReturn('/about-yourself');
        $this->eventService->notifyPreventDuplicationCached(Argument::any(), $companyId)->shouldBeCalled();

        $renderData = [
            'company' => ['company_name' => 'Test Company'],
            'customerDetails' => new CustomerIdDetails(),
            'titles' => Customer::$titles,
            'countries' => Countries::getNames(),
            'industryChoices' => IndustryOptions::getIndustryOptions(),
            'howHeardChoices' => HowHeardOptions::getHowHeardOptions(),
            'tabSteps' => [],
            'serializedCurrentStep' => ['serializedCurrentStep'],
            'seo' => ['title' => 'Account Holder'],
        ];

        $this->incorporationProcessLogService->create(
            $company,
            true,
            Step::COMPANY_INCORPORATION_ABOUT_YOURSELF,
            SubStep::NO_SUB_STEP,
            '/about-yourself',
            $renderData
        )->shouldBeCalled();

        $this->renderer->render($renderData)->willReturn($response);

        $this->index($company)->shouldReturn($response);
    }

    public function it_handles_exceptions_and_redirects(
        Company $company,
        Response $response,
    ) {
        $companyId = 1;
        $company->getId()->willReturn($companyId);

        $e = new \Exception('Error');
        $this->eventService->notifyPreventDuplicationCached(Argument::any(), $companyId)->willThrow($e);

        $this->logger->error('Unable to access the Account Holder Information page', ['exception' => $e])->shouldBeCalled();
        $this->controllerHelper->setFlashMessage($e->getMessage(), IMessageHelper::MESSAGE_ERROR)->shouldBeCalled();
        $this->controllerHelper->redirectionTo(CompaniesCustomerControler::COMPANIES_PAGE)->willReturn($response);
        $this->index($company)->shouldReturn($response);
    }
}
