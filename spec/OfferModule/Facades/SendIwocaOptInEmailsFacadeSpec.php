<?php

namespace spec\OfferModule\Facades;

use EmailModule\IEmail;
use EmailModule\IEmailGateway;
use EmailModule\IEmailLog;
use EmailModule\Loaders\IEmailLoader;
use EmailModule\Repositories\ITemplateEmailRepository;
use Entities\Customer;
use OfferModule\Entities\CustomerDetails;
use OfferModule\Facades\SendIwocaOptInEmailsFacade;
use PhpSpec\ObjectBehavior;
use Utils\File;

class SendIwocaOptInEmailsFacadeSpec extends ObjectBehavior
{
    /**
     * @var IEmailGateway
     */
    private $emailGateway;


    /**
     * @var ITemplateEmailRepository
     */
    private $emailRepository;

    /**
     * @var IEmailLoader
     */
    private $emailLoader;

    private static $details = [
        'id' => 1,
        'email' => '<EMAIL>',
        'subject' => 'classified',
        'first_name' => 'James',
        'template_name' => 'templateName'
    ];

    public function let(
        IEmailGateway $emailGateway,
        ITemplateEmailRepository $emailRepository,
        IEmailLoader $emailLoader
    )
    {
        $this->emailGateway = $emailGateway;
        $this->emailRepository = $emailRepository;
        $this->emailLoader = $emailLoader;

        $this->beConstructedWith($this->emailGateway, $this->emailRepository, $this->emailLoader);
    }

    function it_is_initializable()
    {
        $this->shouldHaveType(SendIwocaOptInEmailsFacade::class);
    }

    public function it_should_send_email(CustomerDetails $details, IEmail $email, IEmailLog $emailLog, Customer $customer)
    {
        $details->getId()->willReturn(self::$details['id']);
        $details->getEmail()->willReturn(self::$details['email']);
        $details->getFirstName()->willReturn(self::$details['first_name']);
        $details->getCustomer()->willReturn($customer);
        $email->getSubject()->willReturn(self::$details['subject']);
        $email->setTo(self::$details['email'])->shouldBeCalled();

        $this->emailLoader->getHtmlEmailById(
            SendIwocaOptInEmailsFacade::EMAIL_NAME,
            File::fromExistingPath(EMAIL_DIR . '/cms/Offers/iwoca.html'),
            [
                'firstName' => self::$details['first_name']
            ]
        )->willReturn($email);

        $this->emailGateway->send($email, $customer)->willReturn($emailLog);

        $this->sendEmail($details)->shouldBe($emailLog);
    }
}
