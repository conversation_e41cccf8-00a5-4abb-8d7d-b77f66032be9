<?php

namespace spec\PeopleWithSignificantControlModule\FormSubmissions\BaseTypes\ChangeDetails\PersonTypes;

use CompaniesHouseModule\FormSubmissions\BaseTypes\ServiceAddressFactory;
use Libs\CHFiling\Core\UtilityClass\Corporate;
use PeopleWithSignificantControlModule\FormSubmissions\BaseTypes\ChangeDetails\PersonTypes\LegalPersonFactory;
use PhpSpec\ObjectBehavior;
use Entities\Company;
use Libs\CHFiling\Core\UtilityClass\LegalPersonPsc;
use PeopleWithSignificantControl\Forms\Old\LegalPersonPscData;
use PeopleWithSignificantControl\Forms\Old\Address;

class LegalPersonFactorySpec extends ObjectBehavior
{
    /**
     * @var ServiceAddressFactory
     */
    private $serviceAddressFactory;

    function let(ServiceAddressFactory $serviceAddressFactory)
    {
        $this->serviceAddressFactory = $serviceAddressFactory;
        $this->beConstructedWith($serviceAddressFactory);
    }

    function it_is_initializable()
    {
        $this->shouldHaveType(LegalPersonFactory::class);
    }

    function it_should_create_legal_person(Company $company, LegalPersonPsc $psc, LegalPersonPscData $data, Address $address)
    {
        $corporate = new Corporate();
        $corporate->setFields(
            [
                'corporate_name' => 'corporate name',
            ]
        );

        $psc->getCorporate()->willReturn($corporate);

        $data->isChangePerson()->willReturn(TRUE);
        $data->isChangeAddress()->willReturn(TRUE);
        $data->isChangeNatureOfControl()->willReturn(TRUE);
        $data->getLegalPersonName()->willReturn('legal person name');
        $data->getServiceAddress()->willReturn($address);
        $data->getGoverningLaw()->willReturn('governing law');
        $data->getLegalForm()->willReturn('legal form');
        $data->getNatureOfControls()->willReturn(['nature of controls']);

        $address->getPremise()->willReturn('premise');
        $address->getStreet()->willReturn('street');
        $address->getThoroughfare()->willReturn('thoroughfare');
        $address->getPostTown()->willReturn('post town');
        $address->getCounty()->willReturn('county');
        $address->getPostcode()->willReturn('post code');
        $address->isNonForeignCountry()->willReturn(TRUE);
        $address->getCountry()->willReturn('country');


        $this->createOld($company, $psc, $data);
    }
}
