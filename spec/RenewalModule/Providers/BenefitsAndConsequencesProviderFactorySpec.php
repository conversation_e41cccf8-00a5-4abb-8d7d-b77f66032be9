<?php

namespace spec\RenewalModule\Providers;

use Nette\InvalidArgumentException;
use RenewalModule\Providers\BenefitsAndConsequencesProvider;
use RenewalModule\Providers\BenefitsAndConsequencesProviderFactory;
use PhpSpec\ObjectBehavior;
use Prophecy\Argument;
use Symfony\Component\Yaml\Parser;
use Utils\File;

class BenefitsAndConsequencesProviderFactorySpec extends ObjectBehavior
{
    private $yamlParser;

    function let(Parser $yamlParser)
    {
        $this->yamlParser = $yamlParser;
        $this->beConstructedWith($yamlParser);
    }

    function it_is_initializable()
    {
        $this->shouldHaveType(BenefitsAndConsequencesProviderFactory::class);
    }

    function it_shoud_throw_exception_when_keys_not_existing(File $file)
    {
        $content = 'test';
        $file->getContent()->willReturn($content);
        $this->yamlParser->parse($content)->willReturn([]);
        $this->shouldThrow(InvalidArgumentException::class)->during('create', [$file]);
    }

    function it_shoud_create_benefits_and_consequences_provider(File $file)
    {
        $content = 'test';
        $file->getContent()->willReturn($content);
        $this->yamlParser->parse($content)->willReturn(['benefits' => [], 'consequences' => []]);
        $this->create($file)->shouldBeAnInstanceOf(BenefitsAndConsequencesProvider::class);
    }
}
