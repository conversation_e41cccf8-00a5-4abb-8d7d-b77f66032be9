<?php

namespace spec\CashplusModule\Factories;

use CashplusModule\Factories\ProductInfoFactory;
use CashplusModule\Models\ProductInfo;
use PhpSpec\ObjectBehavior;
use Prophecy\Argument;
use Utils\Exceptions\InvalidArgumentException;
use Utils\Helpers\ArrayHelper;

class ProductInfoFactorySpec extends ObjectBehavior
{
    function it_is_initializable()
    {
        $this->shouldHaveType(ProductInfoFactory::class);
    }
    
    function it_should_create_product_info()
    {
        $data = [
            'id' => 1,
            'productSegment' => 'segment',
            'name' => 'name',
            'description' => 'desc',
            'feePaymentSchedule' => ['name' => 'paymentSchedule'],
            'termsAndConditions' => [['tCsReferenceNumber' => 123, 'url' => 'hello boi']]
        ];
        
        /** @var ProductInfo $object */
        $object = $this->createFromApiData($data);
        
        $object->getId()->shouldBe(1);
        $object->getProductSegment()->shouldBe('segment');
        $object->getName()->shouldBe('name');
        $object->getDescription()->shouldBe('desc');
        $object->getFeePaymentSchedule()->shouldBe('paymentSchedule');
        $object->getTCsReferenceNumber()->shouldBe(123);
        $object->getUrl()->shouldBe('hello boi');
    }
    
    function it_should_fail_if_some_value_is_not_present()
    {
        $data = [
            'id' => 1,
            'productSegment' => 'segment',
            'name' => 'name',
            'feePaymentSchedule' => ['name' => 'paymentSchedule'],
            'termsAndConditions' => [['tCsReferenceNumber' => 123, 'url' => 'hello boi']]
        ];
        
        /** @var ProductInfo $object */
        $this->shouldThrow(InvalidArgumentException::class)->during('createFromApiData', [$data]);
    }
}
