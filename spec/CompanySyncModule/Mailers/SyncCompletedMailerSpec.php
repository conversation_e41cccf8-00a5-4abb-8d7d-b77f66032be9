<?php

namespace spec\CompanySyncModule\Mailers;

use CompanySyncModule\Entities\CompaniesSync;
use CompanySyncModule\Mailers\SyncCompletedMailer;
use EmailModule\IEmailGateway;
use EmailModule\Loaders\IEmailLoader;
use Framework\FEmail;
use PhpSpec\ObjectBehavior;
use Repositories\CustomerRepository;
use UserModule\Contracts\ICustomer;
use Utils\File;

class SyncCompletedMailerSpec extends ObjectBehavior
{
    /**
     * @var IEmailGateway
     */
    private $emailGateway;

    /**
     * @var CustomerRepository
     */
    private $customerRepository;

    /**
     * @var IEmailLoader
     */
    private $emailLoader;

    function let(
        IEmailGateway $emailGateway,
        CustomerRepository $customerRepository,
        IEmailLoader $emailLoader
    )
    {
        $this->emailGateway = $emailGateway;
        $this->customerRepository = $customerRepository;
        $this->emailLoader = $emailLoader;

        $this->beConstructedWith($emailGateway, $customerRepository, $emailLoader);
    }

    function it_is_initializable()
    {
        $this->shouldHaveType(SyncCompletedMailer::class);
    }

    function it_should_notify_customer(CompaniesSync $companiesSync, FEmail $email, ICustomer $customer)
    {
        $customerEmail = TEST_EMAIL1;
        $firstName = 'first name';

        $companiesSync->getCustomerEmail()->willReturn($customerEmail);
        $companiesSync->getCustomerFirstName()->willReturn($firstName);
        $this->emailLoader->getHtmlEmailById(
            SyncCompletedMailer::EMAIL_CUSTOMER,
            File::fromExistingPath(
                sprintf('%s%s', EMAIL_DIR, '/cms/Customer/sync-all-complete-customer.html')
            ),
            [
                'firstName' => $firstName
            ]
        )->willReturn($email);

        $email->addTo($customerEmail)->shouldBeCalled();
        $email->setName('Sync all complete - customer')->shouldBeCalled();
        $this->customerRepository->requiredCustomerByEmail($customerEmail)->willReturn($customer);
        $this->emailGateway->send($email, $customer)->shouldBeCalled();

        $this->notifyCustomer($companiesSync);
    }

    function it_should_notify_admin(CompaniesSync $companiesSync, FEmail $email, ICustomer $customer)
    {
        $adminEmail = TEST_EMAIL2;
        $adminFirstName = 'admin first name';
        $customerId = 1;
        $customerEmail = TEST_EMAIL1;

        $companiesSync->getAdminFirstName()->willReturn($adminFirstName);
        $companiesSync->getCustomerEmail()->willReturn($customerEmail);
        $companiesSync->getCustomerId()->willReturn($customerId);

        $this->emailLoader->getHtmlEmailById(
            SyncCompletedMailer::EMAIL_ADMIN,
            File::fromExistingPath(
                sprintf('%s%s', EMAIL_DIR, '/cms/Misc/sync-all-complete-admin.html')
            ),
            [
                'staffFirstName' => $adminFirstName,
                'customerEmail' => $customerEmail,
                'customerId' => $customerId
            ]
        )->willReturn($email);

        $companiesSync->getAdminEmail()->willReturn($adminEmail);
        $email->setName('Sync all complete - admin')->shouldBeCalled();
        $email->addTo($adminEmail)->shouldBeCalled();
        $this->customerRepository->requiredCustomerByEmail($customerEmail)->willReturn($customer);
        $email->setSubject('All companies synced for ' . $customerEmail)->shouldBeCalled();
        $this->emailGateway->send($email, $customer)->shouldBeCalled();

        $this->notifyAdmin($companiesSync);
    }
}
