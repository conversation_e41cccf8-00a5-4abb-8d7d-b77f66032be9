<?php

namespace spec\BusinessServicesModule\Helpers;

use BusinessServicesModule\Entities\Lead;
use BusinessServicesModule\Helpers\LeadsHelper;
use BusinessServicesModule\Repositories\ArrayOfferRepository;
use PhpSpec\ObjectBehavior;

class LeadsHelperSpec extends ObjectBehavior
{
    /**
     * @var ArrayOfferRepository
     */
    private $arrayOfferRepository;

    public function let(ArrayOfferRepository $arrayOfferRepository)
    {
        $this->arrayOfferRepository = $arrayOfferRepository;

        $this->beConstructedWith($arrayOfferRepository);
    }

    function it_is_initializable()
    {
        $this->shouldHaveType(LeadsHelper::class);
    }

    function it_should_return_processed_leads(Lead $lead1, Lead $lead2, Lead $lead3)
    {
        $leads = [$lead1, $lead2, $lead3];

        $lead1->isProcessed()->willReturn(TRUE);
        $lead2->isProcessed()->willReturn(TRUE);
        $lead3->isProcessed()->willReturn(FALSE);

        $this->getProcessed($leads)->shouldBe([$lead1, $lead2]);

        $lead1->isProcessed()->willReturn(FALSE);
        $lead2->isProcessed()->willReturn(TRUE);
        $lead3->isProcessed()->willReturn(FALSE);

        $this->getProcessed($leads)->shouldBe([$lead2]);

        $lead1->isProcessed()->willReturn(FALSE);
        $lead2->isProcessed()->willReturn(FALSE);
        $lead3->isProcessed()->willReturn(TRUE);

        $this->getProcessed($leads)->shouldBe([$lead3]);

        $lead1->isProcessed()->willReturn(TRUE);
        $lead2->isProcessed()->willReturn(TRUE);
        $lead3->isProcessed()->willReturn(TRUE);

        $this->getProcessed($leads)->shouldBe($leads);

        $lead1->isProcessed()->willReturn(FALSE);
        $lead2->isProcessed()->willReturn(FALSE);
        $lead3->isProcessed()->willReturn(FALSE);

        $this->getProcessed($leads)->shouldBe([]);
    }

    function it_should_filter_processed_leads(Lead $lead1, Lead $lead2, Lead $lead3)
    {
        $leads = [$lead1, $lead2, $lead3];

        $lead1->isProcessed()->willReturn(TRUE);
        $lead2->isProcessed()->willReturn(TRUE);
        $lead3->isProcessed()->willReturn(FALSE);

        $this->filterProcessed($leads)->shouldBe([$lead3]);

        $lead1->isProcessed()->willReturn(FALSE);
        $lead2->isProcessed()->willReturn(TRUE);
        $lead3->isProcessed()->willReturn(FALSE);

        $this->filterProcessed($leads)->shouldBe([$lead1, $lead3]);

        $lead1->isProcessed()->willReturn(FALSE);
        $lead2->isProcessed()->willReturn(FALSE);
        $lead3->isProcessed()->willReturn(TRUE);

        $this->filterProcessed($leads)->shouldBe([$lead1, $lead2]);

        $lead1->isProcessed()->willReturn(TRUE);
        $lead2->isProcessed()->willReturn(TRUE);
        $lead3->isProcessed()->willReturn(TRUE);

        $this->filterProcessed($leads)->shouldBe([]);

        $lead1->isProcessed()->willReturn(FALSE);
        $lead2->isProcessed()->willReturn(FALSE);
        $lead3->isProcessed()->willReturn(FALSE);

        $this->filterProcessed($leads)->shouldBe($leads);
    }

    function it_should_return_offer_ids(Lead $lead1, Lead $lead2)
    {
        $lead1->getOfferId()->willReturn(1);
        $lead2->getOfferId()->willReturn(2);

        $this->getOfferIds([$lead1, $lead2])->shouldBe([1, 2]);
    }

    function it_should_return_offer_ids_of_new_leads(Lead $lead1, Lead $lead2, Lead $lead3)
    {
        $leads = [$lead1, $lead2, $lead3];

        $lead1->isProcessed()->willReturn(TRUE);
        $lead2->isProcessed()->willReturn(FALSE);
        $lead3->isProcessed()->willReturn(FALSE);

        $lead1->getOfferId()->willReturn(1);
        $lead2->getOfferId()->willReturn(2);
        $lead3->getOfferId()->willReturn(3);

        $this->getNewOfferIds($leads, [1, 2, 3])->shouldBeLike([2, 3]);
    }

    function it_should_return_offer_id(Lead $lead)
    {
        $lead->getOfferId()->willReturn(1);

        $this->getOfferId($lead)->shouldBe(1);
    }
}
