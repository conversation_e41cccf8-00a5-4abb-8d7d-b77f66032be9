<?php

namespace spec\BusinessServicesModule\Facades;

use BusinessServicesModule\Deciders\OfferAvailabilityDecider;
use BusinessServicesModule\Entities\Category;
use BusinessServicesModule\Entities\Lead;
use BusinessServicesModule\Entities\Offer;
use BusinessServicesModule\Facades\SelectOffersFacade;
use BusinessServicesModule\Factories\LeadsFactory;
use BusinessServicesModule\Helpers\LeadsHelper;
use BusinessServicesModule\Providers\IOfferProvider;
use BusinessServicesModule\Repositories\ArrayCategoryRepository;
use BusinessServicesModule\Repositories\ArrayOfferRepository;
use BusinessServicesModule\Repositories\LeadRepository;
use Entities\Company;
use Entities\Event;
use EventDetailModule\Services\EventDetailService;
use PhpSpec\ObjectBehavior;
use Services\EventService;

class SelectOffersFacadeSpec extends ObjectBehavior
{
    /**
     * @var LeadRepository
     */
    private $leadRepository;

    /**
     * @var IOfferProvider
     */
    private $offerProvider;

    /**
     * @var OfferAvailabilityDecider
     */
    private $offerAvailabilityDecider;

    /**
     * @var LeadsHelper
     */
    private $leadsHelper;

    /**
     * @var LeadsFactory
     */
    private $leadsFactory;

    /**
     * @var ArrayOfferRepository
     */
    private $arrayOfferRepository;

    /**
     * @var ArrayCategoryRepository
     */
    private $arrayCategoryRepository;

    /**
     * @var EventService
     */
    private $eventService;

    /**
     * @var EventDetailService
     */
    private $eventDetailService;

    public function let(
        LeadRepository $leadRepository,
        IOfferProvider $offerProvider,
        OfferAvailabilityDecider $categoryAvailability,
        LeadsHelper $leadsHelper,
        LeadsFactory $leadsFactory,
        ArrayOfferRepository $arrayOfferRepository,
        ArrayCategoryRepository $arrayCategoryRepository,
        EventService $eventService,
        EventDetailService $eventDetailService
    ) {
        $this->leadRepository = $leadRepository;
        $this->offerProvider = $offerProvider;
        $this->offerAvailabilityDecider = $categoryAvailability;
        $this->leadsHelper = $leadsHelper;
        $this->leadsFactory = $leadsFactory;
        $this->arrayOfferRepository = $arrayOfferRepository;
        $this->arrayCategoryRepository = $arrayCategoryRepository;
        $this->eventService = $eventService;
        $this->eventDetailService = $eventDetailService;

        $this->beConstructedWith(
            $this->leadRepository,
            $this->offerProvider,
            $this->offerAvailabilityDecider,
            $this->leadsHelper,
            $this->leadsFactory,
            $this->arrayOfferRepository,
            $arrayCategoryRepository,
            $this->eventService,
            $this->eventDetailService
        );
    }

    function it_is_initializable()
    {
        $this->shouldHaveType(SelectOffersFacade::class);
    }

    function it_should_return_offers_from_repository(Company $company, Offer $offer1, Offer $offer2)
    {
        $offer1->getCategoryId()->willReturn(1);
        $offer2->getCategoryId()->willReturn(2);

        $this->offerProvider->getOffers($company)->willReturn([$offer1, $offer2]);

        $this->offerAvailabilityDecider->isGranted($offer1, $company)->willReturn(TRUE);
        $this->offerAvailabilityDecider->isGranted($offer2, $company)->willReturn(FALSE);
        $this->getOffers($company)->shouldBe([$offer1]);

        $this->offerAvailabilityDecider->isGranted($offer1, $company)->willReturn(FALSE);
        $this->offerAvailabilityDecider->isGranted($offer2, $company)->willReturn(TRUE);
        $this->getOffers($company)->shouldBeLike([1 => $offer2]);

        $this->offerAvailabilityDecider->isGranted($offer1, $company)->willReturn(TRUE);
        $this->offerAvailabilityDecider->isGranted($offer2, $company)->willReturn(TRUE);
        $this->getOffers($company)->shouldBe([$offer1, $offer2]);

        $this->offerAvailabilityDecider->isGranted($offer1, $company)->willReturn(FALSE);
        $this->offerAvailabilityDecider->isGranted($offer2, $company)->willReturn(FALSE);
        $this->getOffers($company)->shouldBe([]);
    }

    function it_should_return_bank_offers_from_repository(Company $company, Offer $offer1, Offer $offer2, Category $category)
    {
        $this->arrayOfferRepository->getOffersByCategory(Offer::BANKS_OFFER_CATEGORY_ID)->willReturn([$offer1, $offer2]);

        $this->arrayCategoryRepository->get(Offer::BANKS_OFFER_CATEGORY_ID)->willReturn($category);
        $category->isPinnedLayout()->willReturn(true);

        $offer1->getId()->willReturn(18);
        $offer2->getId()->willReturn(20);

        $offer1->isHidden()->willReturn(false);
        $offer2->isHidden()->willReturn(false);

        $this->offerAvailabilityDecider->isGranted($offer1, $company)->willReturn(TRUE);
        $this->offerAvailabilityDecider->isGranted($offer2, $company)->willReturn(FALSE);
        $this->getOffersByCategoryId($company, Offer::BANKS_OFFER_CATEGORY_ID)->shouldBe([$offer1]);
    }

    function it_should_process_offers(
        Company $company,
        Lead $lead1,
        Lead $lead2,
        Lead $lead3,
        Lead $lead4,
        Event $event3,
        Event $event4
    ) {
        $company->getId()->willReturn(1);

        $lead3->getId()->willReturn(3);
        $lead3->getOfferId()->willReturn(Offer::BDG_OFFER_ID);
        $event3->getId()->willReturn(3);

        $lead4->getId()->willReturn(4);
        $lead4->getOfferId()->willReturn(Offer::BDG_OFFER_ID);
        $event4->getId()->willReturn(4);

        $this->arrayOfferRepository->getCategoryIdByOfferId(Offer::BDG_OFFER_ID)->willReturn(Offer::BANKS_OFFER_CATEGORY_ID);

        $this->arrayOfferRepository->getOffersIdsByCategory(Offer::BANKS_OFFER_CATEGORY_ID)->willReturn([18, 19, 20]);
        $this->leadRepository->findByCompany($company, [18, 19, 20, Offer::BDG_OFFER_ID])->willReturn([$lead1, $lead2]);

        $this->leadsHelper->filterProcessed([$lead1, $lead2], null)->willReturn([$lead2]);
        $this->leadsHelper->getNewOfferIds([$lead1, $lead2], [1, 2, 3])->willReturn([2, 3]);
        $this->leadsFactory->newCollectionFromOfferIds($company, [2, 3])->willReturn([$lead3, $lead4]);

        $this->eventService->notify(SelectOffersFacade::NEW_LEAD_EVENT, 3)->willReturn($event3);
        $this->eventService->notify(SelectOffersFacade::NEW_LEAD_EVENT, 4)->willReturn($event4);

        $this->leadRepository->removeLeads([$lead2])->shouldBeCalled();
        $this->leadRepository->saveOffers([$lead3, $lead4])->shouldBeCalled();

        $this->eventService->notify(SelectOffersFacade::NEW_LEAD_EVENT, 3)->shouldBeCalled();
        $this->eventService->notify(SelectOffersFacade::NEW_LEAD_EVENT, 4)->shouldBeCalled();

        $this->eventDetailService->notify(3, 'categoryId',Offer::BANKS_OFFER_CATEGORY_ID)->shouldBeCalled();
        $this->eventDetailService->notify(4, 'categoryId',Offer::BANKS_OFFER_CATEGORY_ID)->shouldBeCalled();

        $this->processOffers($company, [1, [2, 3]]);
    }
}
