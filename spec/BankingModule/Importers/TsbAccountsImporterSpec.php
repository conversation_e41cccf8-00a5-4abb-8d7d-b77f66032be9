<?php

namespace spec\BankingModule\Importers;

use BankingModule\BankingService;
use BankingModule\Config\EventLocator;
use BankingModule\Events\TsbAccountImportedEvent;
use BankingModule\Importers\TsbAccountsImporter;
use CsvParserModule\CsvParser;
use CsvParserModule\Exceptions\ImportException;
use CsvParserModule\Exceptions\IncorrectStructureException;
use Entities\Company;
use PhpSpec\ObjectBehavior;
use Prophecy\Argument;
use Services\CompanyService;
use Symfony\Component\EventDispatcher\EventDispatcher;
use Utils\File;

/**
 * @mixin TsbAccountsImporter
 */
class TsbAccountsImporterSpec extends ObjectBehavior
{
    /**
     * @var CsvParser
     */
    private $parser;

    /**
     * @var CompanyService
     */
    private $companyService;

    /**
     * @var BankingService
     */
    private $bankingService;

    /**
     * @var EventDispatcher
     */
    private $eventDispatcher;

    function let(
        CsvParser $parser,
        CompanyService $companyService,
        BankingService $bankingService,
        EventDispatcher $eventDispatcher
    )
    {
        $this->parser = $parser;
        $this->companyService = $companyService;
        $this->bankingService = $bankingService;
        $this->eventDispatcher = $eventDispatcher;
        $this->beConstructedWith($this->parser, $this->companyService, $this->bankingService, $this->eventDispatcher);
    }

    function it_is_initializable()
    {
        $this->shouldHaveType(TsbAccountsImporter::class);
    }

    function it_can_throw_when_invalid_file_format(File $file)
    {
        $this->parser->parse($file)->willThrow(new IncorrectStructureException);

        $this->shouldThrow(ImportException::class)->during('import', [$file]);
    }

    function it_will_skip_non_existing_companies(File $file)
    {
        $this->parser->parse($file)->willReturn([['123', '10/10/2015', '20/10/2015']]);
        $this->companyService->getCompanyByCompanyNumber('********')->willReturn(NULL);
        $this->eventDispatcher->dispatch(EventLocator::TSB_ACCOUNT_IMPORTED)->shouldNotBeCalled();

        $this->shouldThrow('CsvParserModule\Exceptions\ImportException')->during('import', [$file]);
    }

    function it_can_import_file(File $file, Company $company1, Company $company2)
    {
        $this->parser->parse($file)->willReturn(
            [['123', '10/10/2015', '20/10/2015'], ['456', '1/1/2014', '24/12/2014']]
        );

        $this->companyService->getCompanyByCompanyNumber('********')->willReturn($company1);
        $this->companyService->getCompanyByCompanyNumber('********')->willReturn($company2);

        $company1Event = TsbAccountImportedEvent::create($company1->getWrappedObject(), '10/10/2015', '20/10/2015');
        $company2Event = TsbAccountImportedEvent::create($company2->getWrappedObject(), '1/1/2014', '24/12/2014');

        $this->eventDispatcher->dispatch($company1Event, EventLocator::TSB_ACCOUNT_IMPORTED, )->shouldBeCalled();
        $this->eventDispatcher->dispatch($company2Event, EventLocator::TSB_ACCOUNT_IMPORTED)->shouldBeCalled();

        $this->import($file);
    }
}
