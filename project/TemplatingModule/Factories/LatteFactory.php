<?php

namespace TemplatingModule\Factories;

use InvalidArgumentException;
use Latte\Engine;
use Latte\Macros\MacroSet;
use Symfony\Component\DependencyInjection\Container;

/**
 * this class exists only because of the way how macros are added in latte
 */
class LatteFactory
{
    /**
     * @var Container
     */
    private $container;

    /**
     * @param Container $container
     */
    public function __construct(Container $container)
    {
        $this->container = $container;
    }

    /**
     * @return Engine
     * @throws InvalidArgumentException
     */
    public function createLatte()
    {
        $engine = new Engine();
        $compiler = $engine->getCompiler();
        $set = new MacroSet($compiler);
        $set->addMacro('ui', 'echo $latteUiHelper(%node.args)');
        $set->addMacro('url', 'echo %escape($urlGenerator->url(%node.args))');
        if ($this->container->hasParameter('latte.filters')) {
            foreach ($this->container->getParameter('latte.filters') as $filterName => $value) {
                if (is_string($value)) {
                    $engine->addFilter($filterName, $value);
                } elseif (isset($value['service'], $value['method'])) {
                    $engine->addFilter($filterName, [$this->container->get($value['service']), $value['method']]);
                } else {
                    throw new InvalidArgumentException(sprintf('Unknown filter %s. Filter must be callable or provide service and method keys', $filterName));
                }
            }
        }
        return $engine;
    }
}