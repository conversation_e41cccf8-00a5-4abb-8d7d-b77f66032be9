<div>
    {$formHelper->setTheme($form, 'cms.html.twig')}
    {$formHelper->start($form) nofilter}
    {foreach $view->getCategories() as $categoryView}
        {assign var="category" value=$categoryView->getCategory()}
        <div id="categoryContainer{$categoryView->getCategory()->getId()}" class="{if $categoryView->getCategory()->getId() == 6}hidden{/if}">
            <div class="col-12 alert nip-bg-blue d-flex mb-5" role="alert">
                <i class="fa-solid fa-circle-info cms-blue-text d-inline mt-1"></i>
                <div class="d-inline ms-2">
                    <span><span class="fw-semibold">Limited companies are required by law to maintain a separate business account.</span> We recommend the following banks based on your industry.</span>
                    <p class="mt-3 fw-semibold">Tip: You can have multiple Business bank accounts. Open more than one to  benefit from the cashback offers.</p>
                </div>
            </div>

            <div class="container custom-container">
                {include file="./offers.tpl" form=$form[$category->getId()] categoryView=$categoryView category=$category}
            </div>
        </div>
    {/foreach}

    <div class="mb-5 row" id="noBankSelectedLink">
        <div class="row d-none d-md-flex">
            <div class="col-md-6 col-12 text-start my-3">
                <a href="{$backLink}"
                    class="btn-outline-orange btn-view-formation fs-6 fw-semibold"
                >
                    <i class="me-2 fa-solid fa fa-long-arrow-left"></i>
                    <span>Back</span>
                </a>
            </div>
            <div class="col-md-6 col-12 text-md-end text-sm-start my-3">
                <a class="cms-btn-outline m-0 w-100" role="button" data-bs-toggle="modal" data-bs-target="#warning-no-bank-selected">
                    I don't want to select a bank
                    <i class="py-auto fa fa-long-arrow-right"></i>
                </a>
            </div>
        </div>
        <div class="row d-flex d-md-none">
            <div class="col-12 text-sm-start my-3">
                <a class="cms-btn-outline btn m-0 w-100" role="button" data-bs-toggle="modal" data-bs-target="#warning-no-bank-selected">
                    I don't want to select a bank
                    <i class="py-auto fa fa-long-arrow-right"></i>
                </a>
            </div>
            <div class="col-12 text-start my-3">
                <a href="{$backLink}"
                    class="btn-outline-orange btn-view-formation fs-6 fw-semibold"
                >
                    <i class="me-2 fa-solid fa fa-long-arrow-left"></i>
                    <span>Back</span>
                </a>
            </div>
        </div>
    </div>
    <div class="mb-5 d-none" id="bankSelectedLink">
        <div class="my-5 d-flex justify-content-between mx-2">
            <a href="{$backLink}"
                class="btn-outline-orange btn-view-formation fs-6 fw-semibold"
            >
                <i class="me-2 fa-solid fa fa-long-arrow-left"></i>
                <span>Back</span>
            </a>
            <button type="submit" class="btn btn-orange btn-view-formation d-none fs-5 fw-semibold" id="continueSubmitButton">
                Continue <i class="ms-2 fa-solid py-auto fa fa-long-arrow-right"></i>
            </button>
        </div>
    </div>

    <!-- BEGIN of modal for the warning of no bank selected !-->
    <div id="warning-no-bank-selected" class="modal fade" tabindex="-1" role="dialog" style="z-index: 100000;">
        <div class="modal-dialog modal-xl" role="document">
            <div class="modal-content p-3">
                <div class="modal-header">
                    <p class="modal-title fs-5">
                        Are you sure you don't need a business bank account?
                    </p>
                    <i type="button" class="close fa-solid fa-xmark fa-lg text-dark mx-3" data-bs-dismiss="modal" aria-label="Close"></i>
                </div>
                <div class="modal-body">
                    <ul class="ms-2">
                        <li >It is mandatory to have a dedicated bank account for your business, as your business is legally a separated entity.</li>
                        <li >You will save time by keeping your company expenses isolated, simplifying your business accounting.</li>
                        <li >We have only selected the best banks, with simple online sign up processes (although, if you prefer, Barclays offer in-branch sign ups too)</li>
                    </ul>
                </div> 
                <div class="border-top flex-column d-flex flex-lg-row flex-md-column flex-xl-row flex-sm-column justify-content-between p-3">
                     <div class=" my-3 mx-1">
                        <button type="submit" class="w-100 cms-btn">
                            My company does not need a bank account
                            <i class="fa fa-long-arrow-right"></i>
                        </button>
                     </div>
                     <div class=" my-3 mx-1">
                        <button type="button" data-bs-dismiss="modal" class="w-100 float-lg-end cms-btn-outline">
                            Let me select my company's bank
                            <i class="fa fa-long-arrow-right"></i>
                        </button>
                    
                    </div>
                </div>
            </div>
        </div>
    </div
    <!-- END of modal for the warning of no bank selected !-->
    {$formHelper->end($form) nofilter}
</div>
