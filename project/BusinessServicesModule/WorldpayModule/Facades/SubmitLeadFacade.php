<?php

namespace BusinessServicesModule\WorldpayModule\Facades;

use BusinessServicesModule\Repositories\LeadRepository;
use BusinessServicesModule\WorldpayModule\Clients\WorldpayClient;
use BusinessServicesModule\WorldpayModule\Factories\WorldpayLeadFactory;
use BusinessServicesModule\WorldpayModule\Clients\LeadSubmitter;
use BusinessServicesModule\Entities\Lead;
use OfferModule\Exceptions\UnsuccessfulWorldpayLeadSubmissionException;
use OfferModule\Exceptions\WorldpayAuthenticationException;
use Psr\Log\LoggerInterface;

class SubmitLeadFacade
{

    /**
     * @var LeadRepository
     */
    private $leadRepository;

    /**
     * @var WorldpayLeadFactory
     */
    private $worldpayLeadFactory;

    /**
     * @var WorldpayClient
     */
    private $worldpayClient;

    /**
     * @var array
     */
    private $worldpayConfig;

    /**
     * @var LoggerInterface
     */
    private $logger;

    public function __construct(
        LeadRepository $leadRepository,
        WorldpayLeadFactory $worldpayLeadFactory,
        WorldpayClient $worldpayClient,
        array $worldpayConfig,
        LoggerInterface $logger
    )
    {
        $this->leadRepository = $leadRepository;
        $this->worldpayLeadFactory = $worldpayLeadFactory;
        $this->worldpayClient = $worldpayClient;
        $this->worldpayConfig = $worldpayConfig;
        $this->logger = $logger;
    }

    public function sendLead(Lead $lead)
    {
        $worldpayLead = $this->worldpayLeadFactory->fromBusinessServices($lead, $this->worldpayConfig);

        try {
            $this->worldpayClient->submitLead($worldpayLead);

            $this->logger->debug("Worldpay lead has been submitted", [
                'customerDetailsId' => $worldpayLead->getLeadId(),
                'email' => $worldpayLead->getEmail(),
            ]);

            $lead->markAsProcessed();
            $this->leadRepository->saveEntity($lead);
        } catch (WorldpayAuthenticationException | UnsuccessfulWorldpayLeadSubmissionException $e) {
            $this->logger->error($e->getMessage(), ['leadId' => $lead->getId()]);
        }

    }

}
