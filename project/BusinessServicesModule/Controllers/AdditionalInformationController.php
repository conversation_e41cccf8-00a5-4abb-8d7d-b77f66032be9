<?php

namespace BusinessServicesModule\Controllers;

use BusinessServicesModule\BarclaycardModule\Deciders\BarclaycardDecider;
use BusinessServicesModule\Deciders\AdditionalInformationPageForbidden;
use BusinessServicesModule\Deciders\OfferAvailabilityDecider;
use BusinessServicesModule\Dto\AdditionalInformationData;
use BusinessServicesModule\Entities\InformationSet;
use BusinessServicesModule\Entities\Lead;
use BusinessServicesModule\Entities\Offer;
use BusinessServicesModule\Facades\AdditionalInformationFacade;
use BusinessServicesModule\Forms\AdditionalInformationType;
use BusinessServicesModule\Helpers\AdditionalInformationHelper;
use BusinessServicesModule\Providers\InformationSetProvider;
use BusinessServicesModule\Repositories\IOfferRepository;
use BusinessServicesModule\Repositories\LeadRepository;
use CompaniesHouseModule\Entities\DirectorPerson as CHDirectorPerson;
use CompaniesHouseModule\Repositories\MemberRepository as CHMemberRepository;
use CompanyFormationModule\Entities\DirectorPerson;
use CompanyFormationModule\Providers\StepProvider;
use CompanyFormationModule\Repositories\MemberRepository;
use FrontModule\controlers\CUSummaryControler;
use Services\Dispatcher\Events\CompanyEvent;
use Entities\Company;
use Config\Constants\EventLocator;
use Models\Products\Package;
use RouterModule\Helpers\ControllerHelper;
use Symfony\Component\EventDispatcher\EventDispatcher;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Response;
use TemplateModule\Renderers\IRenderer;

class AdditionalInformationController
{
    /**
     * @var ControllerHelper
     */
    private $controllerHelper;

    /**
     * @var IRenderer
     */
    private $renderer;

    /**
     * @var IRenderer
     */
    private $formationRenderer;

    /**
     * @var AdditionalInformationPageForbidden
     */
    private $pageForbidden;

    /**
     * @var AdditionalInformationHelper
     */
    private $additionalInformationHelper;

    /**
     * @var additionalInformationFacade
     */
    private $additionalInformationFacade;

    /**
     * @var LeadRepository
     */
    private $offerLeadRepository;

    /**
     * @var IOfferRepository
     */
    private $offerRepository;

    /**
     * @var InformationSetProvider
     */
    private $informationSetProvider;

    /**
     * @var MemberRepository
     */
    private $memberRepository;

    /**
     * @var EventDispatcher
     */
    private $eventDispatcher;

    /**
     * @var CHMemberRepository
     */
    private $chMemberRepository;

    /**
     * @var array
     */
    private $mapping;

    /**
     * @var OfferAvailabilityDecider
     */
    private $offerAvailabilityDecider;

    /**
     * @var BarclaycardDecider
     */
    private $barclaycardDecider;

    public function __construct(
        ControllerHelper $controllerHelper,
        IRenderer $renderer,
        IRenderer $formationRenderer,
        AdditionalInformationPageForbidden $pageForbidden,
        AdditionalInformationHelper $additionalInformationHelper,
        AdditionalInformationFacade $additionalInformationFacade,
        LeadRepository $offerLeadRepository,
        IOfferRepository $offerRepository,
        InformationSetProvider $informationSetProvider,
        MemberRepository $memberRepository,
        EventDispatcher $eventDispatcher,
        CHMemberRepository $chMemberRepository,
        array $mapping,
        OfferAvailabilityDecider $offerAvailabilityDecider,
        BarclaycardDecider $barclaycardDecider
    ) {
        $this->controllerHelper = $controllerHelper;
        $this->renderer = $renderer;
        $this->formationRenderer = $formationRenderer;
        $this->pageForbidden = $pageForbidden;
        $this->additionalInformationHelper = $additionalInformationHelper;
        $this->additionalInformationFacade = $additionalInformationFacade;
        $this->offerLeadRepository = $offerLeadRepository;
        $this->offerRepository = $offerRepository;
        $this->informationSetProvider = $informationSetProvider;
        $this->memberRepository = $memberRepository;
        $this->eventDispatcher = $eventDispatcher;
        $this->chMemberRepository = $chMemberRepository;
        $this->mapping = $mapping;
        $this->offerAvailabilityDecider = $offerAvailabilityDecider;
        $this->barclaycardDecider = $barclaycardDecider;
    }

    public function incorporation(Company $company): Response
    {
        [$leads, $informationSetCollection, $prefillData, $form] = $this->buildCommonVariables($company);

        if ($form->isSubmitted() && $form->isValid()) {
            $this->additionalInformationFacade->saveData($leads, $form->getData());
            return $this->controllerHelper->redirectionTo(
                'company_formation_module.next_step',
                ['company' => $company->getId(), 'step' => StepProvider::STEP_ADDITIONAL_INFORMATION]
            );
        }

        return $this->formationRenderer->render(
            $this->getCommonTemplateData($form, $informationSetCollection, $leads, $prefillData)
        );
    }

    public function incorporated(Company $company): Response
    {
        $response = $this->getOptionalRedirectResponse($company);
        if ($response) {
            return $response;
        }

        if ($company->getProductId() === Package::PACKAGE_ANNA) {
            return $this->controllerHelper->redirectionTo(CUSummaryControler::SUMMARY_PAGE, ['company_id' => $company->getId()]);
        }

        [$leads, $informationSetCollection, $prefillData, $form] = $this->buildCommonVariables($company);

        if ($form->isSubmitted() && $form->isValid()) {
            $this->additionalInformationFacade->saveData($leads, $form->getData());
            $this->eventDispatcher->dispatch(
                new CompanyEvent($company),
                EventLocator::BANK_APPLIED_AFTER_INCORPORATION
            );
            return $this->redirectToIncorporatedSummary($company);
        }

        return $this->renderer->render(
            array_merge(
                $this->getCommonTemplateData($form, $informationSetCollection, $leads, $prefillData),
                [
                    'company' => $company,
                    'tabs' => null,
                    'backLink' => $this->controllerHelper->getBacklink(),
                ]
            )
        );
    }

    private function getOptionalRedirectResponse(Company $company): ?RedirectResponse
    {
        if ($this->pageForbidden->noIncompleteAdditionInformation($company)) {
            return $this->controllerHelper->redirectionTo(
                CUSummaryControler::SUMMARY_PAGE,
                ['company_id' => $company->getId()]
            );
        }

        return null;
    }

    /**
     * TODO: separate to e.g. provider
     * @param Lead[] $leads
     * @return InformationSet[]
     */
    private function getInformationSetCollection(array $leads): array
    {
        $informationSets = [];
        foreach ($leads as $lead) {
            if ($offer = $this->offerRepository->findOneById($lead->getOfferId())) {

                // hack for right order
                // TODO: test

                if ($offer->getId() === Offer::BDG_OFFER_ID) {
                    $offerInformationSets = ['directorPrefill'];
                    // This removes not needed information sets based on data received from API
                    foreach ($lead->getBdgLead()->getContactRules() as $rule) {
                        if ($informationSet = $this->mapping[$rule->getType()] ?? null) {
                            $offerInformationSets = array_merge($offerInformationSets, [$informationSet]);
                        }
                    }
                    $informationSets = array_merge(
                        $informationSets,
                        array_intersect($offer->getInformationSets(), array_unique($offerInformationSets))
                    );
                } else {
                    $informationSets = array_merge($informationSets, $offer->getInformationSets());
                }
            }
        }
        return $this->informationSetProvider->getInformationSetCollection(array_unique($informationSets));
    }

    private function redirectToIncorporatedSummary(Company $company): RedirectResponse
    {
        return $this->controllerHelper->redirectionTo(
            CUSummaryControler::SUMMARY_PAGE,
            ['company_id' => $company->getId()]
        );
    }

    private function getPrefillDataAndDirectors(Company $company): array
    {
        /** @var DirectorPerson[] $directors */
        $directors = $company->isIncorporated() ?
            $this->chMemberRepository->getCompanyMembersByEntityName($company, CHDirectorPerson::class) :
            array_filter(
                $this->memberRepository->getDirectors($company),
                function ($director) {
                    return $director instanceof DirectorPerson;
                }
            );

        $customer = $company->getCustomer();

        $prefillData = [
            'customer' => [
                'email' => $customer->getEmail(),
                'phone' => $customer->getPhone()
            ],
            'company' => [
                'name' => $company->getCompanyName()
            ]
        ];

        foreach ($directors as $director) {
            $address = $director->getResidentialAddress();
            $personalDetails = $director->getPersonalDetails();
            $prefillData['directors'][$director->getId()] = [
                'title' => $personalDetails->getTitle(),
                'firstName' => $personalDetails->getForename(),
                'middleName' => $personalDetails->getMiddleName(),
                'lastName' => $personalDetails->getSurname(),
                'dob' => $personalDetails->getFormattedDob('Y-m-d'),
                'dobDay' => $personalDetails->getFormattedDob('d'),
                'dobMonth' => $personalDetails->getFormattedDob('m'),
                'dobYear' => $personalDetails->getFormattedDob('Y'),
                'premise' => $address->getPremise(),
                'street' => $address->getStreet(),
                'thoroughfare' => $address->getThoroughfare(),
                'postTown' => $address->getPostTown(),
                'postcode' => $address->getPostcode(),
                'county' => $address->getCounty(),
                'country' => $director->getCountryOfResidence()
            ];

        }

        return [$directors, $prefillData];
    }

    private function getCommonTemplateData(
        FormInterface $form,
        array $informationSetCollection,
        array $leads,
        array $prefillData
    ): array {
        return [
            'didCustomerOptInForIssuingOffer' => $this->barclaycardDecider->didCustomerOptInForOffer($leads[0]->getCompany(), Offer::BARCLAYCARD_ISSUING_OFFER_ID),
            'isBarclayIssuingOfferAvailable' => $this->barclaycardDecider->isIssuingOfferAvailable($leads[0]->getCompany()),
            'form' => $form->createView(),
            'submitted' => $form->isSubmitted(),
            'informationSets' => $this->informationSetProvider->getExistingTemplates($informationSetCollection),
            'offerNames' => $this->additionalInformationHelper->getOfferNames($leads),
            'showTitleSelect' => false,
            'prefillData' => [
                'directors' => json_encode($prefillData['directors'], JSON_HEX_APOS),
                'company' => json_encode($prefillData['company'], JSON_HEX_APOS),
                'customer' => json_encode($prefillData['customer'], JSON_HEX_APOS),
            ]
        ];
    }

    private function buildCommonVariables(Company $company): array
    {
        $leads = $this->offerLeadRepository->findNewByCompany($company);
        $informationSetCollection = $this->getInformationSetCollection($leads);

        [$directors, $prefillData] = $this->getPrefillDataAndDirectors($company);

        $form = $this->controllerHelper->buildForm(
            AdditionalInformationType::class,
            new AdditionalInformationData(),
            [
                'informationSets' => $informationSetCollection,
                'directors' => $directors
            ]
        );

        return [$leads, $informationSetCollection, $prefillData, $form];
    }
}
