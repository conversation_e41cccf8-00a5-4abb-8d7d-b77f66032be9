<?php

namespace BusinessServicesModule\PearlCharteredModule\Providers;

use BusinessServicesModule\Entities\Offer;
use BusinessServicesModule\Providers\IOfferProvider;
use BusinessServicesModule\Repositories\ArrayOfferRepository;
use BusinessServicesModule\Repositories\IOfferRepository;
use Entities\Company;

class OfferProvider implements IOfferProvider
{
    /**
     * @var IOfferRepository
     */
    private $repository;

    public function __construct(IOfferRepository $repository)
    {
        $this->repository = $repository;
    }

    /**
     * @return Offer[]
     */
    public function getOffers(Company $company): array
    {
        return $this->repository->findManyByMediatorId(ArrayOfferRepository::PEARL_CHARTERED_MEDIATOR_ID);
    }
}