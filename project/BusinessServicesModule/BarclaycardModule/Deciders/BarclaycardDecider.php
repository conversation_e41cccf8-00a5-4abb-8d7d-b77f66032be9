<?php

namespace BusinessServicesModule\BarclaycardModule\Deciders;

use BusinessServicesModule\Deciders\OfferAvailabilityDecider;
use BusinessServicesModule\Entities\Lead;
use BusinessServicesModule\Entities\Offer;
use BusinessServicesModule\Repositories\IOfferRepository;
use BusinessServicesModule\Repositories\LeadRepository;
use Doctrine\ORM\NonUniqueResultException;
use Entities\Company;

class BarclaycardDecider
{

    /**
     * @var LeadRepository
     */
    private $leadRepository;

    /**
     * @var IOfferRepository
     */
    private $offerRepository;

    /**
     * @var OfferAvailabilityDecider
     */
    private $offerAvailabilityDecider;

    public function __construct(
        LeadRepository $leadRepository,
        IOfferRepository $offerRepository,
        OfferAvailabilityDecider $offerAvailabilityDecider
    )
    {
        $this->leadRepository = $leadRepository;
        $this->offerRepository = $offerRepository;
        $this->offerAvailabilityDecider = $offerAvailabilityDecider;
    }

    /**
     * @param Company $company
     * @param int $offerId
     * @return bool
     * @throws NonUniqueResultException
     */
    public function didCustomerOptInForOffer(Company $company, int $offerId): bool
    {
        return !empty($this->leadRepository->optionalByCompanyAndOfferId($company, $offerId));
    }

    /**
     * @param Company $company
     * @return string
     * @throws NonUniqueResultException
     */
    public function didCustomerOptInForIssuing(Company $company): string
    {
        return $this->didCustomerOptInForOffer($company, Offer::BARCLAYCARD_ISSUING_OFFER_ID) ? 'YES' : 'NO';
    }

    /**
     * @param Company $company
     * @return string
     * @throws NonUniqueResultException
     */
    public function didCustomerOptInForAcquiring(Company $company): string
    {
        return $this->didCustomerOptInForOffer($company, Offer::BARCLAYCARD_ACQUIRING_OFFER_ID) ? 'YES' : 'NO';
    }

    /**
     * @param Company $company
     * @return bool
     * @throws NonUniqueResultException
     */
    public function isIssuingOfferAvailable(Company $company): bool
    {

        $offer = $this->offerRepository->findOneById(Offer::BARCLAYCARD_ISSUING_OFFER_ID);

        return !$this->didCustomerOptInForOffer($company, Offer::BARCLAYS_OFFER_ID)
            && !$offer->isHidden()
            && $this->offerAvailabilityDecider->isGranted($offer, $company);

    }

}