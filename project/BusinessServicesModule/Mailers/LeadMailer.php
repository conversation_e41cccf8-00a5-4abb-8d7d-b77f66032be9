<?php

namespace BusinessServicesModule\Mailers;

use BusinessServicesModule\Entities\Lead;
use BusinessServicesModule\Entities\PartnerServicesInformation;
use BusinessServicesModule\Exceptions\RuntimeException;
use EmailModule\Exceptions\EmailException;
use EmailModule\IEmailGateway;
use EmailModule\IEmailLog;
use EmailModule\Loaders\IEmailLoader;
use Entities\Company;
use Entities\Customer;
use Utils\File;

class LeadMailer
{
    /**
     * @var IEmailGateway
     */
    private $emailGateway;

    /**
     * @var IEmailLoader
     */
    private $emailLoader;


    public function __construct(
        IEmailGateway $emailGateway,
        IEmailLoader $emailLoader
    )
    {
        $this->emailGateway = $emailGateway;
        $this->emailLoader = $emailLoader;
    }

    /**
     * @throws EmailException
     */
    public function sendCustomerEmail(Lead $lead, string $emailTemplatePath, bool $markdownParsing = IEmailLoader::DISABLE_MARKDOWN_PARSING): IEmailLog
    {
        $company = $lead->getCompany();
        $customer = $company->getCustomer();
        $psi = $lead->getPartnerServicesInformation();

        if (!$psi) {
            throw new RuntimeException('The PSI is required.');
        }

        $email = $this->emailLoader->getEmail(
            new File(sprintf('%s/../%s', __DIR__, $emailTemplatePath)),
            $this->getDataForEmail($psi, $company),
            $markdownParsing
        );
        $email->setTo($customer->getEmail());

        return $this->emailGateway->send($email, $customer);
    }

    private function getDataForEmail(PartnerServicesInformation $psi, Company $company): array
    {
        $registeredOffice = $company->getRegisteredOffice();

        return [
            'title' => $psi->getTitle(),
            'firstName' => $psi->getFirstName(),
            'lastName' => $psi->getLastName(),
            'dateOfBirth' => $this->getDateOfBirth($psi),
            'phoneNumber' => $psi->getPhone(),
            'email' => $psi->getEmail(),

            'companyName' => $company->getCompanyName(),
            'number' => $company->getCompanyNumber(),

            'premise' => !empty($psi->getPremise()) ? $psi->getPremise() : 'n/a',
            'street' => !empty($psi->getStreet()) ? $psi->getStreet() : 'n/a',
            'town' => !empty($psi->getPostTown()) ? $psi->getPostTown() : 'n/a',
            'postcode' => !empty($psi->getPostcode()) ? $psi->getPostcode() :  'n/a',
            'country' => !empty($psi->getCountry()) ? $psi->getCountry() : $company->getCountryOfOrigin(),
            'county' => !empty($psi->getCounty()) ? $psi->getCounty() : "n/a",
            'locality' => !empty($psi->getThoroughfare()) ? $psi->getThoroughfare() : "n/a",

            'ro_premise' => $registeredOffice->getPremise(),
            'ro_street' => $registeredOffice->getStreet(),
            'ro_town' => $registeredOffice->getPostTown(),
            'ro_postcode' => $registeredOffice->getPostcode(),
            'ro_country' => !empty($registeredOffice->getCountry()) ? $registeredOffice->getCountry() : $company->getCountryOfOrigin(),
            'ro_county' => !empty($registeredOffice->getCounty()) ? $registeredOffice->getCounty() : "n/a",
            'ro_locality' => !empty($registeredOffice->getThoroughfare()) ? $registeredOffice->getThoroughfare() : "n/a"
        ];
    }

    private function getDateOfBirth($psi): string
    {
        if ($psi->getDob() && !empty($psi->getDob())) {
            return $psi->getDob()->format('Y-m-d');
        } else if (!empty($psi->getDateOfBirth())) {
            return $psi->getDateOfBirth()->format('Y-m-d');
        }
        return 'n/a';
    }

}
