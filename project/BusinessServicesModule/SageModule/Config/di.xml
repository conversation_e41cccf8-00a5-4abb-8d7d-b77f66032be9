<?xml version="1.0" ?>

<container xmlns="http://symfony.com/schema/dic/services"
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">
    <services>
        <defaults public="true" />
        <service class="BusinessServicesModule\SageModule\Providers\OfferProvider" id="business_services_module.sage.providers.offer_provider">
            <argument id="business_services_module.repositories.array_offer_repository" type="service"/>
            <!--<tag name="business_services_module.offers_provider"/>-->
        </service>

        <service class="BusinessServicesModule\SageModule\Facades\SubmitLeadFacade" id="business_services_module.sage_module.facades.submit_lead_facade">
            <argument id="business_services_module.sage_module.clients.sage_client" type="service"/>
            <argument id="business_services_module.repositories.lead_repository" type="service"/>
            <argument id="business_services_module.sage_module.dto.eloqua_form_data_factory" type="service"/>
            <argument id="business_services_module.sage_module.mailers.start_trial_mailer" type="service"/>
            <argument id="business_services.normalizers.country_normalizer.monolog.logger" type="service"/>
            <argument>%environment%</argument>
        </service>

        <service class="GuzzleHttp\Client" id="business_services_module.sage_module.clients.http_client">
            <argument type="collection">
                <argument key="base_url">%sage_business_services.base_url%</argument>
            </argument>
        </service>

        <service class="BusinessServicesModule\SageModule\Clients\SageClient" id="business_services_module.sage_module.clients.sage_client">
            <argument id="business_services_module.sage_module.clients.http_client" type="service"/>
        </service>

        <service class="TranslationModule\Normalizers\CountryNormalizer" id="business_services_module.sage_module.normalizers.iso_alpha2_to_sage_name_normalizer">
            <argument>%sage_business_services.countries%</argument>
        </service>

        <service class="Monolog\Handler\StreamHandler" id="business_services.sage_module.normalizers.country_normalizer.monolog.handler.stream_handler">
            <argument>%business_services.normalizers_log_file_path%</argument>
        </service>

        <service class="Monolog\Logger" id="business_services.normalizers.country_normalizer.monolog.logger">
            <argument>sage_country_normalizer</argument>
            <argument type="collection">
                <argument type="service" id="business_services.sage_module.normalizers.country_normalizer.monolog.handler.stream_handler" />
                <argument id="error_module.handlers.stack_driver_handler.debug" type="service"/>
                <argument id="error_module.handlers.sentry_handler.error" type="service"/>
            </argument>
        </service>

        <service class="BusinessServicesModule\SageModule\Dto\EloquaFormDataFactory" id="business_services_module.sage_module.dto.eloqua_form_data_factory">
            <argument>%sage_business_services.form%</argument>
            <argument id="business_services_module.sage_module.normalizers.iso_alpha2_to_sage_name_normalizer" type="service"/>
        </service>

        <service class="BusinessServicesModule\SageModule\Commands\SubmitLeadsCommand" id="business_services_module.sage_module.commands.submit_leads_command">
            <argument id="cron.loggers.default_logger" type="service"/>
            <argument id="business_services_module.repositories.lead_repository" type="service"/>
            <argument id="business_services_module.sage_module.facades.submit_lead_facade" type="service"/>
            <argument id="business_services_module.sage_module.providers.leads_to_process_provider" type="service"/>
            <tag name="cron.command" command-name="cron:business_services:sage:submit_leads" action="sendLeads"/>
        </service>

        <service class="BusinessServicesModule\SageModule\Mailers\StartTrialMailer" id="business_services_module.sage_module.mailers.start_trial_mailer">
            <argument type="service" id="email_module.gateways.default_gateway"/>
            <argument type="service" id="email_module.repositories.predefined_email_repository"/>
            <argument>%sage_business_services.mailer.start_trial_email%</argument>
            <argument>%sage_business_services.email.start_trial_link%</argument>
            <argument>%sage_business_services.email.start_trial_with_sage_link%</argument>
        </service>

        <service class="BusinessServicesModule\SageModule\Factories\LeadsToProcessCriteriaFactory" id="business_services_module.sage_module.factories.leads_to_process_criteria_factory">
            <argument id="business_services_module.repositories.array_offer_repository" type="service"/>
        </service>

        <service class="BusinessServicesModule\SageModule\Providers\LeadsToProcessProvider" id="business_services_module.sage_module.providers.leads_to_process_provider">
            <argument id="business_services_module.sage_module.factories.leads_to_process_criteria_factory" type="service"/>
            <argument id="business_services_module.repositories.lead_repository" type="service"/>
        </service>

    </services>

</container>