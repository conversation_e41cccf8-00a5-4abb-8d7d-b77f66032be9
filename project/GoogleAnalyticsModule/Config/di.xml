<?xml version="1.0" ?>
<container xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"  xmlns="http://symfony.com/schema/dic/services"
        xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd"
>
    <services>
        <service class="HttpClient\Client" id="google_analytics_module.clients.google_analytics_client_client">
            <factory service="http_client.factories.client_factory" method="create"/>
        </service>

        <service class="GoogleAnalyticsModule\ApiClient\GoogleAnalyticsClient" id="google_analytics_module.api_client.google_analytics_client">
            <argument id="google_analytics_module.clients.google_analytics_client_client" type="service"/>
            <argument>%google_analytics.api.secret%</argument>
            <argument>%google_analytics.api.measurement_id%</argument>
            <argument>%google_analytics.api.url%</argument>
            <argument id="google_analytics_module.loggers.google_analytics_logger" type="service"/>
        </service>

        <service class="GoogleAnalyticsModule\Services\GoogleAnalyticsService" id="google_analytics_module.services.google_analytics_service">
            <argument id="google_analytics_module.api_client.google_analytics_client" type="service"/>
            <argument id="user_module.services.customer_availability" type="service"/>
        </service>

        <service class="GoogleAnalyticsModule\Loggers\GoogleAnalyticsLogger" id="google_analytics_module.loggers.google_analytics_logger">
            <argument id="error.loggers.monolog" type="service"/>
        </service>
    </services>
</container>