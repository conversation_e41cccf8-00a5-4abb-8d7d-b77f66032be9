<?php

namespace PeopleWithSignificantControlModule\Facades;

use DateTime;
use Entities\Company;
use Entities\CompanyHouse\FormSubmission;
use Entities\CompanyHouse\FormSubmission\PSCStatementNotification as PSCStatementNotificationFS;
use Exceptions\Technical\RequestException;
use FormSubmissionModule\Exceptions\FormSubmissionFailedException;
use FormSubmissionModule\Exceptions\PendingFormSubmissionExists;
use FormSubmissionModule\Providers\OldFormSubmissionProvider;
use Libs\CHFiling\Core\Request\Form\Form;
use Nette\InvalidStateException;
use PeopleWithSignificantControl\Interfaces\IPscAware;
use PeopleWithSignificantControlModule\Dto\PscStatementNotificationData;
use PeopleWithSignificantControlModule\FormSubmissions\Forms\PSCStatementNotification;

class PscStatementNotificationFacade
{
    /**
     * @var OldFormSubmissionProvider
     */
    private $formSubmissionProvider;

    /**
     * @param OldFormSubmissionProvider $formSubmissionProvider
     */
    public function __construct(OldFormSubmissionProvider $formSubmissionProvider)
    {
        $this->formSubmissionProvider = $formSubmissionProvider;
    }

    /**
     * @param Company $company
     * @param PscStatementNotificationData $data
     * @throws FormSubmissionFailedException
     */
    public function process(Company $company, PscStatementNotificationData $data): void
    {
        try {
            $formSubmission = $this->formSubmissionProvider->tryNewFromCompany(
                $company,
                PSCStatementNotificationFS::FORM_IDENTIFIER
            );
        } catch (PendingFormSubmissionExists $e) {
            return;
        }

        $this->hydrateForm(
            $formSubmission->getForm(),
            DateTime::createFromFormat('Y-m-d', $data->getRegisterEntryDate()->format('Y-m-d')),
            IPscAware::NO_INDIVIDUAL_OR_ENTITY_WITH_SIGNFICANT_CONTROL
        );

        try {
            $formSubmission->sendRequestImproved();
        } catch (RequestException | InvalidStateException $e) {
            throw new FormSubmissionFailedException($formSubmission);
        }

        if ($formSubmission->isError()) {
            throw new FormSubmissionFailedException($formSubmission);
        }
    }

    /**
     * @param PSCStatementNotification|Form $form
     * @param DateTime $registerEntryDate
     * @param string $companyStatement
     */
    private function hydrateForm(
        PSCStatementNotification $form,
        DateTime $registerEntryDate,
        string $companyStatement
    ): void
    {
        $form->setRegisterEntryDate($registerEntryDate);
        $form->setCompanyStatement($companyStatement);
    }
}
