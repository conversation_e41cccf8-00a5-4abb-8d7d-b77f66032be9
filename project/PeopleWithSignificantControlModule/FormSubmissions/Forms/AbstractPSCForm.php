<?php

namespace PeopleWithSignificantControlModule\FormSubmissions\Forms;

use DateTime;
use Libs\CHFiling\Core\Request\Form\Form;
use PeopleWithSignificantControlModule\FormSubmissions\BaseTypes\ICorporate;
use PeopleWithSignificantControlModule\FormSubmissions\BaseTypes\IIndividual;
use PeopleWithSignificantControlModule\FormSubmissions\BaseTypes\ILegalPerson;
use PeopleWithSignificantControlModule\FormSubmissions\BaseTypes\IPerson;
use PeopleWithSignificantControlModule\FormSubmissions\Forms\Traits\GetXmlTrait;

abstract class AbstractPSCForm implements Form
{
    use GetXmlTrait;

    /**
     * @var IPerson
     */
    private $person;

    /**
     * @param IPerson $person
     */
    public function setPerson(IPerson $person): void
    {
        $this->person = $person;
    }

    /**
     * @return IPerson
     */
    public function getPerson(): ?IPerson
    {
        return $this->person;
    }

    /**
     * @return IIndividual|IPerson|null
     */
    public function getIndividual(): ?IIndividual
    {
        return $this->getVirtualProperty(IIndividual::class);
    }

    /**
     * @return ICorporate|IPerson|null
     */
    public function getCorporate(): ?ICorporate
    {
        return $this->getVirtualProperty(ICorporate::class);
    }

    /**
     * @return ILegalPerson|IPerson|null
     */
    public function getLegalPerson(): ?ILegalPerson
    {
        return $this->getVirtualProperty(ILegalPerson::class);
    }

    /**
     * @return DateTime
     */
    public function getRegisterEntryDate(): DateTime
    {
        return new DateTime();
    }

    /**
     * @param string $className
     * @return null|IPerson
     */
    private function getVirtualProperty(string $className): ?IPerson
    {
        return $this->person instanceof $className ? $this->person : NULL;
    }
}
