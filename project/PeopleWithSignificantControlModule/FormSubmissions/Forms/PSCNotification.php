<?php

namespace PeopleWithSignificantControlModule\FormSubmissions\Forms;

use DateTime;

class PSCNotification extends AbstractPSCForm
{
    /**
     * @var string[]
     */
    private $natureOfControls = [];

    /**
     * @var string[]
     */
    private $llpNatureOfControls = [];

    /**
     * @var DateTime
     */
    private $notificationDate;

    /**
     * @param DateTime $notificationDate
     */
    public function setNotificationDate(DateTime $notificationDate): void
    {
        $this->notificationDate = $notificationDate;
    }

    /**
     * @param string[] $natureOfControls
     */
    public function setNatureOfControls(array $natureOfControls): void
    {
        $this->natureOfControls = $natureOfControls;
    }

    /**
     * @param string[] $llpNatureOfControls
     */
    public function setLlpNatureOfControls(array $llpNatureOfControls): void
    {
        $this->llpNatureOfControls = $llpNatureOfControls;
    }

    /**
     * @return string[]
     */
    public function getNatureOfControls(): array
    {
        return $this->natureOfControls;
    }

    /**
     * @return string[]
     */
    public function getLlpNatureOfControls(): array
    {
        return $this->llpNatureOfControls;
    }

    /**
     * @return DateTime
     */
    public function getNotificationDate(): ?DateTime
    {
        return $this->notificationDate;
    }
}
