<?php

namespace PeopleWithSignificantControlModule\FormSubmissions\BaseTypes\Notifications\PersonTypes;

use CompaniesHouseModule\FormSubmissions\BaseTypes\ResidentialAddress;
use CompaniesHouseModule\FormSubmissions\BaseTypes\ServiceAddressFactory;
use DateTime;
use PeopleWithSignificantControlModule\Dto\Notify\PscNotifyCorporateData;
use PeopleWithSignificantControlModule\Dto\Notify\PscNotifyLegalPersonData;
use PeopleWithSignificantControlModule\Dto\Notify\PscNotifyPersonData;
use PeopleWithSignificantControlModule\FormSubmissions\BaseTypes\LegalPersonIdentification;
use PeopleWithSignificantControlModule\FormSubmissions\BaseTypes\PSCCompanyIdentification;

class PersonTypeFactory
{
    /**
     * @var ServiceAddressFactory
     */
    private $serviceAddressFactory;

    public function __construct(ServiceAddressFactory $serviceAddressFactory)
    {
        $this->serviceAddressFactory = $serviceAddressFactory;
    }

    public function createIndividualFromPscNotifyPersonData(PscNotifyPersonData $data): Individual
    {
        $personData = $data->getPerson()->getPerson();
        $residentialAddress = $data->getResidentialAddress()->isDifferent()
            ? ResidentialAddress::fromResidentialAddressData($data->getResidentialAddress())
            : ResidentialAddress::sameAsServiceAddress();

        return new Individual(
            $personData->getTitle(),
            $personData->getForename(),
            $personData->getMiddleName(),
            $personData->getSurname(),
            $data->getServiceAddress()->isMsgServiceAddress()
                ? $this->serviceAddressFactory->createOurServiceAddress()
                : $this->serviceAddressFactory->createFromNotifyServiceAddressData($data->getServiceAddress()),
            DateTime::createFromFormat('Y-m-d', $personData->getDob()->format('Y-m-d')),
            $personData->getNationality(),
            $personData->getCountryOfResidence(),
            $residentialAddress
        );
    }

    public function createCorporateFromPscNotifyCorporateData(PscNotifyCorporateData $data): Corporate
    {
        $corporateData = $data->getCorporate()->getCorporate();

        return new Corporate(
            $corporateData->getCorporateName(),
            $data->getAddress()->isMsgServiceAddress()
                ? $this->serviceAddressFactory->createOurServiceAddress()->getAddress()
                : $this->serviceAddressFactory->createFromNotifyServiceAddressData($data->getAddress())->getAddress(),
            PSCCompanyIdentification::fromCorporateData($corporateData)
        );
    }

    public function createLegalPersonFromPscNotifyLegalPersonData(PscNotifyLegalPersonData $data): LegalPerson
    {
        $legalPersonData = $data->getLegalPerson();

        return new LegalPerson(
            $legalPersonData->getLegalPersonName(),
            $data->getAddress()->isMsgServiceAddress()
                ? $this->serviceAddressFactory->createOurServiceAddress()->getAddress()
                : $this->serviceAddressFactory->createFromNotifyServiceAddressData($data->getAddress())->getAddress(),
            new LegalPersonIdentification($legalPersonData->getLawGoverned(), $legalPersonData->getLegalForm())
        );
    }
}
