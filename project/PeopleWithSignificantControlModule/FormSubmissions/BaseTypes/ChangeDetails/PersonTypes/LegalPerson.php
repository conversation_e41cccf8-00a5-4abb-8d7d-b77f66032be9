<?php

namespace PeopleWithSignificantControlModule\FormSubmissions\BaseTypes\ChangeDetails\PersonTypes;

use PeopleWithSignificantControlModule\FormSubmissions\BaseTypes\ChangeDetails\Changes\LegalPersonChange;
use PeopleWithSignificantControlModule\FormSubmissions\BaseTypes\ILegalPerson;

class Legal<PERSON>erson implements ILegalPerson
{
    /**
     * @var string
     */
    private $legalPersonName;

    /**
     * @var LegalPersonChange
     */
    private $change;

    /**
     * @param string $legalPersonName
     * @param LegalPersonChange $change
     */
    public function __construct(string $legalPersonName, LegalPersonChange $change)
    {
        $this->legalPersonName = $legalPersonName;
        $this->change = $change;
    }

    /**
     * @return string
     */
    public function getLegalPersonName(): string
    {
        return $this->legalPersonName;
    }

    /**
     * @return LegalPersonChange
     */
    public function getChange(): LegalPersonChange
    {
        return $this->change;
    }
}
