purchase_confirmation:
  path: /purchase-confirmation.html
  defaults:
    controller: payment_module.controllers.payment_confirmation_controller
    action: confirmation
    page: 139 # id check depends on this page
    requiredController: FrontModule\controlers\LoggedInController
  options:
    excludeFromSitemap: true

purchase_confirmation_download_invoice:
  path: /purchase-confirmation/download-invoice/
  defaults:
    controller: payment_module.controllers.payment_confirmation_controller
    action: downloadInvoice
    page: 139
    requiredController: FrontModule\controlers\LoggedInController
  options:
    excludeFromSitemap: true

inline_payment_pay:
  path: /inline-payment/pay/
  methods: [POST]
  defaults:
    controller: payment_module.controllers.inline_payment_controller
    action: pay
  options:
    excludeFromSitemap: true

inline_payment_payCv2:
  path: /inline-payment/payCv2/
  methods: [POST]
  defaults:
    controller: payment_module.controllers.inline_payment_controller
    action: payCv2
  options:
    excludeFromSitemap: true

inline_payment_add_card:
  path: /inline-payment/pay-with-new-card/{company}/{product}/
  methods: [GET, POST]
  defaults:
    controller: payment_module.controllers.inline_payment_controller
    action: payWithNewCard
    requirements:
      product:
        converter: Product
      basket:
        type: inline_payment
  requirements:
    company: '\d+'
    product: '\d+'
  options:
    excludeFromSitemap: true

inline_payment_log:
  path: /inline-payment/log/
  methods: [POST]
  defaults:
    controller: payment_module.controllers.inline_payment_log_controller
    action: recordLog
  options:
    excludeFromSitemap: true

inline_payment_payment:
  path: /inline_payment/payment/
  defaults:
    controller: payment_module.controllers.payment_controller_inline_payment
    action: payment
    requirements:
      basket:
        type: inline_payment
  methods: [GET, POST]
  options:
    excludeFromSitemap: true

inline_payment_validate_3d_secure:
  path: /inline-payment/validate-3d-secure/{company}/{product}/
  defaults:
    controller: payment_module.controllers.inline_payment_controller
    action: validate3DSecure
    requirements:
      product:
        converter: Product
      basket:
        type: inline_payment
  requirements:
    company: '\d+'
    product: '\d+'
  options:
    excludeFromSitemap: true

inline_payment_check_status:
  path: /inline-payment/check-status/{company}/{product}/
  defaults:
    controller: payment_module.controllers.inline_payment_controller
    action: checkStatus
    requirements:
      product:
        converter: Product
      basket:
        type: inline_payment
  requirements:
    company: '\d+'
    product: '\d+'
  options:
    excludeFromSitemap: true

inline_payment_sage_validation:
  path: /inline-payment/sage-validation/
  defaults:
    controller: payment_module.controllers.sage_controller_inline_payment
    action: validate
    requirements:
      basket:
        type: inline_payment
  options:
    excludeFromSitemap: true

inline_payment_sage_authentication:
  path: /inline-payment/sage-authentication/
  defaults:
    controller: payment_module.controllers.sage.authentication_controller_inline_payment
    action: authenticate
  options:
    excludeFromSitemap: true

inline_payment_purchase_confirmation:
  path: /inline-payment/purchase-confirmation.html
  defaults:
    controller: payment_module.controllers.payment_confirmation_controller
    action: confirmation
    page: 139 # id check depends on this page
    requiredController: FrontModule\controlers\LoggedInController
    requirements:
      basket:
        type: inline_payment
  options:
    excludeFromSitemap: true

non_standard_payment:
  path: /non-standard-payment/
  methods: [GET, POST]
  defaults:
    controller: payment_module.controllers.non_standard_payment_controller
    action: paymentForm
  options:
    excludeFromSitemap: true

omnipay_inline_payment_pay:
  path: /omnipay/inline-payment/pay/
  methods: [POST]
  defaults:
    controller: payment_module.controllers.inline_payment_controller
    action: payWithOmnipay
  options:
    excludeFromSitemap: true

omnipay_order_confirmation:
  path: /omnipay/order-confirmation/{omnipayOrderId}/
  defaults:
    controller: payment_module.controllers.payment_confirmation_controller
    action: orderConfirmation
    requiredController: FrontModule\controlers\LoggedInController
    methods: [ GET ]
  options:
    excludeFromSitemap: true