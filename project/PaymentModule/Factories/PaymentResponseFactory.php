<?php

namespace PaymentModule\Factories;

use Entities\Customer;
use PaymentModule\Responses\PaymentResponse;
use models\Payment\PaymentResponse as OldPaymentResponse;
use Services\Payment\PaymentResponse as NewerPaymentResponse;
use Models\OldModels\Transaction;

/**
 * @deprecated use IPaymentResponse
 */
final class PaymentResponseFactory
{
    /**
     * @param PaymentResponse $paymentResponse
     * @param Customer $customer
     * @return OldPaymentResponse
     */
    public function convert(PaymentResponse $paymentResponse, ?Customer $customer, string $email = null)
    {
        $p = new OldPaymentResponse();

        $p->setPaymentTransactionTime(date('Y m d H:i:s'));
        $p->setPaymentHolderEmail($customer ? $customer->getEmail() : $email);

        $transactionType = $this->mapTransactionTypes($paymentResponse->getPaymentType());
        $p->setTypeId($transactionType);

        $p->setPaymentTransactionDetails(Transaction::$types[$transactionType]);
        if ($paymentResponse->getConfirmHash()) {
            $p->setConfirmHash($paymentResponse->getConfirmHash());
        }


        // sage
        if ($paymentResponse->isSagePay()) {
            $details = $paymentResponse->getSagePaymentInfo();
            $p->setPaymentCardNumber($paymentResponse->getCardNumber());
            $p->setPaymentCardType($paymentResponse->getCardType());
            $p->setPaymentRequestType($paymentResponse->getRequestType());

            $p->setPaymentHolderFirstName($details->getBillingFirstnames());
            $p->setPaymentHolderLastName($details->getBillingSurname());
            $p->setPaymentHolderAddressStreet(sprintf(
                '%s %s %s',
                $details->getBillingAddress1(),
                $details->getBillingAddress2(),
                $details->getBillingAddress3()
            ));
            $p->setPaymentHolderAddressCity($details->getBillingCity());
            $p->setPaymentHolderAddressState($details->getBillingState());
            $p->setPaymentHolderAddressPostCode($details->getBillingPostCode());
            $p->setPaymentHolderAddressCountry($details->getBillingCountry());
            $p->setPaymentHolderPhone($details->getBillingPhone());

            $p->setPaymentTransactionDetails(serialize($details));
            $p->setPaymentOrderCode($paymentResponse->getSageVpsTxId());
            $p->setPaymentVpsAuthCode($paymentResponse->getSageTxAuthNo());
            $p->setPaymentVendorTXCode($paymentResponse->getSageVendorTxCode());
            $p->setPaymentSecurityKey($paymentResponse->getSageSecurityKey());
            if ($parentTransaction = $paymentResponse->getParentTransaction()) {
                $p->setParentTransactionId($parentTransaction->getId());
            }

        } elseif ($paymentResponse->isPaypal()) {
            $details = $paymentResponse->getPaypalPaymentInfo();
            $payer = $details->getPayerName();
            $p->setPaymentOrderCode($paymentResponse->getPaypalTransactionId());
            $p->setPaymentHolderFirstName($payer->getFirstName());
            $p->setPaymentHolderLastName($payer->getLastName());

            // retrieve from paypal ?
            if ($address = $details->getShippingAddress()) {
                $p->setPaymentHolderAddressStreet($address->getStreet() . ' ' . $address->getStreet2());
                $p->setPaymentHolderAddressCity($address->getCity());
                $p->setPaymentHolderAddressPostCode($address->getZip());
                $p->setPaymentHolderAddressCountry($address->getCountry());
            }
            $p->setPaymentTransactionDetails(serialize($details));
        } elseif ($paymentResponse->isOmnipay()) {
            $p->setPaymentOrderCode($paymentResponse->getPaymentIntentId());
            $p->setPaymentCardNumber($paymentResponse->getCardNumber());
            $p->setPaymentCardType($paymentResponse->getCardType());
        } elseif ($customer) {
            $p->setPaymentHolderFirstName($customer->getFirstName());
            $p->setPaymentHolderLastName($customer->getLastName());

            $p->setPaymentHolderAddressStreet($customer->getAddress1());
            $p->setPaymentHolderAddressCity($customer->getCity());
            $p->setPaymentHolderAddressPostCode($customer->getPostcode());
            $p->setPaymentHolderAddressCountry($customer->getCountryIso());
        }
        return $p;
    }

    /**
     * @param string $paymentType
     * @return int
     */
    public function mapTransactionTypes($paymentType)
    {
        switch ($paymentType) {
            case PaymentResponse::TYPE_SAGE:
                return Transaction::TYPE_SAGEPAY;

            case PaymentResponse::TYPE_PAYPAL:
                return Transaction::TYPE_PAYPAL;

            case PaymentResponse::TYPE_ON_ACCOUNT:
                return Transaction::ON_ACCOUNT;

            case PaymentResponse::TYPE_FREE:
                return Transaction::TYPE_FREE;

            case PaymentResponse::TYPE_OMNIPAY:
                return Transaction::TYPE_OMNIPAY;

            case PaymentResponse::TYPE_OMNIPAY_CREDIT:
                return Transaction::TYPE_OMNIPAY_CREDIT;

            default:
                return Transaction::TYPE_SAGEPAY;
        }
    }

    /**
     * @param PaymentResponse $paymentResponse
     * @return NewerPaymentResponse
     */
    public function convertToNewer(PaymentResponse $paymentResponse)
    {
        $p = NewerPaymentResponse::createEmpty();
        $p->setTypeId($paymentResponse->getPaymentType());
        $p->setCardHolder($paymentResponse->getCardHolder());
        $p->setCardNumber($paymentResponse->getCardNumber());
        $p->setCardExpiryDate($paymentResponse->getCardExpiryDate());
        $p->setCardType($paymentResponse->getCardType());
        $p->setPhone($paymentResponse->getPhone());

        $p->setError($paymentResponse->getError());

        $p->setSageSecurityKey($paymentResponse->getSageSecurityKey());
        $p->setSageDetails($paymentResponse->getSagePaymentInfo());
        $p->setSageTxAuthNo($paymentResponse->getCardHolder());
        $p->setSageVendorTxCode($paymentResponse->getCardHolder());
        $p->setSageVpsTxId($paymentResponse->getCardHolder());
        $p->setSageToken($paymentResponse->getSageToken());

        $p->setPaypalTransactionId($paymentResponse->getPaypalTransactionId());
        $p->setAndroidTransactionId($paymentResponse->getAndroidTransactionId());
        $p->setItunesTransactionId($paymentResponse->getItunesTransactionId());

        $p->setRecurringPayment($paymentResponse->isRecurringPayment());
        return $p;
    }
}
