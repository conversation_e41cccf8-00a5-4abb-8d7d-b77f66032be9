<?php

namespace PaymentModule\Converters;

use Entities\Payment\Token;
use PaymentModule\Contracts\IToken;
use Repositories\Payment\TokenRepository;
use RouterModule\Converters\IConverter;
use RouterModule\Exceptions\RequiredArgumentException;
use UserModule\Contracts\ICustomer;
use UserModule\Services\ICustomerAvailability;

class TokenConverter implements IConverter
{
    /**
     * @var TokenRepository
     */
    private $tokenRepository;

    /**
     * @var ICustomerAvailability
     */
    private $customerAvailability;

    public function __construct(TokenRepository $tokenRepository, ICustomerAvailability $customerAvailability)
    {
        $this->tokenRepository = $tokenRepository;
        $this->customerAvailability = $customerAvailability;
    }

    public function convert($value, $type, $configuration = null)
    {
        if (!$value) {
            return null;
        }

        $customer = $this->customerAvailability->optionalLoggedInCustomer();
        if (!$customer) {
            throw RequiredArgumentException::noDependency(ICustomer::class, $configuration);
        }

        /** @var Token $token */
        $token = $this->tokenRepository->getCustomerTokenById($customer, $value);
        if ($token && $token->isActive()) {
            return $token;
        }

        throw RequiredArgumentException::noDependency(IToken::class, $configuration);
    }
}