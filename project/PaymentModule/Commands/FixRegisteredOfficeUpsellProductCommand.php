<?php

namespace PaymentModule\Commands;

use DateTime;
use Doctrine\ORM\ORMInvalidArgumentException;
use Entities\Company;
use Entities\Service;
use Framework\FNode;
use Models\Products\Package;
use Models\Products\Product;
use Psr\Log\LoggerInterface;
use Repositories\CompanyRepository;
use Repositories\ServiceRepository;
use Services\ServiceService;

/**
 * @description Command to fix the wrong RO product added to the upsell during the incorporation process. The correct one is Full Privacy (1724) and it's products but instead we were adding 1257.
 */
class FixRegisteredOfficeUpsellProductCommand
{

    /**
     * @var ServiceRepository
     */
    private $serviceRepository;

    /**
     * @var CompanyRepository
     */
    private $companyRepository;

    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * @var ServiceService
     */
    private $serviceService;

    public function __construct(
        ServiceRepository $serviceRepository,
        CompanyRepository $companyRepository,
        LoggerInterface $logger,
        ServiceService $serviceService
    ) {
        $this->serviceRepository = $serviceRepository;
        $this->companyRepository = $companyRepository;
        $this->logger = $logger;
        $this->serviceService = $serviceService;
    }

    public function execute(bool $dryRun = false, int $companyId = null, int $limit = null): void
    {
        $company = null;

        if ($companyId && !$company = $this->companyRepository->find($companyId)){
            $this->logger->debug(sprintf('No company found for the provided ID: %s', $companyId));
            die;
        }

        $services = $this->serviceRepository->getServicesByDateAndProduct(
            Product::PRODUCT_REGISTERED_OFFICE_SERVICE,
            new DateTime('2020-08-06 00:00:00'),
            $company,
            $limit
        );

        $amountOfCompaniesToFix = count($services);

        if ($amountOfCompaniesToFix === 0) {
            $this->logger->debug("No companies found to be fixed.");
            return;
        }

        $this->logger->debug(sprintf('Fixing %s companies', $amountOfCompaniesToFix));

        $count = 0;

        /** @var Service $service */
        foreach ($services as $service) {
            $company = $service->getCompany();
            $this->addFullPrivacy($company, $service, $dryRun);
            $this->setIdsForCompany($company, $dryRun);

            /** Changing the RO service to UPGRADED and set the expiration date to today */
            $service->setStateId(Service::STATE_UPGRADED);
            if (!$dryRun) {
                $this->serviceRepository->saveEntity($service);
            }
            $count++;
        }

        $this->logger->debug(sprintf('Fixed total of %s companies', $count));
    }

    private function addFullPrivacy(Company $company, Service $currentService, bool $dryRun): void
    {
        $this->logger->debug(sprintf('Adding Full Privacy to company %s', $company->getId()));
        $package = FNode::getProductById(Package::PACKAGE_FULL_PRIVACY);

        $service = new Service($package->serviceTypeId, $package, $company, $currentService->getOrderItem());
        $currentService->getOrderItem()->getOrder()->addService($service);
        $company->addService($service);

        $this->logger->debug(sprintf('Setting the Renewal Product (%s) to Full Privacy to company %s', $package->getRenewalProduct()->getName(), $company->getId()));
        $service->setRenewalProduct($package->getRenewalProduct());

        if ($currentService->getDtStart()) {
            $this->logger->debug(sprintf('Setting the DT Start (%s) to Full Privacy to company %s', $currentService->getDtStart(), $company->getId()));
            $service->setDtStart($currentService->getDtStart());
        }

        if ($currentService->getDtExpires()) {
            $this->logger->debug(sprintf('Setting the DT Expires (%s) to Full Privacy to company %s', $currentService->getDtExpires(), $company->getId()));
            $service->setDtExpires($currentService->getDtExpires());
        }

        $this->logger->debug(sprintf('Setting the State ID from the current service (%s) to the Full Privacy to company %s', $currentService->getStateId(), $company->getId()));
        $service->setStateId($currentService->getStateId());

        foreach ($package->getPackageProducts('includedProducts') as $product) {
            if ($product->hasServiceType()) {
                $this->logger->debug(sprintf('Adding included product (%s) to company %s', $product->serviceTypeId, $company->getId()));
                $childService = new Service($product->serviceTypeId, $product, $company, $currentService->getOrderItem());
                $service->addChild($childService);
            }
        }

        if (!$dryRun) {
            $this->serviceService->saveService($service);
        }
    }

    private function setIdsForCompany(Company $company, bool $dryRun): bool
    {
        try {
            $this->logger->debug(sprintf('Adding product %s to company %s', Product::PRODUCT_REGISTERED_OFFICE, $company->getId()));
            $company->setRegisteredOfficeId(Product::PRODUCT_REGISTERED_OFFICE);
            $this->logger->debug(sprintf('Adding product %s to company %s', Product::PRODUCT_SERVICE_ADDRESS, $company->getId()));
            $company->setServiceAddressId(Product::PRODUCT_SERVICE_ADDRESS);
            if (!$dryRun) {
                $this->companyRepository->saveEntity($company);
            }
            $this->logger->debug(sprintf('Company %s fixed successfully.', $company->getId()));
            return true;
        } catch (ORMInvalidArgumentException $exception) {
            $this->logger->debug(
                sprintf('Error while saving the IDs for company %s', $company->getId()),
                [
                    'exception' => $exception,
                    'company' => $company
                ]
            );
            return false;
        }
    }

}