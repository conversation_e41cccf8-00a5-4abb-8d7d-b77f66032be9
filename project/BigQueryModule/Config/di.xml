<?xml version="1.0" ?>
<container xmlns="http://symfony.com/schema/dic/services"
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">
    <services>
        <defaults public="true" />
        <service class="BigQueryModule\Facades\BigQueryFacade" id="big_query_module.facades.big_query_facade"/>
        <service class="BigQueryModule\Factories\BigQueryFactory" id="big_query_module.factories.big_query_factory"/>
        <service class="BigQueryModule\ApiClients\BigQueryApiClient" id="big_query_module.api_clients.big_query_api_client">
            <argument>%big_query_credentials%</argument>
            <argument id="error.loggers.monolog" type="service" />
        </service>
        <service class="BigQueryModule\Repositories\BigQueryRepository" id="big_query_module.repositories.big_query_repository">
            <argument id="big_query_module.api_clients.big_query_api_client" type="service"/>
            <argument id="error.loggers.monolog" type="service" />
            <argument id="big_query_module.facades.big_query_facade" type="service" />
        </service>
    </services>
</container>