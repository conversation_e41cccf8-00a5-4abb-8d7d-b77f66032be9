<?php

declare(strict_types = 1);

namespace Services;

use Config\Constants\EventLocator;
use CustomerModule\Entities\BusinessInformation;
use CustomerModule\Entities\InvoiceAddress;
use CustomerModule\Repositories\InvoiceAddressRepository;
use Dispatcher\Events\CustomerEvent;
use Entities\Customer;
use Iterator;
use Libs\Exceptions\EntityNotFound;
use Utils\NetteSmartObject;
use OrmModule\Exceptions\EntityException;
use Repositories\CustomerRepository;
use Symfony\Component\EventDispatcher\EventDispatcher;
use UserModule\Contracts\ICustomer;
use UserModule\Domain\Credentials;

class CustomerService extends NetteSmartObject
{
    /**
     * @var CustomerRepository
     */
    private $repository;

    /**
     * @var InvoiceAddressRepository
     */
    private $invoiceAddressRepository;

    /**
     * @var EventDispatcher
     */
    private $dispatcher;

    public function __construct(
        CustomerRepository $repository,
        InvoiceAddressRepository $invoiceAddressRepository,
        EventDispatcher $dispatcher
    )
    {
        $this->repository = $repository;
        $this->invoiceAddressRepository = $invoiceAddressRepository;
        $this->dispatcher = $dispatcher;
    }

    /**
     * @param Customer|ICustomer $entity
     *
     * @return Customer
     */
    public function save(Customer $entity)
    {
        return $this->repository->saveEntity($entity);
    }

    public function saveBusinessDetails(BusinessInformation $businessInformation)
    {
        return $this->repository->saveEntity($businessInformation);
    }

    /**
     * @param int $id
     * @param bool $need
     *
     * @throws EntityNotFound
     *
     * @return Customer
     */
    public function getCustomerById($id, $need = false)
    {
        $entity = $this->repository->find($id);
        if (!$entity && $need) {
            throw new EntityNotFound("Customer `$id`` not found");
        }
        return $entity;
    }

    /**
     * @param string $email
     * @param bool $need
     *
     * @throws EntityNotFound
     *
     * @return Customer
     */
    public function getCustomerByEmail(string $email, bool $need = false)
    {
        $entity = $this->repository->findOneBy(['email' => $email]);
        if (!$entity && $need) {
            throw new EntityNotFound("Customer `$email`` not found");
        }
        return $entity;
    }

    /**
     * @return Customer[]
     */
    public function getCustomersByName(string $name, ?string $surname = null): array
    {
        $q = $this->repository->createQueryBuilder('c')
            ->where('c.firstName LIKE :forename')
            ->orWhere('c.lastName LIKE :surname')
            ->setMaxResults(20)
            ->setParameter('forename', sprintf('%%%s%%', $name))
            ->setParameter('surname', sprintf('%%%s%%', $surname ?? $name))
            ->getQuery();

        return $q->getResult();
    }

    /**
     * @param Customer $customer
     * @param float $credit
     */
    public function addCredit(Customer $customer, $credit)
    {
        $customer->addCredit($credit);
        $this->save($customer);
    }

    /**
     * @param InvoiceAddress $address
     */
    public function removeInvoiceAddress(InvoiceAddress $address)
    {
        $address->getCustomer()->removeInvoiceAddress();
        $this->invoiceAddressRepository->removeEntity($address);
    }

    public function createAccount(Credentials $credentials): Customer
    {
        $customer = new Customer($credentials->getUsername(), $credentials->getPassword());
        $this->save($customer);
        $this->dispatcher->dispatch(new CustomerEvent($customer), EventLocator::CUSTOMER_CREATED);
        return $customer;
    }

    /**
     * @param Customer $customer
     */
    public function setAsValidated(Customer $customer)
    {
        $customer->setStatusId(Customer::STATUS_VALIDATED);
        $this->save($customer);
    }

    /**
     * @param int $id
     *
     * @return Customer
     *
     * @throws EntityException
     */
    public function required($id)
    {
        $customer = $this->getCustomerById($id);

        if (!$customer) {
            throw EntityException::notFound($id);
        }

        return $customer;
    }

    public function getSagepayCustomersWithActiveToken(): Iterator
    {
        return $this->repository->getSagepayCustomersWithActiveToken();
    }
}
