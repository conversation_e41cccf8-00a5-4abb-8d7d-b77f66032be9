<?php

namespace Services\OldServices;

use BootstrapModule\Singletons\StaticContainer;
use EmailModule\Exceptions\EmailException;
use Framework\FTools;
use Models\OldModels\Customer;
use Psr\Log\LoggerInterface;
use Services\Email\EmailerFactory;
use Services\Payment\PaymentResponse;
use Services\Registry;
use Utils\NetteSmartObject;

class CustomerService extends NetteSmartObject
{
    /**
     * @param PaymentResponse $paymentResponse
     * @param string|null $email
     * @param string|null $roleId
     * @return Customer
     */
    public function createPaymentCustomer(PaymentResponse $paymentResponse, $email = NULL, $roleId = NULL)
    {
        $newCustomer = new Customer;
        $newCustomer->email = $email ? $email : $paymentResponse->getPaymentHolderEmail();
        $newCustomer->password = FTools::generPwd(8);
//        $newCustomer->firstName = $paymentResponse->getPaymentHolderFirstName();
//        $newCustomer->lastName = $paymentResponse->getPaymentHolderLastName();
//        $newCustomer->address1 = $paymentResponse->getPaymentHolderAddressStreet();
        //$newCustomer->address2 = $newCustomerAddress->getLine2() ? $newCustomerAddress->getLine2() : NULL;
//        $newCustomer->city = $paymentResponse->getPaymentHolderAddressCity();
//        $newCustomer->postcode = $paymentResponse->getPaymentHolderAddressPostCode();
//        $newCustomer->countryId = $this->getTranslateCountry($paymentResponse->getPaymentHolderAddressCountry());
//        $newCustomer->country = $paymentResponse->getPaymentHolderAddressState();
        if ($roleId) {
            $newCustomer->roleId = $roleId;
        }
        $customerId = $newCustomer->save();

        try {
            //send welcome email
            $emailer = Registry::$emailerFactory->get(EmailerFactory::USER);
            $emailer->sendWelcomeNewCustomerEmail($newCustomer);
        } catch (EmailException $e) {
            /** @var LoggerInterface $logger */
            $logger = StaticContainer::get('error.loggers.monolog');
            $logger->error('Unable to send welcome new customer email', ['e' => $e]);
        }
        
        return new Customer($customerId);
    }
}