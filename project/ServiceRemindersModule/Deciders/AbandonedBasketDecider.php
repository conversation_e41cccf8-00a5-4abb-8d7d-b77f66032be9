<?php


namespace ServiceRemindersModule\Deciders;

use DateTime;
use EmailModule\Repositories\PredefinedEmailRepository;
use Entities\Customer;
use ServiceRemindersModule\Commands\AbandonedBasketCommand;
use ServiceRemindersModule\Emailers\AbandonedBasketEmailer;
use WorkflowEngineModule\Repositories\IEmailEventRepository;

class AbandonedBasketDecider
{
    /**
     * @var PredefinedEmailRepository
     */
    private $emailRepository;

    /**
     * @var IEmailEventRepository
     */
    private $emailEventRepository;


    public function __construct(
        PredefinedEmailRepository $emailRepository,
        IEmailEventRepository $emailEventRepository
    )
    {
        $this->emailRepository = $emailRepository;
        $this->emailEventRepository = $emailEventRepository;
    }


    public function emailWasNotSentPreviously(Customer $customer, string $emailName): bool
    {
        $emailData = $this->getEmailData($emailName);

        return !$this
            ->emailEventRepository->getEmailsSentFrom(
                $customer->getEmail(),
                $emailData['emailTemplateId'],
                $emailData['campaign'],
                new DateTime('-1 day')
            );
    }

    private function getEmailData(string $emailId): array
    {

        if ($emailId === AbandonedBasketCommand::ABANDONED_BASKET) {
            return [
                'emailTemplateId' => AbandonedBasketEmailer::ABANDONED_BASKET_TEMPLATE_ID,
                'campaign' => AbandonedBasketCommand::ABANDONED_BASKET
            ];
        } else {
            return [
                'emailTemplateId' => AbandonedBasketEmailer::ABANDONED_BASKET_FORMATION_TEMPLATE_ID,
                'campaign' => AbandonedBasketCommand::ABANDONED_BASKET_FORMATIONS
            ];
        }

    }


}