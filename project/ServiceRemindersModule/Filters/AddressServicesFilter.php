<?php

namespace ServiceRemindersModule\Filters;

use Entities\Service;
use Models\View\ServiceView;
use ServiceRemindersModule\Deciders\AddressEligibilityDecider;
use ServiceRemindersModule\Entities\Reminder;
use ServiceRemindersModule\Entities\ReminderService;

class AddressServicesFilter
{
    /**
     * @var AddressEligibilityDecider
     */
    private $addressEligibilityDecider;

    public function __construct(AddressEligibilityDecider $addressEligibilityDecider)
    {
        $this->addressEligibilityDecider = $addressEligibilityDecider;
    }

    /**
     * @param ReminderService[] $services
     * @return ReminderService[]
     */
    public function filter(array $services): array
    {
        $filteredServices = [];
        
        foreach ($services as $service) {
            if ($this->addressEligibilityDecider->isAddressType($service)) {
                $filteredServices[] = $service;
            }
        }
        
        return $filteredServices;
    }
}