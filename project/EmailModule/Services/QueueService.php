<?php

namespace EmailModule\Services;

use EmailModule\Factories\EmailFactory;
use EmailModule\IEmail;
use EmailModule\IEmailLog;
use EmailModule\IEmailLogger;
use EmailModule\Mailers\IEmailer;
use EmailModule\Mailers\MailgunMailer;
use LoggableModule\Entities\EmailLog;

class QueueService
{
    /**
     * @var IEmailLogger
     */
    private $emailLogger;

    /**
     * @var EmailFactory
     */
    private $emailFactory;

    /**
     * @var IEmailer
     */
    private $cmsMailer;

    /**
     * @var IEmailer
     */
    private $marketingMailer;

    /**
     * @var IEmailer
     */
    private $tsbMailer;

    /**
     * @var IEmailer
     */
    private $coolMailer;

    public function __construct(
        IEmailLogger $emailLogger,
        EmailFactory $emailFactory,
        IEmailer $cmsMailer,
        IEmailer $marketingMailer,
        IEmailer $tsbMailer,
        IEmailer $coolMailer
    )
    {
        $this->emailLogger = $emailLogger;
        $this->emailFactory = $emailFactory;
        $this->cmsMailer = $cmsMailer;
        $this->marketingMailer = $marketingMailer;
        $this->tsbMailer = $tsbMailer;
        $this->coolMailer = $coolMailer;
    }

    public function add(IEmail $email, $customer = null, string $gateway = IEmailLog::EMAIL_GATEWAY_CMS): IEmailLog
    {
        return $this->emailLogger->log($email, $customer, $gateway, true);
    }

    public function send(EmailLog $emailLog): void
    {
        if ($emailLog->isSent()) {
            return;
        }

        $email = $this->emailFactory->fromEmailLog($emailLog);

        switch ($emailLog->getGateway()) {
            case IEmailLog::EMAIL_GATEWAY_CMS:
                $this->cmsMailer->send($email);
                break;
            case IEmailLog::EMAIL_GATEWAY_MARKETING:
                $this->marketingMailer->send($email);
                break;
            case IEmailLog::EMAIL_GATEWAY_TSB:
                $this->tsbMailer->send($email);
                break;
            case IEmailLog::EMAIL_GATEWAY_COOL:
                $this->coolMailer->send($email);
                break;
            default:
                $this->cmsMailer->send($email);
        }

        foreach ($email->getAttachments() as $fileAttachment) {
            unlink($fileAttachment['file']);
        }
    }
}