<?php

namespace BankingModule\Controllers;

use BankingModule\Exceptions\MultipleTsbBankDetailsForCompanyException;
use BankingModule\Exceptions\TsbApplicationInvalidParametersException;
use BankingModule\Facades\TsbApplicationFacade;
use Entities\Company;
use Entities\Customer;
use Psr\Log\LoggerInterface;
use RouterModule\Helpers\ControllerHelper;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Response;
use TemplateModule\Renderers\IRenderer;

class TsbApplicationController
{
    /**
     * @var IRenderer
     */
    private $renderer;

    /**
     * @var ControllerHelper
     */
    private $controllerHelper;

    /**
     * @var TsbApplicationFacade
     */
    private $tsbApplicationFacade;

    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * @param ControllerHelper $controllerHelper
     * @param TsbApplicationFacade $tsbApplicationFacade
     * @param LoggerInterface $logger
     * @param IRenderer $renderer
     */
    public function __construct(
        IRenderer $renderer,
        ControllerHelper $controllerHelper,
        TsbApplicationFacade $tsbApplicationFacade,
        LoggerInterface $logger
    )
    {
        $this->renderer = $renderer;
        $this->controllerHelper = $controllerHelper;
        $this->tsbApplicationFacade = $tsbApplicationFacade;
        $this->logger = $logger;
    }

    /**
     * @return RedirectResponse|Response
     */
    public function addTsbBankDetails(Customer $customer, Company $company, $companyNumber)
    {
        try {
            $this->tsbApplicationFacade->addTsbBankDetails($customer, $company, $companyNumber);
        } catch (TsbApplicationInvalidParametersException $e) {
            return new RedirectResponse($this->controllerHelper->url('homepage'));
        } catch (MultipleTsbBankDetailsForCompanyException $e) {
            $this->logger->error($e);
        }

        return $this->renderer->render();
    }
}
