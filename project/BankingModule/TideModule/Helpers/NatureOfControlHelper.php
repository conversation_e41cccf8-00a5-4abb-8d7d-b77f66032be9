<?php

namespace BankingModule\TideModule\Helpers;

use Nette\Utils\Arrays;

class NatureOfControlHelper
{
    /**
     * @var array
     */
    private $natureOfControl = [];

    /**
     * @param array $natureOfControl
     */
    public function __construct(array $natureOfControl)
    {
        $this->natureOfControl = $natureOfControl;
    }

    /**
     * @param string $ownershipOfShares
     * @return bool
     */
    public function hasDirectControl($ownershipOfShares)
    {
        return Arrays::get($this->natureOfControl, [$ownershipOfShares, 'has_direct_control'], FALSE);
    }

    /**
     * @param $ownershipOfShares
     * @return integer
     */
    public function getSharesRatio($ownershipOfShares)
    {
        return Arrays::get($this->natureOfControl, [$ownershipOfShares, 'lower'], 0);
    }
}
