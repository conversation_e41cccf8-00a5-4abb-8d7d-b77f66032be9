{extends '@structure/layout.tpl'}

{block content}
    <div class="width100 bg-white padcard">
        <div class="container">
            <div class="row">
                <div class="col-xs-12 col-md-9">
                    <h1>{$introductionStrip.title nofilter}</h1>
                    <p class="lead margin0">{$introductionStrip.lead nofilter}</p>
                </div>
                {if isset($introductionStrip.imgFeature)}
                    <div class="col-xs-12  col-md-3 hidden-xs">
                        <img class="img-responsive center-block" src="{$introductionStrip.imgFeature nofilter}" alt="{$introductionStrip.imgFeatureDescription nofilter}">
                        <img class="img-responsive center-block" src="/front/imgs/barclays/barclays_award.png" alt="<PERSON><PERSON> is the winner of Business Moneyfacts Award 2018 - Business Start-up Bank of the year" style="width: 110px; margin-top: 15px;">
                    </div>
                {/if}
            </div>
        </div>
    </div>
    <div class="width100 bg-white pad8">
        <div class="container">
            <div class="row">
                {if isset($featuresStrip.features)}
                    <div class="col-xs-12">
                        <h3 class="top0 btm10">{$featuresStrip.featureTitle nofilter}</h3>
                    </div>
                    {foreach $featuresStrip.features as $feature}
                        <div class="col-xs-12">
                            <div class="row">
                                <div class="col-xs-12 col-sm-4 col-lg-3">
                                    <img class="img-responsive top20 center-block why-barclays-img-size" src="{$feature.imgFeature nofilter}" alt="{$feature.title nofilter}">
                                </div>
                                <div class="col-xs-12 col-sm-7 col-lg-8">
                                    <h4 class="top20">{$feature.title nofilter}</h4>
                                    <p class="top20">{$feature.description nofilter}</p>
                                </div>
                            </div>
                        </div>
                    {/foreach}
                {/if}
            </div>
        </div>
    </div>
    <div class="width100 bg-white pad8">
        <div class="container">
            <div class="row">
                <div class="col-xs-12">
                    <h3 class="top20 btm10">{$youAlsoGetStrip.title nofilter}</h3>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-12">
                    <div class="top10">
                        {$youAlsoGetStrip.description nofilter}
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-12">
                    <div class="top10">
                        {$youAlsoGetStrip.note nofilter}
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="width100 bg-white pad8">
        <div class="container">
            <div class="row">
                <div class="col-xs-12">
                    <h3 class="top20 btm10">{$otherServicesStrip.title nofilter}</h3>
                </div>
            </div>
            <div class="row">
                {foreach $otherServicesStrip.boxes as $bluebox}
                    <div class="col-xs-12">
                        {foreach $bluebox as $box}
                            <div class="col-md-4 blueborder barclays-boxes-position">
                                <div class="row">
                                    <div class="col-md-12 barclays-box-img">
                                        <img class="barclays-boxes-img-size" src="{$box.image nofilter}" alt="{$box.imageDescription nofilter}">
                                    </div>
                                </div>
                                <div class="row btm20">
                                    <div class="col-md-12">
                                        <h4 class="top10 btm10">{$box.title nofilter}</h4>
                                        <p>{$box.description nofilter}</p>
                                    </div>
                                </div>
                            </div>
                        {/foreach}
                    </div>
                {/foreach}
            </div>
        </div>
    </div>

    {include 'BankingModule/Templates/@blocks/moreInfoStrip.tpl' stripData=$howItWorksStrip.blocks}

    <div class="width100 bg-white padcard">
        <div class="container">
            <div class="row companyservices-matrix-block">
                <div class="col-xs-12">
                    <div class="row text-center button-wide-mobile">
                        <h3 class="top0 btm20">{$matrixStrip.title nofilter}</h3>
                        <a id="{$matrixStrip.linkId nofilter}" href="{$matrixStrip.link nofilter}" class="btn btn-lg
                         btn-default">{$matrixStrip.cta nofilter}</a>
                        <p class="top20 btm0">{$matrixStrip.description nofilter}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="width100 bg-grey1 padcard">
        <div class="container">
            <div class="row">
                <div class="col-xs-12">
                    <h5>{$faqStrip.title nofilter}</h5>
                    {foreach $faqStrip.tandc as $tandc}
                        <p class="small">{$tandc.description nofilter}</p>
                    {/foreach}
                </div>
            </div>
        </div>
    </div>
{/block}
