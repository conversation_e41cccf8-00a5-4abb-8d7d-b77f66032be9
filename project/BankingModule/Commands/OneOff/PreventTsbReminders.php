<?php

namespace BankingModule\Commands\OneOff;

use BankingModule\BankingService;
use BankingModule\Config\EventLocator;
use Services\EventService;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class PreventTsbReminders extends Command
{
    /**
     * @var BankingService
     */
    private $bankingService;

    /**
     * @var EventService
     */
    private $eventService;

    /**
     * @param BankingService $bankingService
     * @param EventService $eventService
     * @param string|NULL $name
     */
    public function __construct(
        BankingService $bankingService,
        EventService $eventService,
        $name = NULL
    )
    {
        $this->bankingService = $bankingService;
        $this->eventService = $eventService;
        parent::__construct($name);
    }

    protected function configure()
    {
        $this
            ->setName('tsb:prevent_reminders')
            ->setDescription('TSB Suspension: prevents reminders to be sent while (and after) the service is suspended.')
            ->addOption(
                'dryRun',
                'd',
                InputOption::VALUE_NONE,
                'Test this command'
            );
    }

    /**
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int|null|void
     */
    public function execute(InputInterface $input, OutputInterface $output)
    {
        $isDryRun = $input->getOption('dryRun');
        $first = $second = 0;
        $leads = $this->bankingService->getTsbLeadsToPreventReminders();
        foreach ($leads as $lead) {
            if (
                !$this->eventService->eventOccurred(
                    EventLocator::TSB_APPLICATION_FIRST_REMINDER,
                    $lead->getId()
                )
            ) {
                $first++;
                $output->writeln(
                    sprintf(
                        'Lead %s for company %s (1st reminder) will be removed!',
                        $lead->getId(),
                        $lead->getCompany()->getId()
                    )
                );
                if (!$isDryRun) {
                    $this->eventService->notify(
                        EventLocator::TSB_APPLICATION_FIRST_REMINDER,
                        $lead->getId()
                    );
                }
            }
            if (
                !$this->eventService->eventOccurred(
                    EventLocator::TSB_APPLICATION_SECOND_REMINDER,
                    $lead->getId()
                )
            ) {
                $second++;
                $output->writeln(
                    sprintf(
                        'Lead %s for company %s (2nd reminder) will be removed!',
                        $lead->getId(),
                        $lead->getCompany()->getId()
                    )
                );
                if (!$isDryRun) {
                    $this->eventService->notify(
                        EventLocator::TSB_APPLICATION_FIRST_REMINDER,
                        $lead->getId()
                    );
                }
            }
        }
        $output->writeln(
            sprintf("Command has generated %d events for the first and %d for the second reminder.", $first, $second)
        );
    }
}