<?php

namespace ProductModule\Deciders;

use Entities\Company;
use Models\Products\BundlePackage;
use Models\Products\Product;

class DormantCompanyAccountsDecider
{

    public function hasAnyDormantCompanyAccountService(int $productId): bool
    {
        return Product::PRODUCT_DORMANT_COMPANY_ACCOUNTS === $productId
            || Product::PRODUCT_DORMANT_COMPANY_ACCOUNTS_EXPRESS === $productId
            || BundlePackage::BUNDLE_DORMANT_COMPANY_ACCOUNTS_CONFIRMATION_STATEMENT === $productId;
    }

    public function canCompanyAddProduct(Company $company): bool
    {
        return !$company->isLlpType();
    }

}