<?php

namespace CustomerModule\Entities;

use Doctrine\ORM\Mapping as Orm;
use Entities\Customer;
use Gedmo\Mapping\Annotation as Gedmo;
use UserModule\Contracts\IAddress;

/**
 * @Orm\Entity(repositoryClass="CustomerModule\Repositories\InvoiceAddressRepository")
 */
class InvoiceAddress extends CustomerAddress
{
    /**
     * @var Customer
     *
     * @Orm\OneToOne(targetEntity="Entities\Customer", inversedBy="invoiceAddress")
     * @Orm\JoinColumn(name="customerId", referencedColumnName="customerId")
     */
    protected $customer;

    private function __construct()
    {
    }

    /**
     * @param IAddress $addressData
     * @return InvoiceAddress
     */
    public static function fromAddress(IAddress $addressData)
    {
        $self = new self();

        $self->setRecipientName($addressData->getRecipientName());
        $self->setAddress1($addressData->getAddress1());
        $self->setAddress2($addressData->getAddress2());
        $self->setCity($addressData->getCity());
        $self->setPostcode($addressData->getPostCode());
        $self->setCountryIso($addressData->getCountryIso());

        return $self;
    }

    /**
     * @return Customer
     */
    public function getCustomer()
    {
        return $this->customer;
    }

    /**
     * @param Customer $customer
     */
    public function setCustomer(Customer $customer)
    {
        $this->customer = $customer;
    }
}
