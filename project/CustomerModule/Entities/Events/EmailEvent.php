<?php

namespace CustomerModule\Entities\Events;

use EmailModule\IEmailLog;
use Doctrine\ORM\Mapping as Orm;
use Entities\Customer;

/**
 * @Orm\Entity
 */
class EmailEvent extends CustomerEvent_Abstract
{
    /**
     * @var IEmailLog
     * @Orm\OneToOne(targetEntity="LoggableModule\Entities\EmailLog")
     * @Orm\JoinColumn(name="emailLogId", referencedColumnName="emailLogId")
     */
    private $emailLog;

    public function __construct(Customer $customer, IEmailLog $emailLog, string $actionBy)
    {
        parent::__construct($customer, $actionBy);
        $this->emailLog = $emailLog;
    }
}