<?xml version="1.0" ?>

<container xmlns="http://symfony.com/schema/dic/services"
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">
    <services>
        <defaults public="true" />
        <service id="voucher_module.services.voucher_service" class="VoucherModule\Services\VoucherService" >
            <argument type="service" id="voucher_module.repositories.voucher_repository"/>
        </service>

        <service class="VoucherModule\Repositories\VoucherRepository" id="voucher_module.repositories.voucher_repository">
            <factory service="doctrine.orm.entity_manager" method="getRepository"/>
            <argument>VoucherModule\Entities\Voucher</argument>
        </service>

        <service id="voucher_module.validators.voucher_exists_validator" class="VoucherModule\Validators\VoucherExistsValidator">
            <argument type="service" id="voucher_module.services.voucher_service"/>
            <tag name="validator.constraint_validator" alias="voucher_module.validators.voucher_exists"/>
        </service>

        <service id="voucher_module.validators.voucher_enabled_validator" class="VoucherModule\Validators\VoucherEnabledValidator">
            <argument type="service" id="voucher_module.services.voucher_service"/>
            <tag name="validator.constraint_validator" alias="voucher_module.validators.voucher_enabled"/>
        </service>

        <service id="voucher_module.validators.minimal_spend_reached_validator" class="VoucherModule\Validators\MinimalSpendReachedValidator">
            <argument type="service" id="voucher_module.services.voucher_service"/>
            <argument type="service" id="basket"/>
            <tag name="validator.constraint_validator" alias="voucher_module.validators.minimal_spend_reached"/>
        </service>
    </services>
</container>
