<?xml version="1.0" ?>

<container xmlns="http://symfony.com/schema/dic/services"
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">
    <services>
        <defaults public="true" />
        <service class="MyServicesModule\Views\MyServicesView" id="my_services_module.views.my_services_view">
            <factory class="MyServicesModule\Views\MyServicesViewFactory" method="create"/>
            <argument>%my_services.info_links%</argument>
            <argument>%service_module.late_payment_fee_product_id%</argument>
        </service>

        <service id="my_services_module.facades.settings_facade" class="MyServicesModule\Facades\SettingsFacade">
            <argument type="service" id="service_settings_module.facades.service_settings_facade"/>
        </service>

        <service id="my_services_module.controllers.service_settings_controller" class="MyServicesModule\Controllers\ServiceSettingsController">
            <argument type="service" id="router_module.helpers.controller_helper" />
            <argument type="service" id="session.logged_in_customer" />
            <argument type="service" id="services.service_service" />
            <argument type="service" id="service_settings_module.services.service_settings_service" />
            <argument type="service" id="services.company_service" />
            <argument type="service" id="my_services_module.facades.settings_facade"/>
            <argument type="service" id="my_services_module.renderers.company_block_renderer"/>
            <argument type="service" id="my_services_module.views.my_services_view" />
            <argument type="service" id="symfony.request"/>
        </service>

        <service id="my_services_module.renderers.company_block_renderer" class="MyServicesModule\Renderers\CompanyBlockRenderer">
            <argument id="ftemplate" type="service"/>
            <argument id="services.company_service" type="service"/>
            <argument id="services.service_service" type="service"/>
            <argument id="service_settings_module.services.service_settings_service" type="service"/>
            <argument id="factories.front.company_view_factory" type="service"/>
        </service>

        <service class="MyServicesModule\Repositories\AutoRenewalFinder" id="my_services_module.repositories.auto_renewal_finder">
            <argument id="repositories.customer_repository" type="service"/>
        </service>

    </services>
</container>
