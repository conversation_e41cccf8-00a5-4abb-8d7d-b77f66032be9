<?xml version="1.0" ?>

<container xmlns="http://symfony.com/schema/dic/services"
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">
    <services>
        <defaults public="true" />
        <service id="femail_factory" class="Services\Email\FEmailFactory"/>

        <service id="tax_assist_accounts_emailer" class="Services\Emailers\TaxAssistAccountsEmailer">
            <argument type="service" id="email_module.gateways.default_gateway"/>
        </service>
        <service class="Services\Emailers\CompanyEmailer" id="company_emailer">
            <argument id="email_module.gateways.default_gateway" type="service"/>
            <argument id="email_module.loaders.front_matter_template_loader" type="service"/>
            <argument id="url_generator" type="service"/>
            <argument id="id_module.repositories.id_info_repository" type="service"/>
            <argument id="user_module.creators.one_time_password_auth_token_creator" type="service"/>
        </service>
        <service id="journey_emailer" class="Services\Emailers\JourneyEmailer">
            <argument type="service" id="email_module.gateways.default_gateway"/>
        </service>
        <service id="emailers.services_emailer" class="Services\Emailers\ServicesEmailer">
            <argument type="service" id="email_module.gateways.default_gateway" />
            <argument type="service" id="nette.templating.file_template"/>
            <argument type="service" id="symfony.router"/>
            <argument type="service" id="user_module.creators.one_time_password_auth_token_creator"/>
            <argument id="email_module.loaders.front_matter_template_loader" type="service"/>
        </service>
        <service class="Services\Emailers\CompanyNameEmailer" id="company_name_emailer">
            <argument id="email_module.gateways.marketing_gateway" type="service"/>
            <argument id="email_module.loaders.front_matter_template_loader" type="service"/>
        </service>

        <service id="emailers.token_emailer" class="Services\Emailers\TokenEmailer">
            <argument type="service" id="email_module.gateways.default_gateway"/>
            <argument id="email_module.loaders.front_matter_template_loader" type="service"/>
        </service>
        <service id="error_testing_emailer" class="Services\Emailers\ErrorTestingEmailer">
            <argument id="email_module.loaders.front_matter_template_loader" type="service"/>
            <argument type="service" id="email_module.gateways.default_gateway"/>
        </service>
        <service class="Services\Emailers\UserEmailer" id="user_emailer">
            <argument id="email_module.loaders.front_matter_template_loader" type="service"/>
            <argument type="service" id="email_module.gateways.default_gateway"/>
        </service>

        <service id="contact_us_emailer" class="Services\Emailers\ContactUsEmailer">
            <argument type="service" id="email_module.gateways.default_gateway"/>
        </service>
        <service id="emailer.cashback" class="Services\Emailers\CashBackEmailer">
            <argument type="service" id="email_module.gateways.default_gateway"/>
        </service>
        <service id="emailer.wholesale.professional" class="Services\Emailers\Wholesale\ProfessionalEmailer">
            <argument id="email_module.loaders.front_matter_template_loader" type="service"/>
            <argument type="service" id="email_module.gateways.default_gateway"/>
        </service>
        <service id="emailer.wholesale.icaew" class="Services\Emailers\Wholesale\ICAEWEmailer">
            <argument type="service" id="email_module.gateways.default_gateway"/>
        </service>
        <service id="emailer.wholesale.tax_assists" class="Services\Emailers\Wholesale\TaxAssistEmailer">
            <argument type="service" id="email_module.gateways.default_gateway"/>
        </service>
        <service id="payment.paypal_emailer" class="Payment\PaypalEmailer">
            <argument type="service" id="email_module.gateways.default_gateway"/>
        </service>
        <service id="order_emailer" class="Services\Emailers\OrderEmailer">
            <argument type="service" id="email_module.gateways.default_gateway"/>
            <argument id="email_module.loaders.front_matter_template_loader" type="service"/>
        </service>
        <service id="payment_emailer" class="Services\Emailers\PaymentEmailer">
            <argument type="service" id="email_module.gateways.default_gateway"/>
            <argument type="service" id="services.order_service"/>
            <argument type="service" id="order_module.fpdf.invoice_generator"/>
            <argument id="email_module.loaders.front_matter_template_loader" type="service"/>
        </service>
        <service class="ForgottenPasswordEmailer" id="forgotten_password_emailer">
            <argument type="service" id="email_module.gateways.default_gateway" />
            <argument type="service" id="femail_factory"/>
        </service>
    </services>
</container>
