<?xml version="1.0" ?>

<container xmlns="http://symfony.com/schema/dic/services"
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">
    <services>
        <defaults public="true" />

        <service id="event_dispatcher" class="Symfony\Component\EventDispatcher\EventDispatcher" />

        <service class="Symfony\Component\EventDispatcher\DependencyInjection\RegisterListenersPass" id="symfony.component.event_dispatcher.dependency_injection.register_listeners_pass">
            <tag name="compiler_pass"/>
        </service>

        <service id="symfony.event_dispatcher" alias="event_dispatcher" />

        <service id="dispatcher.listeners.incorporation_listener" class="Dispatcher\Listeners\IncorporationListener">
            <argument type="service" id="company_emailer"/>
            <argument type="service" id="company_module.repositories.company_event_repository"/>
            <argument type="service" id="companies_house_module.services.submission_handler"/>
            <argument type="service" id="company_formation_module.deciders.registration_review_decider"/>
            <tag name="kernel.event_subscriber"/>
        </service>
        <service id="session.order_summary" class="Legacy\Nette\Web\SessionNamespace">
            <factory service="session" method="getNamespace" />
            <argument>orderSummary</argument>
        </service>
        <service id="session.reenable_auto_renewal" class="Legacy\Nette\Web\SessionNamespace">
            <factory service="session" method="getNamespace" />
            <argument>reenableAutoRenewal</argument>
        </service>
        <service id="dispatcher.listeners.order_listener" class="Dispatcher\Listeners\OrderListener">
            <argument type="service" id="session"/>
            <tag name="kernel.event_subscriber" />
        </service>
        <service id="dispatcher.listeners.service_listener" class="Dispatcher\Listeners\ServiceListener">
            <argument type="service" id="services.service_service"/>
            <argument type="service" id="service_activator_module.service_activator"/>
            <tag name="kernel.event_subscriber" />
        </service>
        <service id="dispatcher.listeners.services_email_listener" class="Dispatcher\Listeners\ServicesEmailListener">
            <argument type="service" id="emailers.services_emailer"/>
            <tag name="kernel.event_subscriber" />
        </service>
        <service id="dispatcher.listeners.token_email_listener" class="Dispatcher\Listeners\TokenEmailListener">
            <argument type="service" id="emailers.token_emailer"/>
            <tag name="kernel.event_subscriber" />
        </service>
        <service id="dispatcher.listeners.token_listener" class="Dispatcher\Listeners\TokenListener">
            <argument type="service" id="services.payment.token_service"/>
            <argument type="service" id="payment_module.payment_types.sage_payment"/>
            <argument type="service" id="sagepay.reporting.sagepay"/>
            <tag name="kernel.event_subscriber" />
        </service>
        <service id="dispatcher.listeners.company_house.submission_status_listener" class="Dispatcher\Listeners\CompanyHouse\SubmissionStatusListener">
            <argument id="services.submission_service" type="service"/>
            <argument id="services.company_service" type="service"/>
            <argument id="error_testing_emailer" type="service"/>
            <argument id="company_sync_module.services.synchronization_service" type="service"/>
            <argument id="company_sync_module.loggers.synchronization_logger" type="service"/>
            <argument id="cron.loggers.default_logger" type="service"/>
            <tag name="kernel.event_subscriber" />
        </service>
        <service id="dispatcher.listeners.company_house.incorporation_listener" class="Dispatcher\Listeners\CompanyHouse\IncorporationListener">
            <argument type="service" id="services.submission_service"/>
            <tag name="kernel.event_subscriber" />
        </service>
        <service id="dispatcher.listeners.company_house.annual_return_listener" class="Dispatcher\Listeners\CompanyHouse\AnnualReturnListener">
            <argument type="service" id="services.submission_service"/>
            <tag name="kernel.event_subscriber" />
        </service>
        <service id="dispatcher.listeners.company_house.change_of_name_listener" class="Dispatcher\Listeners\CompanyHouse\ChangeOfNameListener">
            <argument type="service" id="services.submission_service"/>
            <tag name="kernel.event_subscriber" />
        </service>
        <service id="dispatcher.listeners.cashback" class="Dispatcher\Listeners\CashbackListener">
            <argument type="service" id="services.cashback_service"/>
            <argument type="service" id="emailer.cashback"/>
            <argument type="service" id="error.loggers.monolog"/>
            <tag name="kernel.event_subscriber" />
        </service>
        <service id="dispatcher.listeners.customer" class="Dispatcher\Listeners\CustomerListener">
            <argument type="service" id="services.customer_service"/>
            <argument type="service" id="repositories.customer_repository"/>
            <argument type="service" id="omnipay_module.api_client.omnipay_api_client"/>
            <argument type="service" id="session" />
            <tag name="kernel.event_subscriber" />
        </service>
        <service id="listeners.signUp" class="Dispatcher\Listeners\SignUpListener">
            <tag name="kernel.event_subscriber" />
        </service>
        <service id="dispatcher.listeners.delete_unformed_company_listener" class="Dispatcher\Listeners\DeleteUnformedCompanyListener">
            <argument type="service" id="services.service_service" />
            <argument type="service" id="services.company_service" />
            <argument type="service" id="services.package_service"/>
            <tag name="kernel.event_subscriber" />
        </service>
        <service id="dispatcher.listeners.order_email" class="Dispatcher\Listeners\OrderEmailListener">
            <argument type="service" id="order_emailer"/>
            <tag name="kernel.event_subscriber" />
        </service>
        <service id="dispatcher.listeners.basket_listener" class="Dispatcher\Listeners\BasketListener">
            <argument type="string">Front\Payment\OrderSummaryForm</argument>
            <tag name="kernel.event_subscriber" />
        </service>
        <service id="dispatcher.listeners.nominee_director_listener" class="Dispatcher\Listeners\NomineeDirectorListener">
            <argument type="service" id="services.package_service" />
            <argument type="service" id="services.company_service"/>
            <tag name="kernel.event_subscriber" />
        </service>
        <service id="dispatcher.listeners.create_service_settings_listener" class="Dispatcher\Listeners\CreateServiceSettingsListener">
            <argument type="service" id="services.service_service"/>
            <argument type="service" id="service_settings_module.services.service_settings_service"/>
            <argument type="service" id="service_settings_module.facades.service_settings_facade"/>
            <tag name="kernel.event_subscriber" />
        </service>
        <service id="dispatcher.listeners.suspend_auto_renewal_listener" class="Dispatcher\Listeners\SuspendAutoRenewalListener">
            <argument type="service" id="service_settings_module.services.service_settings_service"/>
            <tag name="kernel.event_subscriber" />
        </service>
    </services>
</container>
