<?php

namespace FrontModule\Dto;

use BadMethodCallException;
use BootstrapModule\Singletons\StaticContainer;
use BusinessServicesModule\Entities\Offer;
use Entities\Company;
use Entities\Customer;
use FeatureModule\Feature;

class ServiceWidgetConditions
{
    /**
     * @var array
     */
    private $conditions;

    public function __construct(array $conditions) {
        $this->conditions = $conditions;
    }

    public static function fromArray(array $data): self
    {
        $array = [];
        foreach ($data as $key => $value) {
            $array[$key] = $value;
        }

        return new self($array);
    }

    public function isEmpty(): bool
    {
        return empty($this->conditions);
    }

    public function getConditions(): array
    {
        return $this->conditions;
    }


}