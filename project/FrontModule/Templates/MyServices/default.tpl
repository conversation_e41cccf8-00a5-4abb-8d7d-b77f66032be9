{include file="@header.tpl"}

<div class="row">
    {if $customerCompanies}
        {$form->begin nofilter}
        <div class="col-xs-12">
            <h1>My Services</h1>
        </div>
        <div class="col-xs-12">
            <span class="midfont grey2">Check the status of all your services and renew them when necessary.</span>
        </div>
        <div class="col-xs-12 text-right">
            {*default collapsing companies temporary disabled [QS2VQfq1] link visibility inverted*}
            <span class="toggle-company-hide">
                <a href="#" data-renewal-action="toggleAllCompanyServices">
                    <i class="fa fa-angle-up"></i>
                    Hide All
                </a>
            </span>
            <span class="toggle-company-show hidden">
                <a href="#" data-renewal-action="toggleAllCompanyServices">
                    <i class="fa fa-angle-down"></i>
                    Expand All
                </a>
            </span>
        </div>
        {if !$customerHasPaymentMethod}
            <div class="col-xs-12">
                <div class="flash info2 payment-method-flash" style="margin-left: 0 !important;">
                    Your renewal payments will fail as there is no payment method linked to your account.
                    <a href="{$this->router->link("FrontModule\controlers\ManagePaymentControler::MANAGE_PAYMENT_PAGE")}">Add new payment method</a>
                </div>
            </div>
        {/if}
        <div class="col-xs-12">
            <div id="sstatusgrid">
                {foreach $customerCompanies as $company}
                    {if $disabledRemindersCompanyId && $company->getId() == $disabledRemindersCompanyId}
                        <div class="alert alert-success alert-dismissible reminders-disabled-flash" role="alert">
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                            <h4>All payment reminders have been turned off for this company</h4>
                            <ul>
                                <li>You can turn reminders back on from each service's settings</li>
                                <li>Services set to auto-renew cannot have reminders turned off (auto-renew must be disabled first)</li>
                                <li>If you do decide to renew a service, we'll turn reminders back on for you (so you are notified for next year)</li>
                            </ul>
                        </div>
                    {/if}
                    {include 'MyServices/@company.tpl'}
                {/foreach}
            </div>
        </div>
        <div class="col-md-offset-9 col-md-3 col-xs-12" id="summary">
            <div class="col-xs-4">
                <div>Subtotal</div>
                <div>VAT</div>
                <div><strong>Total</strong></div>
            </div>
            <div class="col-xs-4">
                <div class="price-subtotal" data-renewal-selector="subtotal">£0.00</div>
                <div class="price-vat" data-renewal-selector="vat">£0.00</div>
                <div class="price-total" data-renewal-selector="total" style="font-weight: bold">£0.00</div>
            </div>
            <div class="col-md-12 col-sm-4 col-xs-12">
                {$form->getControl('submit') nofilter}
            </div>
        </div>
        {$form->end nofilter}
    {else}
        <div>
            <h1>My Services</h1>
            <span class="midfont grey2">No services.</span>
        </div>
    {/if}
</div>

<script type="text/javascript" src="{$urlJs}RenewalForm.js"></script>
<script type="text/javascript">
    $(document).ready(function() {
        CMS.renewalFormInit('form[name$="renew_services"]');
    });
</script>

{include file="@footer.tpl"}
