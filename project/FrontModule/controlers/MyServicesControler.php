<?php
namespace FrontModule\controlers;

use BasketModule\Services\BasketService;
use Config\Constants\DiLocator;
use Exception;
use Factories\Front\CompanyViewFactory;
use Factories\ServiceViewFactory;
use Libs\Basket;
use Models\Products\BasketProduct;
use Models\Products\Product;
use Models\View\CompanyView;
use MyServicesModule\Views\MyServicesView;
use OmnipayModule\Providers\OmnipayCardProvider;
use ServiceModule\Deciders\LatePaymentFeeDecider;
use ServiceModule\Products\LatePaymentFeeProductFactory;
use Services\CompanyService;
use FrontModule\forms\Services\Renew\IRenewFormDelegate;
use FrontModule\forms\Services\Renew\RenewForm;
use Services\ServiceService;
use ServiceSettingsModule\Config\DiLocator as ServiceSettingsDiLocator;
use ServiceSettingsModule\Services\ServiceSettingsService;
use CustomerModule\Factories\OptionalRenewalFactory;
use CustomerModule\Services\OptionalRenewalService;

class MyServicesControler extends LoggedControler implements IRenewFormDelegate
{
    const PAGE_SERVICES = 1364;
    const DAYS_INTERVAL_START = -28;
    const DAYS_INTERVAL_END = 50;

    /** @var array */
    public $possibleActions = [
        self::PAGE_SERVICES => 'default',
    ];

    /**
     * @var ServiceService
     */
    private $serviceService;

    /**
     * @var BasketService
     */
    private $basketService;

    /**
     * @var CompanyService
     */
    private $companyService;

    /**
     * @var ServiceSettingsService
     */
    private $serviceSettingsService;

    /**
     * @var CompanyViewFactory
     */
    private $companyViewFactory;

    /**
     * @var MyServicesView
     */
    private $myServicesView;

    /**
     * @var ServiceViewFactory
     */
    private $serviceViewFactory;

    /**
     * @var LatePaymentFeeDecider
     */
    private $latePaymentFeeDecider;

    /**
     * @var LatePaymentFeeProductFactory
     */
    private $latePaymentFeeProductFactory;

    /**
     * @var OptionalRenewalFactory
     */
    private $optionalRenewalFactory;

    /**
     * @var OptionalRenewalService
     */
    private $optionalRenewalService;

    /**
     * @var OmnipayCardProvider
     */
    private $omnipayCardProvider;

    public function startup()
    {
        parent::startup();

        $this->serviceService = $this->getService(DiLocator::SERVICE_SERVICE);
        $this->basketService = $this->getService('basket_module.services.basket_service');
        $this->companyService = $this->getService(DiLocator::SERVICE_COMPANY);
        $this->serviceSettingsService = $this->getService(ServiceSettingsDiLocator::SERVICES_SERVICE_SETTINGS_SERVICE);
        $this->companyViewFactory = $this->getService(DiLocator::FACTORY_FRONT_COMPANY_VIEW);
        $this->myServicesView = $this->getService('my_services_module.views.my_services_view');
        $this->serviceViewFactory = $this->getService('factories.front.service_view_factory');
        $this->latePaymentFeeDecider = $this->getService('service_module.deciders.late_payment_fee_decider');
        $this->latePaymentFeeProductFactory = $this->getService('service_module.products.late_payment_fee_product_factory');
        $this->optionalRenewalFactory = $this->getService('customer_module.factories.optional_renewal_factory');
        $this->optionalRenewalService = $this->getService('customer_module.services.optional_renewal_service');
        $this->omnipayCardProvider = $this->getService('omnipay_module.providers.omnipay_card_provider');
    }

    public function handleDefault()
    {
        if ($optionalRenewalData = $this->optionalRenewalFactory->getOptionalRenewalData($this->request->query)) {
            $this->optionalRenewalService->createOrUpdateOptionalRenewalSettings($this->customerEntity, $optionalRenewalData);
        }

        if ($this->isAjax() && isset($this->get['do'])) {
            if ($this->get['do'] === 'calculateTotal') {
                $uncheckServices = $uncheckServicesMessages = [];

                try {
                    $basket = new Basket('my-services');
                    $basket->clear(TRUE);

                    if (!empty($this->get['services'])) {
                        foreach ($this->get['services'] as $item) {

                            if (!isset($item['service_id'], $item['product_id'])) {
                                continue;
                            }

                            $product = Product::getProductById($item['product_id']);

                            $service = $this->serviceService->getCustomerService($this->customerEntity, $item['service_id']);
                            if (!$service) {
                                continue;
                            }

                            $company = $service->getCompany();
                            $services = $company->getServicesByType($service->getServiceTypeId());
                            $serviceView = $this->serviceViewFactory->create($services->toArray());

                            if ($this->latePaymentFeeDecider->isEligible($serviceView)) {
                                $fee = $this->latePaymentFeeProductFactory->create($company, $serviceView->getServiceType());
                                $product->setChild($fee);
                            }

                            try {
                                $basket->add($product);
                            } catch (Exception $e) {
                                if (strpos($e->getMessage(), 'so we have removed the extra') !== false) {
                                    $uncheckServices[] = $item['service_id'];
                                    $uncheckServicesMessages[] = str_replace('your basket', 'your selected services', $e->getMessage());
                                } else {
                                    throw $e;
                                }
                            }
                        }
                    }
                    $price = $basket->getPrice();
                    $payload = [
                        'error' => FALSE,
                        'price' => [
                            'subtotal' => $price->subTotal,
                            'vat' => $price->vat,
                            'total' => $price->total
                        ],
                        'uncheckServices' => $uncheckServices,
                        'uncheckServicesMessages' => $uncheckServicesMessages,
                    ];
                    $this->sendJSONResponse($payload);
                } catch (Exception $e) {
                    $this->handleException($e);
                }
            } elseif ($this->get['do'] === 'toggleServiceInBasket' && isset($this->get['id'])) {
                $service = $this->serviceService->getServiceById($this->get['id']);
                /** @var BasketProduct $product */
                $product = $service->getRenewalProduct();
                /** @var Basket $basket */
                $basket = $this->getService('basket');

                // product don't have companyId (even if retrieved with $service->getProduct())
                $product->setCompanyId($service->getCompany()->getCompanyId());
                $basketKey = $product->inBasketForCompany($basket);
                if ($basketKey !== FALSE) {
                    $basket->remove($basketKey);
                }

                $payload = [
                    'error' => FALSE
                ];
                $this->sendJSONResponse($payload);
            }
        }
    }

    public function renderDefault()
    {
        $checkedServices = [];
        if (isset($this->get['serviceId'])) {
            $service = $this->serviceService->getServiceById($this->get['serviceId']);
            if ($service) {
                $checkedServices[] = $service;
            }
        }
        if (isset($this->get['companyId']) && $company = $this->companyService->getCompanyById($this->get['companyId'])){
            if ($company->getCustomer() !== $this->customerEntity || !$company->hasServices()) {
                $this->redirect(DashboardCustomerControler::DASHBOARD_PAGE);
            }
            $companyView = $this->companyViewFactory->create($company);
            $customerCompanies = [$companyView];
            $checkedServices = $companyView->getServices();
        } else {
            $customerCompanies = $this->companyService->getCustomerServiceCompaniesViews($this->customerEntity);
            $checkedServices = $this->getServicesToCheck($customerCompanies, $checkedServices);
        }

        $checkedServices = $this->getServicesToCheck($customerCompanies, $checkedServices);

        $customerHasPaymentMethod = $this->omnipayCardProvider->hasPaymentMethods($this->customerEntity->getId());

        $this->template->customerCompanies = $customerCompanies;
        $this->template->customer = $this->customerEntity;
        $this->template->customerHasPaymentMethod = $customerHasPaymentMethod;
        $this->template->settingsService = $this->serviceSettingsService;
        $this->template->disabledRemindersCompanyId = $this->getParameter('disabledRemindersCompanyId');
        $this->template->view = $this->myServicesView;

        $form = new RenewForm($this->customerEntity->id . '_renew_services');
        $form->startup($customerCompanies, $checkedServices, $this->serviceSettingsService, $this->customerEntity);

        if ($form->isOk2Save()) {
            $data = $form->getValues();
            $form->clean();
            $this->renewFormWithServices(array_filter($data['services']));
        }

        $this->template->form = $form;
    }

    /*     * ****************************** IRenewFormDelegate ******************************** */

    /**
     * @param array $serviceIds
     */
    public function renewFormWithServices(array $serviceIds)
    {
        try {
            $this->basketService->clearBasket(false);
            $this->basketService->addRenewableServices($this->customerEntity, $serviceIds);
            if ($this->isAjax()) {
                $payload = ['error' => FALSE, 'redirect' => $this->newRouter->generate('basket_module_link_company')];
                $this->sendJSONResponse($payload);
            } else {
                $this->redirectRoute('basket_module_link_company');
            }
        } catch (Exception $e) {
            $this->handleException($e);
        }
    }

    /**
     * @param Exception $e
     */
    private function handleException(Exception $e)
    {
        if ($this->isAjax()) {
            $payload = ['error' => TRUE, 'message' => $e->getMessage()];
            $this->sendJSONResponse($payload);
        } else {
            $this->flashMessage($e->getMessage(), 'error');
            $this->redirect();
        }
    }

    private function getServicesToCheck(array $customerCompanies, array $checkedServices): array
    {
        /** @var CompanyView $company */
        foreach ($customerCompanies as $company) {
            foreach ($company->getServices() as $service) {

                if ($service->isCancelled()) {
                    continue;
                }

                $daysToExpire = $service->getDaysToExpire();
                if ($daysToExpire >= self::DAYS_INTERVAL_START && $daysToExpire <= self::DAYS_INTERVAL_END) {
                    $checkedServices[] = $service;
                }
            }
        }
        return $checkedServices;
    }
}
