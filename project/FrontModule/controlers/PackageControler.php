<?php
namespace FrontModule\controlers;

use AdminModule\Controlers\PackageAdminControler;
use Framework\FNode;
use Models\Products\Package;
use RouterModule\Helpers\ControllerHelper;

class PackageControler extends DefaultControler
{
    public function startup()
    {
        parent::startup();
    }

    public function renderDefault()
    {
        $company = SearchControler::getCompanyName();
        if ($company != FALSE) {
            $this->template->company = $company;
        }

        //test for page 1244 - clean this code after all test are done
        if (isset($this->get['no']) && $this->get['no'] == 1) {
            $this->template->noCompany = 1;
        } else {
            $this->template->noCompany = 0;
        }

        $this->template->package = new Package($this->nodeId);

        if (in_array($this->nodeId, [Package::PACKAGE_BASIC, Package::PACKAGE_PRIVACY, Package::PACKAGE_PRIVACY, Package::PACKAGE_PRIVACY])) {
            return $this->redirect(sprintf('/page%s.html', $this->nodeId));
        }

        $this->template->packageUrl = $this->controllerHelper->getUrl('basket_module_basket_upgrade_add_package');

        // other packages
        $ids = FNode::getChildsIds(PackageAdminControler::PACKAGES_FOLDER);
        $this->template->packages = FNode::getTitleAndLink($ids);
    }
}