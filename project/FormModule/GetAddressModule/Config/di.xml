<?xml version="1.0" ?>
<container xmlns="http://symfony.com/schema/dic/services"
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">
    <services>
        <defaults public="true" />
        <service class="HttpClient\Client" id="form_module.get_address_module.http_client">
            <factory service="http_client.factories.client_factory" method="create"/>
            <argument>%get_address.api.base_url%</argument>
        </service>

        <service class="FormModule\GetAddressModule\Clients\ApiClient" id="form_module.get_address_module.clients.api_client">
            <argument>%get_address.api.key%</argument>
            <argument type="service" id="form_module.get_address_module.http_client" />
        </service>

        <service class="FormModule\GetAddressModule\Controllers\AddressController" id="form_module.get_address_module.controllers.address_controller">
            <argument type="service" id="form_module.get_address_module.clients.api_client"/>
        </service>
    </services>
</container>
