<?php

namespace CompanyFormationModule\Entities;

use CompaniesHouseModule\Entities\NatureOfControl;

class PscCandidate
{
    /**
     * @var ShareholderPerson
     */
    private $shareholder;

    /**
     * @var NatureOfControl
     */
    private $natureOfControl;

    public function __construct(ShareholderPerson $shareholder, NatureOfControl $natureOfControl)
    {
        $this->shareholder = $shareholder;
        $this->natureOfControl = $natureOfControl;
    }

    public function getShareholder(): ShareholderPerson
    {
        return $this->shareholder;
    }

    public function getNatureOfControl(): NatureOfControl
    {
        return $this->natureOfControl;
    }

    public function isSamePerson(IMember $member): bool
    {
        return $member->getName() === $this->shareholder->getName();
    }
}
