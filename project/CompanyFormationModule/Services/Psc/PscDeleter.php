<?php

namespace CompanyFormationModule\Services\Psc;

use CompanyFormationModule\Entities\IPsc;
use CompanyFormationModule\Exceptions\CompanyNotMatchException;
use Doctrine\ORM\EntityManager;
use Entities\Company;

class PscDeleter
{
    /**
     * @var EntityManager
     */
    private $em;

    public function __construct(EntityManager $em) {
        $this->em = $em;
    }

    public function deletePsc(Company $company, IPsc $psc): void
    {
        if ($psc->getFormSubmission()->getCompany() !== $company) {
            throw new CompanyNotMatchException();
        }

        $this->em->remove($psc);
        $this->em->flush($psc);
    }
}
