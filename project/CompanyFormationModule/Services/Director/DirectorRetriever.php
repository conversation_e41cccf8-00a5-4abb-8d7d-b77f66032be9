<?php

namespace CompanyFormationModule\Services\Director;

use CompanyFormationModule\Entities\Member;
use Doctrine\ORM\EntityManager;
use Entities\Company;

class DirectorRetriever
{
    /**
     * @var EntityManager
     */
    private $em;

    public function __construct(EntityManager $em)
    {
        $this->em = $em;
    }

    public function getDirectors(Company $company)
    {
        return $this->em->getRepository(Member::class)->getDirectors($company);
    }
}
