<div class="row">
    {if isset($introductionStrip.flash)}
        <div class="col-12">
            <div class="alert {$introductionStrip.flash.type nofilter} alert-dismissible" role="alert">
                {if isset($introductionStrip.flash.dismissable) && ($introductionStrip.flash.dismissable)=="true"}
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span
                                aria-hidden="true">&times;</span></button>
                {/if}
                {$introductionStrip.flash.message nofilter}
            </div>
        </div>
    {/if}
    {if isset($context, $additionalFlash, $additionalFlash[$context])}
        <div class="col-12">
            <div class="alert {if arrayKeyExists('type', $additionalFlash[$context])}{$additionalFlash[$context]['type']}{else}alert-info{/if} alert-dismissible"
                 role="alert">
                {if arrayKeyExists('dismissable', $additionalFlash[$context]) && ($additionalFlash[$context]['dismissable'])=="true"}
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span
                                aria-hidden="true">&times;</span></button>
                {/if}
                {$additionalFlash[$context]['message'] nofilter}
            </div>
        </div>
    {/if}
    {block additionalFlash}{/block}
</div>
<div class="row">
    <div class="col-12">
        <div>
            <p class="font-36 mb-3 mt-4 fw-bold">
                {if !empty($memberTypeTitle)}
                    {$introductionStrip.title|replace:"Director":$memberTypeTitle}
                {else}
                    {$introductionStrip.title}
                {/if}
            </p>
        </div>
        {include file="FrontModule/Templates/@blocks/navlist.tpl" currentTab=$currentTab}
    </div>
</div>