<div class="bg-light px-3 pt-3 border-1">
    <div class="d-none d-xxl-block d-lg-block d-xl-block">
        <ul id="nav-tabs" class="nav nav-pills">
            <li class="nav-item" id="search" role="presentation">
                <a class="nav-link link-dark" href='{url route="company_formation_module.add_sic_search" company=$company->getId()}'>
                    Search by keyword
                </a>
            </li>
            <li class="nav-item" id="browse" role="presentation">
                <a class="nav-link link-dark" href='{url route="company_formation_module.add_sic_browse" company=$company->getId()}'>
                    Browse by category
                </a>
            </li>
        </ul>
    </div>
    <div class="d-block d-xxl-none d-lg-none d-xl-none d-flex flex-column" id="nav-mob">
        <p class="p-3 bg-white" id="search" role="presentation">
            <a class="link-dark" href='{url route="company_formation_module.add_sic_search" company=$company->getId()}'>
                Search by keyword
            </a>
        </p>
        <p class="p-3 bg-white" id="browse" role="presentation">
            <a class="link-dark" href='{url route="company_formation_module.add_sic_browse" company=$company->getId()}'>
                Browse by category
            </a>
        </p>
    </div>
</div>

<script language="JavaScript">
    $(document).ready(function() {
        $('#nav-tabs {$sicTab}').addClass('active');
        $('#nav-mob {$sicTab}').addClass(' active-option border rounded-2 fw-semibold');
    });
</script>

<style>
.active {
    background-color: white !important;
    border-top-right-radius: 0.25rem !important;
    border-top-left-radius: 0.25rem !important;
    border-bottom: 0 !important;
}
.active-option a {
    color: #001f4e!important;
}
</style>