<?php

namespace CompanyFormationModule\Listeners;

use CompaniesHouseModule\Services\SubmissionHandler;
use CompaniesHouseModule\Services\SubmissionReviewService;
use Config\Constants\EventLocator;
use Dispatcher\Events\CompanyDeletedEvent;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

class CompanyRefundedDeletedListener implements EventSubscriberInterface
{
    /**
     * @var SubmissionHandler
     */
    private $submissionHandler;

    /**
     * @var SubmissionReviewService
     */
    private $submissionReviewService;

    public function __construct(SubmissionHandler $submissionHandler, SubmissionReviewService $submissionReviewService)
    {
        $this->submissionHandler = $submissionHandler;
        $this->submissionReviewService = $submissionReviewService;
    }

    /**
     * {@inheritDoc}
     */
    public static function getSubscribedEvents()
    {
        return [
            EventLocator::COMPANY_DELETED => 'onDeleted',
        ];
    }

    public function onDeleted(CompanyDeletedEvent $event)
    {
        $this->submissionHandler->removeSubmissionsFromQueueForCompany($event->getCompany());
        $this->submissionHandler->changePendingSubmissionsToDeleted($event->getCompany());
        $this->submissionReviewService->removeForCompany($event->getCompany());
    }
}