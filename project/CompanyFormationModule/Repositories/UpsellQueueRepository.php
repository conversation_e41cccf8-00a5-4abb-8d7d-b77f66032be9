<?php

namespace CompanyFormationModule\Repositories;

use CompanyFormationModule\Entities\UpsellQueueItem;
use Entities\Company;
use Repositories\BaseRepository_Abstract;

class UpsellQueueRepository extends BaseRepository_Abstract
{
    /**
     * @return UpsellQueueItem[]
     */
    public function getItems()
    {
        return $this->createQueryBuilder('i')
            ->join('i.company', 'co')
            ->getQuery()->getResult();
    }

    /**
     * @param Company $company
     * @return UpsellQueueItem[]
     */
    public function getItemsByCompany(Company $company)
    {
        return $this->createQueryBuilder('i')
            ->where('i.company = :company')
            ->setParameter('company', $company)
            ->getQuery()
            ->getResult();
    }

    /**
     * @param Company $company
     * @param mixed $type
     * @return bool
     */
    public function hasExistingUpsell(Company $company, $type)
    {
        return $this->createQueryBuilder('i')
            ->where('i.company = :company')
            ->andWhere('i.productId = :productId')
            ->setParameter('company', $company)
            ->setParameter('productId', $type)
            ->getQuery()
            ->getOneOrNullResult();
    }
}
