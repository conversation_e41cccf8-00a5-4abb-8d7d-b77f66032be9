<?php

namespace CompanyFormationModule\Forms;

use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class SubmitIncorporationType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
    }

    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults(
            [
                'attr' => ['novalidate' => 'novalidate']
            ]
        );
    }

    /**
     * @return string
     */
    public function getName()
    {
        return 'submit_incorporation_type';
    }
}