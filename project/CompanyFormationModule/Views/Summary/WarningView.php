<?php

namespace CompanyFormationModule\Views\Summary;

use Entities\CompanyHouse\Helper\Address;
use JsonSerializable;
use Models\Products\ServiceAddress;

class WarningView implements \JsonSerializable
{
    public function __construct(
        private bool $isSubmissionPending,
        private bool $waitingForIdCheck,
        private bool $waitingForReview,
        private bool $hasDefaultCompanyName,
        private array $postcodeConflicts,
        private ServiceAddress $ourServiceAddress,
        private array $reservedWords = [],
        private array $previousCompanyIncorporation = [],
    ) {}

    public function isSubmissionPending(): bool
    {
        return $this->isSubmissionPending;
    }

    public function isWaitingForIdCheck(): bool
    {
        return $this->waitingForIdCheck;
    }

    public function isWaitingForReview(): bool
    {
        return $this->waitingForReview;
    }

    public function hasDefaultCompanyName(): bool
    {
        return $this->hasDefaultCompanyName;
    }

    public function hasPostcodeConflicts(): bool
    {
        return !empty($this->getPostcodeConflicts());
    }

    public function getPostcodeConflicts(): array
    {
        return $this->postcodeConflicts;
    }
    public function getOurServiceAddress(): ServiceAddress
    {
        return $this->ourServiceAddress;
    }

    public function hasReservedWords(): bool
    {
        return !empty($this->reservedWords);
    }

    public function getReservedWords(): array
    {
        return $this->reservedWords;
    }

    public function jsonSerialize(): array
    {
        return [
            'isSubmissionPending' => $this->isSubmissionPending(),
            'isWaitingForIdCheck' => $this->isWaitingForIdCheck(),
            'isWaitingForReview' => $this->isWaitingForReview(),
            'hasDefaultCompanyName' => $this->hasDefaultCompanyName(),
            'hasPostcodeConflicts' => $this->hasPostcodeConflicts(),
            'postCodeConflicts' => $this->getPostcodeConflicts(),
            'ourServiceAddress' => [
                'premise' => $this->ourServiceAddress->getPremise(),
                'street' => $this->ourServiceAddress->getStreet(),
                'thoroughfare' => $this->ourServiceAddress->getThoroughfare(),
                'postcode' => $this->ourServiceAddress->getPostcode(),
                'post_town' => $this->ourServiceAddress->getPostTown(),
                'county' => $this->ourServiceAddress->getCounty(),
                'country' => $this->ourServiceAddress->getCountry(),
            ],
            'reservedWords' => $this->reservedWords,
            'previousCompanyIncorporation' => $this->previousCompanyIncorporation,
        ];
    }
}