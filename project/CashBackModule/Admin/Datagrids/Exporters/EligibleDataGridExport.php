<?php

namespace CashBackModule\Admin\Datagrids\Exporters;

use DataGrid\src\DataGrid\DataGridExport;
use Legacy\Nette\Web\Html;

class EligibleDataGridExport extends DataGridExport
{
    /**
     * @var string
     */
    private $paymentKitDownloadLink;

    public function getPaymentKitLink(): Html
    {
        return Html::el('a')
            ->href($this->paymentKitDownloadLink)
            ->setText('Download bank cashback payment kit:');
    }

    public function setPaymentKitDownloadLink(string $paymentKitDownloadLink): void
    {
        $this->paymentKitDownloadLink = $paymentKitDownloadLink;
    }
}
