<?php

namespace CashBackModule\Commands;

use Entities\Customer;
use Entities\CustomerLog;
use Exception;
use Repositories\CustomerLogRepository;
use Repositories\CustomerRepository;
use Psr\Log\LoggerInterface;
use DateTime;

/**
 * @description Create CASHBACK_PREF_CREATED log action id normalizing some cash back existing cases
 */
class FixExistingCustomersCashBackCommand
{

    /**
     * @var CustomerRepository
     */
    private $customerRepository;

    /**
     * @var CustomerLogRepository
     */
    private $customerLogRepository;

    /**
     * @var LoggerInterface
     */
    private $logger;

    public function __construct(
        CustomerRepository $customerRepository,
        CustomerLogRepository $customerLogRepository,
        LoggerInterface $logger
    )
    {
        $this->customerRepository = $customerRepository;
        $this->customerLogRepository = $customerLogRepository;
        $this->logger = $logger;
    }

    /**
     * @throws Exception
     */
    public function applyFix(bool $dryRun = FALSE)
    {
        $customers = $this->customerRepository->getCustomersWithCashBackWithoutCashBackLog();

        $processed = 0;

        foreach ($customers as $customer) {

            $customerLog = self::createCustomerLog($customer);

            if (!$dryRun) {
                $this->customerLogRepository->saveEntity($customerLog);
            }

            $this->log($customerLog);

            $processed++;

            if ($processed % 100 === 0) {
                $this->customerRepository->releaseEntities();
            }

        }
    }

    /**
     * @throws Exception
     */
    private static function createCustomerLog(Customer $customer): CustomerLog
    {
        $log = new CustomerLog();
        $log->setCustomer($customer);
        $log->setActionId(CustomerLog::CASHBACK_PREF_CREATED);
        $log->setDtc(new DateTime());
        return $log;
    }

    private function log(CustomerLog $log)
    {
        $customer = $log->getCustomer();

        $context = [
            'log.actionId' => $log->getActionId(),
            'log.dtc' => $log->getDtc()->format('Y-m-d'),
            'customer.roleId' => $customer->getRoleId(),
            'customer.cashBackType' => $customer->getCashbackType(),
            'customer.sortCode' => $customer->getSortCode(),
            'customer.accountNumber' => $customer->getAccountNumber(),
        ];

        $this->logger->debug('Creating customer log', $context);
    }

}