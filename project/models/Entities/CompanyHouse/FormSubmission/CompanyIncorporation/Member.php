<?php

namespace Entities\CompanyHouse\FormSubmission\CompanyIncorporation;

use Doctrine\ORM\Mapping as Orm;
use Entities\CompanyHouse\Helper\Identification;
use Entities\EntityAbstract;
use Entities\CompanyHouse\Helper\BaseOfficer;
use Entities\CompanyHouse\Helper\Address;
use Entities\CompanyHouse\Helper\UkIdentification;
use Entities\CompanyHouse\Helper\NonUkIdentification;
use Entities\CompanyHouse\Helper\PersonOfficer;
use Entities\CompanyHouse\Helper\CorporateOfficer;

/**
 * @deprecated use CompanyFormationModule\Entities\Member (refactor needed https://gitlab.com/madesimplegroup/cms/issues/26)
 * @Orm\Entity
 * @Orm\Table(name="ch_incorporation_member")
 * @Orm\InheritanceType("SINGLE_TABLE")
 * @Orm\DiscriminatorColumn(name="type", type="string")
 * @Orm\DiscriminatorMap({
 *      "DIR" = "Entities\CompanyHouse\FormSubmission\CompanyIncorporation\Appointment\Director",
 *      "SEC" = "Entities\CompanyHouse\FormSubmission\CompanyIncorporation\Appointment\Secretary",
 *      "SUB" = "Entities\CompanyHouse\FormSubmission\CompanyIncorporation\Subscriber",
 *      "MEM" = "Entities\CompanyHouse\FormSubmission\CompanyIncorporation\Appointment\LLPMember",
 *      "PSC" = "Entities\CompanyHouse\FormSubmission\CompanyIncorporation\Psc"
 * })
 */
abstract class Member extends EntityAbstract
{

    const TYPE_DIRECTOR = 'DIR';
    const TYPE_SECRETARY = 'SEC';
    const TYPE_SUBSCRIBER = 'SUB';
    const TYPE_LLP_MEMBER = 'MEM';
    const TYPE_PSC = 'PSC';
    const TYPE_NAME = 'member';

    /**
     * @var string
     * @Orm\Column(name="title", type="string", length=50, nullable=true)
     */
    protected $title;

    /**
     * @var string
     * @Orm\Column(name="forename", type="string", length=50, nullable=true)
     */
    protected $forename;

    /**
     * @var string
     * @Orm\Column(name="middle_name", type="string", length=50, nullable=true)
     */
    protected $middleName;

    /**
     * @var string
     * @Orm\Column(name="surname", type="string", length=160, nullable=false)
     */
    protected $surname;

    /**
     * @var string
     * @Orm\Column(name="dob", type="string", length=160, nullable=false)
     */
    protected $dob;

    /**
     * @var string
     * @Orm\Column(name="nationality", type="string", length=160, nullable=false)
     */
    private $nationality;

    /**
     * @var string
     * @Orm\Column(name="corporate_name", type="string", length=160, nullable=true)
     */
    private $corporateName;

    /**
     * @var string
     * @Orm\Column(name="premise", type="string", length=50, nullable=false)
     */
    protected $premise;

    /**
     * @var string
     * @Orm\Column(name="street", type="string", length=50, nullable=false)
     */
    protected $street;

    /**
     * @var string
     * @Orm\Column(name="thoroughfare", type="string", length=50, nullable=true)
     */
    protected $thoroughfare;

    /**
     * @var string
     * @Orm\Column(name="post_town", type="string", length=50, nullable=true)
     */
    protected $postTown;

    /**
     * @var string
     * @Orm\Column(name="county", type="string", length=50, nullable=true)
     */
    protected $county;

    /**
     * @var string
     * @Orm\Column(name="country", type="string", length=50, nullable=true)
     */
    protected $country;

    /**
     * @var string
     * @Orm\Column(name="postcode", type="string", length=15, nullable=true)
     */
    protected $postcode;

    /**
     * @var string
     * @Orm\Column(name="country_of_residence", type="string", length=160, nullable=false)
     */
    private $countryOfResidence;

    /**
     * @var string
     * @Orm\Column(name="residential_premise", type="string", length=50, nullable=false)
     */
    protected $residential_premise;

    /**
     * @var string
     * @Orm\Column(name="residential_street", type="string", length=50, nullable=false)
     */
    protected $residential_street;

    /**
     * @var string
     * @Orm\Column(name="residential_thoroughfare", type="string", length=50, nullable=true)
     */
    protected $residential_thoroughfare;

    /**
     * @var string
     * @Orm\Column(name="residential_post_town", type="string", length=50, nullable=true)
     */
    protected $residential_postTown;

    /**
     * @var string
     * @Orm\Column(name="residential_county", type="string", length=50, nullable=true)
     */
    protected $residential_county;

    /**
     * @var string
     * @Orm\Column(name="residential_country", type="string", length=50, nullable=true)
     */
    protected $residential_country;

    /**
     * @var string
     * @Orm\Column(name="residential_postcode", type="string", length=15, nullable=true)
     */
    protected $residential_postcode;

    /**
     * @var string
     * @Orm\Column(name="residential_secure_address_ind", type="boolean", nullable=true)
     */
    protected $residential_secure_address_ind;

    /**
     * @var string
     * @Orm\Column(name="care_of_name", type="string", length=100, nullable=true)
     */
    protected $careOfName;

    /**
     * @var string
     * @Orm\Column(name="po_box", type="string", length=10, nullable=true)
     */
    protected $poBox;

    /**
     * @var BaseOfficer
     * (NOTE: no ORM annotation here)
     */
    protected $officer;

    /**
     * @var integer
     * @Orm\Column(name="incorporation_member_id", type="integer", nullable=false)
     * @Orm\Id
     * @Orm\GeneratedValue(strategy="IDENTITY")
     */
    private $memberId;

    /**
     * @var boolean
     * @Orm\Column(name="nominee", type="boolean", nullable=true)
     */
    private $nominee;

    /**
     * @var string
     */
    private $type;

    /**
     * @var boolean
     * @Orm\Column(name="corporate", type="boolean", nullable=false)
     */
    private $corporate = FALSE;

    /**
     * @var bool
     * @Orm\Column(name="consentToAct", type="boolean")
     */
    private $consentToAct = TRUE;

    /**
     * @var string
     * @Orm\Column(name="identification_type", type="string", nullable=true)
     */
    private $identificationType;

    /**
     * @var string
     * @Orm\Column(name="place_registered", type="string", length=50, nullable=true)
     */
    private $placeRegistered;

    /**
     * @var string
     * @Orm\Column(name="registration_number", type="string", length=20, nullable=true)
     */
    private $registrationNumber;

    /**
     * @var string
     * @Orm\Column(name="law_governed", type="string", length=50, nullable=true)
     */
    private $lawGoverned;

    /**
     * @var string
     * @Orm\Column(name="legal_form", type="string", length=50, nullable=true)
     */
    private $legalForm;


    /**
     * @var string
     * @Orm\Column(name="country_or_state", type="string", nullable=true)
     */
    private $countryOrState;

    /**
     * @var string
     * @Orm\Column(name="discriminator", type="string", nullable=true)
     */
    private $discriminator;

    /**
     * @param BaseOfficer $officer
     */
    public function __construct(BaseOfficer $officer)
    {
        $this->setOfficer($officer);
        $this->extractOfficer();
        $this->extractIdentification();
    }

    /**
     * @return BaseOfficer
     */
    public function getOfficer()
    {
        return $this->officer;
    }

    /**
     * @param BaseOfficer $officer
     */
    public function setOfficer(BaseOfficer $officer)
    {
        $this->officer = $officer;
    }

    /**
     * @return bool
     */
    public function hasConsentToAct()
    {
        return $this->consentToAct;
    }

    /**
     * @param bool $consentToAct
     */
    public function setConsentToAct($consentToAct)
    {
        $this->consentToAct = $consentToAct;
    }

    /**
     * @return integer
     */
    public function getMemberId()
    {
        return $this->memberId;
    }

    /**
     * @return boolean
     */
    public function getNominee()
    {
        return $this->nominee;
    }

    /**
     * @param boolean $nominee
     */
    public function setNominee($nominee)
    {
        $this->nominee = $nominee;
    }

    /**
     * @return string
     */
    public function getTitle()
    {
        return $this->title;
    }

    /**
     * @return string
     */
    public function getForename()
    {
        return $this->forename;
    }

    /**
     * @return string
     */
    public function getMiddleName()
    {
        return $this->middleName;
    }

    /**
     * @return string
     */
    public function getSurname()
    {
        return $this->surname;
    }

    /**
     * @return string
     */
    public function getDob()
    {
        return $this->dob;
    }

    /**
     * @return string
     */
    public function getNationality()
    {
        return $this->nationality;
    }

    /**
     * @return string
     */
    public function getCorporateName()
    {
        return $this->corporateName;
    }

    /**
     * @return string
     */
    public function getPremise()
    {
        return $this->premise;
    }

    /**
     * @return string
     */
    public function getStreet()
    {
        return $this->street;
    }

    /**
     * @return string
     */
    public function getThoroughfare()
    {
        return $this->thoroughfare;
    }

    /**
     * @return string
     */
    public function getPostTown()
    {
        return $this->postTown;
    }

    /**
     * @return string
     */
    public function getCounty()
    {
        return $this->county;
    }

    /**
     * @return string
     */
    public function getCountry()
    {
        return $this->country;
    }

    /**
     * @return string
     */
    public function getPostcode()
    {
        return $this->postcode;
    }

    /**
     * @return string
     */
    public function getCountryOfResidence()
    {
        return $this->countryOfResidence;
    }

    /**
     * @return string
     */
    public function getResidentialPremise()
    {
        return $this->residential_premise;
    }

    /**
     * @return string
     */
    public function getResidentialStreet()
    {
        return $this->residential_street;
    }

    /**
     * @return string
     */
    public function getResidentialThoroughfare()
    {
        return $this->residential_thoroughfare;
    }

    /**
     * @return string
     */
    public function getResidentialPostTown()
    {
        return $this->residential_postTown;
    }

    /**
     * @return string
     */
    public function getResidentialCounty()
    {
        return $this->residential_county;
    }

    /**
     * @return string
     */
    public function getResidentialCountry()
    {
        return $this->residential_country;
    }

    /**
     * @return string
     */
    public function getResidentialPostcode()
    {
        return $this->residential_postcode;
    }

    /**
     * @return string
     */
    public function getResidentialSecureAddressInd()
    {
        return $this->residential_secure_address_ind;
    }

    /**
     * @return string
     */
    public function getPlaceRegistered()
    {
        return $this->placeRegistered;
    }

    /**
     * @return string
     */
    public function getRegistrationNumber()
    {
        return $this->registrationNumber;
    }

    /**
     * @return string
     */
    public function getLawGoverned()
    {
        return $this->lawGoverned;
    }

    /**
     * @return string
     */
    public function getLegalForm()
    {
        return $this->legalForm;
    }

    /**
     * @return string
     */
    public function getCountryOrState()
    {
        return $this->countryOrState;
    }

    /**
     * @return string
     */
    public function getCareOfName()
    {
        return $this->careOfName;
    }

    /**
     * @return string
     */
    public function getPoBox()
    {
        return $this->poBox;
    }

    /**
     * @return string
     */
    public function getType()
    {
        return $this->type;
    }

    /**
     * @return bool
     */
    public function getCorporate()
    {
        return $this->corporate;
    }

    /**
     * @param bool $corporate
     */
    public function setCorporate($corporate)
    {
        $this->corporate = $corporate;
    }

    /**
     * @param string $residential_premise
     */
    public function setResidentialPremise($residential_premise)
    {
        $this->residential_premise = $residential_premise;
    }

    /**
     * @param string $residential_street
     */
    public function setResidentialStreet($residential_street)
    {
        $this->residential_street = $residential_street;
    }

    /**
     * @param string $residential_thoroughfare
     */
    public function setResidentialThoroughfare($residential_thoroughfare)
    {
        $this->residential_thoroughfare = $residential_thoroughfare;
    }

    /**
     * @param string $residential_postTown
     */
    public function setResidentialPostTown($residential_postTown)
    {
        $this->residential_postTown = $residential_postTown;
    }

    /**
     * @param string $residential_county
     */
    public function setResidentialCounty($residential_county)
    {
        $this->residential_county = $residential_county;
    }

    /**
     * @param string $residential_country
     */
    public function setResidentialCountry($residential_country)
    {
        $this->residential_country = $residential_country;
    }

    /**
     * @param string $residential_postcode
     */
    public function setResidentialPostcode($residential_postcode)
    {
        $this->residential_postcode = $residential_postcode;
    }

    /**
     * @param string $residential_secure_address_ind
     */
    public function setResidentialSecureAddressInd($residential_secure_address_ind)
    {
        $this->residential_secure_address_ind = $residential_secure_address_ind;
    }

    /**
     * @Orm\PreFlush
     * set all properties when this object is to
     * save so that doctrine can easily save these scalar values
     */
    public function extractOfficer()
    {
        $this->forename = $this->officer->getForeName();
        $this->surname = $this->officer->getSurname();
        $this->middleName = $this->officer->getOtherForenames();
        $this->title = $this->officer->getTitle();

        if ($this->officer instanceof CorporateOfficer) {
            $this->corporateName = $this->officer->getCorporateName();
            $this->corporate = TRUE;
        }
        $this->extractAddress($this->officer->getAddress());
    }

    /**
     * @Orm\PreFlush
     * set all properties when this object is to
     * save so that doctrine can easily save these scalar values
     */
    public function extractIdentification()
    {
        if ($this->officer instanceof CorporateOfficer && $this instanceof Appointment) {
            $identification = $this->officer->getIdentification();
            $this->registrationNumber = $identification->getRegistrationNumber();
            if ($identification instanceof NonUkIdentification) {
                $this->placeRegistered = $identification->getPlaceRegistered();
                $this->lawGoverned = $identification->getLawGoverned();
                $this->legalForm = $identification->getLegalForm();
            }
        }
    }

    /**
     * @param Address $address
     */
    public function extractAddress($address)
    {
        $this->premise = $address->getPremise();
        $this->street = $address->getStreet();
        $this->postTown = $address->getPostTown();
        $this->postcode = $address->getPostcode();
        $this->thoroughfare = $address->getThoroughfare();
        $this->careOfName = $address->getCareOfName();
        $this->county = $address->getCounty();
        $this->country = $address->getCountry();
        $this->poBox = $address->getPoBox();
    }

    /**
     * @Orm\PostLoad
     * When the row is hydrated into this class,
     * $officer is not set because that isn't mapped.
     * so simply, map it manually
     */
    public function hydrateOfficer()
    {
        $address = $this->getHydrateAddress();
        if ($this->corporate) {
            $this->officer = new CorporateOfficer($this->forename, $this->surname, $this->corporateName, $address);
        } else {
            $this->officer = new PersonOfficer($this->forename, $this->surname, $address);
            $this->officer->setTitle($this->title);
            $this->officer->setOtherForenames($this->middleName);
        }
    }

    /**
     * @Orm\PostLoad
     * When the row is hydrated into this class,
     * Identification is not set because that isn't mapped.
     * so simply, map it manually
     */
    public function hydrateIdentification()
    {
        if ($this->corporate) {
            if ($this->identificationType == Identification::IDENTIFICATION_TYPE_UK) {
                $this->officer->setIdentification(new UkIdentification($this->registrationNumber));
            } else {
                $this->officer->setIdentification(
                    new NonUkIdentification(
                        $this->placeRegistered,
                        $this->registrationNumber,
                        $this->lawGoverned,
                        $this->legalForm
                    )
                );
            }
        }
    }

    /**
     * @return Address
     */
    public function getHydrateAddress()
    {
        $address = new Address(
            $this->premise,
            $this->street,
            $this->postTown,
            $this->postcode,
            $this->country
        );
        $address->setThoroughfare($this->thoroughfare);
        $address->setCareOfName($this->careOfName);
        $address->setCounty($this->county);
        $address->setPoBox($this->poBox);

        return $address;
    }

    /**
     * @return bool
     */
    public function isCorporate()
    {
        return $this->getCorporate();
    }

    public function getFields()
    {
        $fields = array();
        foreach ($this->fields as $key => $value) {
            $fields[$key] = $value;
        }
        return $fields;
    }

    /**
     * @return string
     */
    public function getFullName()
    {
        return $this->getForename() . ' ' . $this->getSurname();
    }

    public function getTypeName(): string
    {
        return static::TYPE_NAME;
    }

}
