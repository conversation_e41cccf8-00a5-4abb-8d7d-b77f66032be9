<?php

namespace Entities;

class CapitalShare extends Share implements EntityInterface
{
    /**
     * @var string
     */
    protected $id;

    /**
     * @var string
     */
    protected $prescribedParticulars;

    /**
     * @var int
     */
    protected $aggregateNominalValue;

    /**
     * @var Capital
     */
    protected $capital;

    /**
     * @var int
     */
    protected $totalAmountUnpaid;

    public function __construct(string $shareClass, string $prescrPartic, float $numShares, float $aggregateNominalValue)
    {
        $this->setShareClass($shareClass);
        $this->setPrescribedParticulars($prescrPartic);
        $this->setNumShares($numShares);
        $this->setAggregateNominalValue($aggregateNominalValue);
    }
    
    public function getPrescribedParticulars(): string 
    {
        return $this->prescribedParticulars;
    }

    public function setPrescribedParticulars($prescribedParticulars): self
    {
        $this->prescribedParticulars = $prescribedParticulars;
        return $this;
    }

    public function getAggregateNominalValue(): float
    {
        return $this->aggregateNominalValue;
    }

    public function setAggregateNominalValue(float $aggregateNominalValue): self
    {
        $this->aggregateNominalValue = $aggregateNominalValue;
        return $this;
    }
    
    public function getCapital(): Capital
    {
        return $this->capital;
    }
    
    public function setCapital(Capital $capital): self
    {
        $this->capital = $capital;
        return $this;
    }
    
    public function getTotalAmountUnpaid(): ?float
    {
        return $this->totalAmountUnpaid;
    }
    
    public function setTotalAmountUnpaid(?float $totalAmountUnpaid): self
    {
        $this->totalAmountUnpaid = $totalAmountUnpaid;
        return $this;
    }
    
    public function getId()
    {
        return $this->id;
    }
    
    public function setId($id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getShareValue(): float
    {
        $calculated = (double) ($this->getAggregateNominalValue() / $this->getNumShares());
        return $calculated ?? (double) 1;
    }
}
