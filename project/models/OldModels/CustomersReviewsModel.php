<?php

namespace Models\OldModels;

use Libs\CustomerReviews\CustomerReviewsSatisfaction;
use Framework\FApplication;
use Framework\FNode;
use Framework\Paginator\FPaginator2;
use Libs\CustomerReviews\CustomerReview;

class CustomersReviewsModel extends FNode
{
	const REVIEWS_PAGINATOR_ITEMS_PER_PAGE = 25;

	/**
	 * Returns paginator for reviews
	 *
	 * @return FPaginator2
	 */
	public function getReviewsPaginator()
	{
		$paginator = new FPaginator2(CustomerReview::getCustomerAllowedAndApproveReviewsCount());
		$paginator->itemsPerPage = self::REVIEWS_PAGINATOR_ITEMS_PER_PAGE;
		$paginator->htmlDisplayEnabled = FALSE;
		return $paginator;
	}

	/**
	 * Returns list of reviews
	 *
	 * @param FPaginator2 $paginator
	 * @param array<CustomerReview>
	 */
	public function getReviews(FPaginator2 $paginator)
	{
		$reviews = CustomerReview::getAllowedAndApprovedCustomerReviews(array(), $paginator->getLimit(), $paginator->getOffset());
		return $reviews;
	}

	/**
	 * Provides replace text
	 *
	 * @return string
	 */
	public function getReplacesReviewsText()
	{
		$text = $this->getLngText();
		$text = FApplication::modifyCmsText($text);
		return $text;
	}

	/**
	 * @return int
	 */
	public function getSatisfactionFinalPercentage()
	{
		$customerSatiscation = new CustomerReviewsSatisfaction();
		return $customerSatiscation->getFinalPercentageValue();
	}
}