<?php

declare(strict_types=1);

namespace CompanyIncorporationModule\UseCase\BusinessServices\Api\ProcessAdverts;

use Entities\Company;
use Utils\Helpers\ArrayHelper;

class Factory
{
    public function makeRequest(Company $company, array $payload): Request
    {
        return new Request(
            $company,
            $payload['advertId'],
            ArrayHelper::get($payload, 'referral', null)
        );
    }

    public function makeResponse(): Response
    {
        return new Response();
    }
}
