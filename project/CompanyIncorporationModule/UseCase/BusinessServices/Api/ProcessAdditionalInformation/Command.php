<?php

declare(strict_types=1);

namespace CompanyIncorporationModule\UseCase\BusinessServices\Api\ProcessAdditionalInformation;

use BusinessServicesModule\Entities\Offer;
use BusinessServicesModule\Facades\AdditionalInformationFacade;
use BusinessServicesModule\Repositories\LeadRepository;
use BusinessServicesModule\RevolutModule\Mailers\RevolutMailer;
use Config\Constants\EventLocator;
use Doctrine\ORM\OptimisticLockException;
use Psr\Log\LoggerInterface;
use Services\Dispatcher\Events\CompanyEvent;
use Services\EventService;
use Symfony\Component\EventDispatcher\EventDispatcher;
use Utils\Helpers\ArrayHelper;

class Command
{
    public const EVENT_NAME = 'company_formation.additional_information_processed';
    public const REVOLUT_BANKING_IDS = [Offer::REVOLUT_OFFER_ID, Offer::REVOLUT_INTERNATIONAL_OFFER_ID];

    public function __construct(
        private Factory $factory,
        private AdditionalInformationFacade $additionalInformationFacade,
        private EventDispatcher $eventDispatcher,
        private EventService $eventService,
        private LeadRepository $offerLeadRepository,
        private RevolutMailer $revolutMailer,
        private LoggerInterface $logger,
    ) {
    }

    /**
     * @throws OptimisticLockException
     */
    public function execute(Request $request): Response
    {
        $this->eventService->notifyPreventDuplicationCached(self::EVENT_NAME, $request->company->getId());

        $this->additionalInformationFacade->saveData(
            $this->offerLeadRepository->findNewByCompany($request->company),
            $request->additionalInformationData
        );

        if ($request->company->isIncorporated()) {
            $this->eventDispatcher->dispatch(
                new CompanyEvent($request->company),
                EventLocator::BANK_APPLIED_AFTER_INCORPORATION
            );
        }

        $this->sendRevolutCustomerEmail($request);

        return $this->factory->makeResponse();
    }

    private function sendRevolutCustomerEmail(Request $request): void
    {
        try {
            if (
                empty(array_filter($request->leads, [$this, 'filterRevolutLead']))
                || $this->eventService->eventOccurred(RevolutMailer::EVENT_REVOLUT_CUSTOMER_EMAIL_ALREDY_SENT, $request->company->getId())
            ) {
                return;
            }

            $this->revolutMailer->sendEmailToCustomer($request->company->getCustomer(), $request->additionalInformationData);
            $this->eventService->notifyPreventDuplication(RevolutMailer::EVENT_REVOLUT_CUSTOMER_EMAIL_ALREDY_SENT, $request->company->getId());
        } catch (\Throwable $e) {
            $this->logger->error(
                'Error sending Revolut email to customer',
                ['e' => $e]
            );
        }
    }

    private function filterRevolutLead($lead): bool
    {
        $specificLead = ArrayHelper::get($lead, ['offer', 'id'], null);

        return isset($specificLead) && in_array((int) $specificLead, self::REVOLUT_BANKING_IDS);
    }
}
