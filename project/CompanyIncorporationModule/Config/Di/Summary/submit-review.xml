<?xml version="1.0" ?>
<container xmlns="http://symfony.com/schema/dic/services"
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd"
>
    <services>
        <defaults public="true" />

        <service class="CompanyIncorporationModule\UseCase\Summary\Api\SubmitReview\Command"
                 id="company_incorporation_module.use_case.summary.submit_review.command">
            <argument id="company_incorporation_module.use_case.summary.submit_review.factory" type="service"/>
            <argument id="companies_house_module.services.submission_review_service" type="service"/>
            <argument id="company_incorporation_module.services.summary_service" type="service" />
            <argument id="money_penny_numbers_module.providers.money_penny_provider" type="service" />
        </service>

        <service class="CompanyIncorporationModule\UseCase\Summary\Api\SubmitReview\Factory"
                 id="company_incorporation_module.use_case.summary.submit_review.factory">
        </service>
    </services>
</container>