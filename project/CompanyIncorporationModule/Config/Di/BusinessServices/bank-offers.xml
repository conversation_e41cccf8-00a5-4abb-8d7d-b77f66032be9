<?xml version="1.0" ?>

<container xmlns="http://symfony.com/schema/dic/services"
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd"
>
    <services>
        <defaults public="true" />
        <service class="CompanyIncorporationModule\UseCase\BusinessServices\Api\GetBankOffers\Command"
                 id="company_incorporation_module.use_case.business_services.get_bank_offers.command">
            <argument id="company_incorporation_module.use_case.business_services.get_bank_offers.factory" type="service"/>
            <argument id="business_services_module.barclaycard_module.deciders.barclaycard_decider" type="service"/>
            <argument id="business_services_module.facades.select_offers_facade" type="service"/>
            <argument id="business_services_module.providers.choices_provider" type="service"/>
            <argument id="business_data_module.deciders.bdg_enabled_decider" type="service"/>
            <argument id="business_services_module.providers.cashback_provider" type="service"/>
            <argument id="business_services_module.views.business_services_view_factory" type="service"/>
            <argument id="services.event_service" type="service"/>
        </service>

        <service class="CompanyIncorporationModule\UseCase\BusinessServices\Api\GetBankOffers\Factory"
                 id="company_incorporation_module.use_case.business_services.get_bank_offers.factory">
            <argument id="package_module.validators.partner_package_validator" type="service"/>
        </service>

        <service class="CompanyIncorporationModule\UseCase\BusinessServices\Api\ProcessBankOffers\Command"
                 id="company_incorporation_module.use_case.business_services.process_bank_offers.command">
            <argument id="company_incorporation_module.use_case.business_services.process_bank_offers.factory" type="service"/>
            <argument id="business_services_module.facades.select_offers_facade" type="service"/>
            <argument id="services.event_service" type="service"/>
        </service>

        <service class="CompanyIncorporationModule\UseCase\BusinessServices\Api\ProcessBankOffers\Factory"
                 id="company_incorporation_module.use_case.business_services.process_bank_offers.factory">
        </service>
    </services>
</container>