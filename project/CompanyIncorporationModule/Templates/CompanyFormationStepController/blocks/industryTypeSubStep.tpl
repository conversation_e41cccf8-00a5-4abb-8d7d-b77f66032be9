<div class="mb-3" id="IndustryTypeStep">
  <div
    class="card rounded-4 p-4 {if $currentStep == 'INDUSTRY_TYPE'} d-none {else} d-block {/if}" id="industryTypeCompleted"
  >
    <div class="card-body">
      <p class="fw-semibold fs-5 card-title">
        Nature of Business
        <button
          id="editIndustryType"
          type="button"
          class="btn btn-link {if empty($sicCodes)} d-none {/if}"
        >
          Edit
        </button>
        <span
          id="industryTypeCompletedText"
          class="float-end nip-completed-card-text {if empty($sicCodes)} d-none {else} d-block {/if}"
        >
          <span class="nip-completed-card-text">COMPLETED</span>
        </span>
      </p>

      <div class="row" id="dt-company-sic-summary-completed">
        {foreach $sicCodes as $code}
          <div class="col-12 nip-purple-text fw-semibold">
            {$code.Description } ({$code.Code })
          </div>
        {/foreach}
      </div>
    </div>
  </div>
  <div
    id="industryType"
    class="{if ($currentStep == 'INDUSTRY_TYPE' && $nextStep == 'INDUSTRY_TYPE') || $currentStep == 'INDUSTRY_TYPE' && $nextStep == 'BUSINESS_BANKING' } d-block {else} d-none {/if}"
  >
    <industry-type
      company-id="{$company->getId()}"
      continue-button-label="{$industryTypeContinueButtonLabel}"
    >
    </industry-type>
  </div>
</div>