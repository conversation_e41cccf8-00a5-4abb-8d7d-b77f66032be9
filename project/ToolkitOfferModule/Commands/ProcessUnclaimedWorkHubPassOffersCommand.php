<?php

namespace ToolkitOfferModule\Commands;

use Cron\Commands\CommandAbstract;
use <PERSON>ron\INotifier;
use Exception;
use Psr\Log\LoggerInterface;
use ToolkitOfferModule\Emailers\ToolkitOfferEmailer;
use ToolkitOfferModule\Entities\CompanyToolkitOffer;
use ToolkitOfferModule\Services\ToolkitOfferService;
/**
 * @deprecated (replaced by Free day pass email story https://gitlab.com/madesimplegroup/cms/issues/121)
 */
class ProcessUnclaimedWorkHubPassOffersCommand
{
    /**
     * @var ToolkitOfferService
     */
    private $service;

    /**
     * @var ToolkitOfferEmailer
     */
    private $emailer;

    /**
     * @var LoggerInterface
     */
    private $logger;

    public function __construct(
        ToolkitOfferService $service,
        ToolkitOfferEmailer $emailer,
        LoggerInterface $logger
    )
    {
        $this->service = $service;
        $this->emailer = $emailer;
        $this->logger = $logger;
    }

    public function processOffers()
    {
        $count = 0;
        $successful = 0;

        foreach ($this->service->getUnclaimedWorkHubPassOffers() as $offer) {
            try {
                $customer = $offer->getCompany()->getCustomer();
                $this->emailer->sendWorkHubPassEmail($customer);
                $this->service->claim($offer);

                $successful++;

            } catch ( Exception $e ) {
                $this->logError($offer, $e);
            }

            $count++;
        }

        $this->logSummary($count, $successful);
    }

    private function logError(CompanyToolkitOffer $offer, Exception $e): void
    {
        $this->logger->error(
            'Work Hub offer was not process successfully.',
            [
                'companyId' => $offer->getCompany()->getId(),
                'offerType' => $offer->getToolkitOffer()->getType(),
                'error' => $e->getMessage(),
                'e' => $e,
            ]
        );
    }

    private function logSummary($count, $successful): void
    {
        $this->logger->debug(sprintf('Work Hub offers processed for %d companies, %d successfully.', $count, $successful));
    }
}
