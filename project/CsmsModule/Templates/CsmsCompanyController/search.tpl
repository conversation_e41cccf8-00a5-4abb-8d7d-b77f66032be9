{extends 'base.tpl'}

{block additionalHead}
    <style>
        .fw-bold {
            font-weight: 600 !important;
        }
        .link {
            color: #2680EB !important;
            text-decoration: underline;
            font-weight: normal;
        }
        .csms-search-company-tab {
            display: flex;
            justify-content: space-between;
            width: 100% !important;
            max-width: 800px !important;
        }
        .csms-search-company-tab > a {
            color: #00204E !important;
            border: 1px solid #00204E !important;
            background-color: white !important;
            font-weight: 600;
            width: 250px;
            text-align: center;
            margin-bottom: 12px;
        }
        .csms-search-company-tab > a.active {
            background-color: #00204E !important;
            color: white !important;
        }
        .text-primary {
            color: #2680EB !important;
            text-decoration: underline;
        }
        .bg-gray {
            background-color: #EDF3F5;
        }
        .full-bleed {
            box-shadow: 0 0 0 100vmax #EDF3F5;
            clip-path: inset(0 -100vmax);
        }
        input:disabled {
            background-color: white !important;
        }
        .form-control:focus {
            outline: none;
            border: none;
            box-shadow: none;
            background-color: white !important;
        }
        .company-result-row {
            border: 1px solid #ddd;
            display: block;
            padding: 0 10px 10px 10px;
            margin-bottom: 20px;
            font-family: "Foundry <PERSON> W01";
        }
        .company-result-row > a, .company-result-row > a:hover, .company-result-row > a:active {
            color: #000000;
            text-decoration: none;
        }

        #csms-company-search form { margin: 0; }
        #csms_uk_company_name_search_form_companyName {
            border: 0;
            height: auto;
            font-size: 22px;
            box-shadow: none;
        }
        #csms-company-search .form-group { margin: 0px 15px; }
        #csms-company-search div#name-search-form-container div { padding: 0 6px; }
        #csms-company-search .form-group .help-block { font-weight: normal }
    </style>
{/block}

{block content}
    <div class="width100 bg-white padcard-mobile">
        <div class="container">
            <div class="row">
                <div class="col-xs-12">
                    <h1 class="fw-bold mt-4">Find a Company Credit Report</h1>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-12">
                    {include 'NotificationModule/Templates/oldNotifications.tpl' items = $flashMessageNotifications}
                </div>
            </div>
            <div class="row">
                <div class="col-xs-12">
                    <div class="row">
                        <div class="col-xs-12">
                            <nav class="csms-search-company-tab nav nav-pills my-4" id="myTab" role="tablist">
                                <a
                                    class="nav-link custom-tab active"
                                    id="uk-company-tab"
                                    data-bs-toggle="tab"
                                    data-bs-target="#uk-company-search-form"
                                    type="button" role="tab"
                                    aria-controls="uk-company-search-form"
                                    aria-selected="true"
                                >
                                    UK Company (£1.99)
                                </a>
                                <a
                                    class="nav-link custom-tab"
                                    id="uk-director-tab"
                                    data-bs-toggle="tab"
                                    data-bs-target="#uk-director-search-form"
                                    type="button" role="tab"
                                    aria-controls="uk-director-search-form"
                                    aria-selected="false"
                                >
                                    UK Director (FREE)
                                </a>
                                <a
                                    class="nav-link custom-tab"
                                    id="international-company-tab"
                                    data-bs-toggle="tab"
                                    data-bs-target="#international-company-search-form"
                                    type="button"
                                    role="tab"
                                    aria-controls="international-company-search-form"
                                    aria-selected="false"
                                >
                                    International (£19.99)
                                    <span class="badge rounded-pill bg-light text-dark fw-bold">Soon</span>
                                </a>
                            </nav>

                            <div class="tab-content">
                                <div class="tab-pane fade show active" id="uk-company-search-form" role="tabpanel" aria-labelledby="uk-company-tab">
                                    {include file='./blocks/ukCompanySearch.tpl' form=$ukCompanySearchForm}
                                </div>
                                <div class="tab-pane fade" id="uk-director-search-form" role="tabpanel" aria-labelledby="uk-director-tab">
                                    {include file='./blocks/ukDirectorSearch.tpl' form=$ukDirectorSearchForm}
                                </div>
                                <div class="tab-pane fade" id="international-company-search-form" role="tabpanel" aria-labelledby="international-company-tab">
                                    {include file='./blocks/internationalCompanySearch.tpl' form=$internationalCompanySearchForm}
                                </div>
                            </div>


                        </div>
                    </div>
                </div>
            </div>

            {include file='./blocks/ukCompaniesResults.tpl'}
            {include file='./blocks/ukDirectorsResults.tpl'}

            {if !isset($ukCompaniesSearchQuery) && !isset($ukDirectorSearchQuery) && !isset($internationalCompaniesSearchQuery)}
                <div class="col-md-11 mt-4 mb-5" style="text-align: left; padding-top: 20px">
                    <h4 class="fw-bold">Gain valuable insight into any business.</h4>
                    <p class="fw-normal mt-4">
                        Instantly run company credit checks, view full company reports, annual accounts, CCJ history and much more for £1.99.
                    </p>
                    <p><a class="text-primary fw-normal" href="/csms_reports/download_sample/">View Sample Report</a></p>
                </div>
            {/if}

            {if !$reportCreditsView->hasAvailableCredits()}
                <div class="col-md-11 mt-4 py-4 bg-gray full-bleed" style="text-align: left; padding-top: 20px">
                    <h4 class="fw-bold mt-4">Want to save time and money? Sign our Report Subscription package instead!</h4>
                    <p class="fw-normal mt-4">Get instant access to multiple UK company reports and save time and money with a monthly subscription package.</p>
                    {if isset($matrixStrip.blocks)}
                        <div class="width100 mb-4 py-4">
                            {foreach $matrixStrip.blocks as $block}
                                {include file="./blocks/subscriptionPlan.tpl" block=$block}
                            {/foreach}
                        </div>
                    {/if}
                </div>
            {else}
                <div id="current-report-subscription" class="row mt-4 mb-2" style="padding-top: 20px;">
                    <h4 class="fw-bold">Current Report Subscription</h4>
                </div>
                <div class="width100 mb-4">
                        <div class="row">
                            <div class="col-xs-12 col-sm-6 col-md-6 col-lg-6 col-xl-6">
                                <table class="table table-bordered">
                                    <tbody>
                                    <tr>
                                        <th class="active fw-bold">Available report credits:</th>
                                        <td>{$reportCreditsView->getTotalAvailableCredits()}</td>
                                    </tr>
                                    <tr>
                                        <th class="active fw-bold">Purchase date:</th>
                                        <td>{$reportCreditsView->getFormattedPurchaseDate()}</td>
                                    </tr>
                                    <tr>
                                        <th class="active fw-bold">Expiration date:</th>
                                        <td>{$reportCreditsView->getFormattedExpirationDate()}</td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            {/if}
        </div>
    </div>
{/block}