<?php

namespace CsmsModule\Views;

use DateTime;
use Models\Products\Product;

class ReportCreditView
{
    /**
     * @var int
     */
    private $totalAvailableCredits;
    /**
     * @var DateTime|null
     */
    private $purchaseDate;
    /**
     * @var DateTime|null
     */
    private $expirationDate;
    /**
     * @var bool
     */
    private $canSeeCredits;

    public function __construct(int $totalAvailableCredits, ?DateTime $purchaseDate, ?DateTime $expirationDate, bool $canSeeCredits)
    {
        $this->totalAvailableCredits = $totalAvailableCredits;
        $this->purchaseDate = $purchaseDate;
        $this->expirationDate = $expirationDate;
        $this->canSeeCredits = $canSeeCredits;
    }

    public function getTotalAvailableCredits(): int
    {
        return $this->totalAvailableCredits;
    }

    public function getPurchaseDate(): ?DateTime
    {
        return $this->purchaseDate;
    }

    public function getFormattedPurchaseDate(): string
    {
        return $this->getPurchaseDate() ? $this->getPurchaseDate()->format('d/m/Y') : '';
    }

    public function getExpirationDate(): ?DateTime
    {
        return $this->expirationDate;
    }

    public function getFormattedExpirationDate(): string
    {
        return $this->getExpirationDate() ? $this->getExpirationDate()->format('d/m/Y') : '';
    }

    public function canSeeCredits(): bool
    {
        return $this->canSeeCredits;
    }

    public function getUpgradeProductId(): string
    {
        if ($this->getTotalAvailableCredits() == 0) {
            return Product::PRODUCT_10_COMPANY_REPORTS;
        } else if ($this->getTotalAvailableCredits() > 0 && $this->getTotalAvailableCredits() <= 10) {
            return Product::PRODUCT_50_COMPANY_REPORTS;
        } else {
            return Product::PRODUCT_UNLIMITED_COMPANY_REPORTS;
        }
    }

    public function hasAvailableCredits(): bool
    {
        return $this->getTotalAvailableCredits() > 0;
    }
}