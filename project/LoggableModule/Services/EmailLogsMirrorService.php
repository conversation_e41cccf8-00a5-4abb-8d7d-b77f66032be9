<?php

namespace LoggableModule\Services;

use DataGrid\src\DataGrid\Doctrine\DoctrineDataSource;
use Entities\Customer;
use LoggableModule\Repositories\EmailLogMirrorRepository;

class EmailLogsMirrorService
{

    protected $repository;

    /**
     * @param EmailLogMirrorRepository $repository
     */
    public function __construct(EmailLogMirrorRepository $repository)
    {
        $this->repository = $repository;
    }

    /**
     * @param Customer $customer
     * @return DoctrineDataSource
     */
    public function getEmailsLogDataSource(Customer $customer = NULL)
    {
        $qb = $this->repository->getEmailsLogQueryBuilder($customer);
        return new DoctrineDataSource($qb);
    }

    public function getEmailsLog(Customer $customer = NULL)
    {
        $qb = $this->repository->getEmailsLogQueryBuilder($customer);
        return $qb->getQuery()->getArrayResult();
    }
}