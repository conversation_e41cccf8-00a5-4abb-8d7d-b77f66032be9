{extends '@structure/layout.tpl'}

{block content}
    <div id="retail-matrix-progress" class="width100 bg-white">
        <div class="container">

            {if $showProgressBar}
                <div class="row">
                    <div class="col-md-12">
                        <ul class="progress-indicator">
                            <li class="completed"><span>Search</span><span class="bubble"></span></li>
                            <li class="active"><span>Select</span><span class="bubble"></span></li>
                            <li><span>Buy</span><span class="bubble"></span></li>
                            <li><span>Complete</span><span class="bubble"></span></li>
                        </ul>
                    </div>
                </div>
            {/if}

            <div class="row">
                <div class="col-xs-12">
                     {include 'NotificationModule/Templates/oldNotifications.tpl' items = $flashMessageNotifications}

                    <h1 class="font-300 top10 btm10">{$introductionStrip.title nofilter}</h1>
                    <p class="btm30"><strong>{$introductionStrip.description nofilter}</strong></p>
                </div>
            </div>
            <div class="row matrix-tabs">
                <div class="col-xs-12" style="border-bottom: 1px solid #ddd;">
                    <ul class="nav nav-tabs" role="tablist">
                        <li role="presentation" class="active"><a href="#limited" aria-controls="limited" role="tab" data-toggle="tab">Limited Company</a></li>
                        <li role="presentation"><a href="#contractors" aria-controls="contractors" role="tab" data-toggle="tab">Contractors</a></li>
                        <li role="presentation"><a href="#nonresidents" aria-controls="nonresidents" role="tab" data-toggle="tab">Non-Residents</a></li>
                        <li role="presentation"><a href="#guarantee" aria-controls="guarantee" role="tab" data-toggle="tab">Guarantee</a></li>
                        <li role="presentation"><a href="#llp" aria-controls="llp" role="tab" data-toggle="tab">LLP</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Tab panes -->
    <div class="tab-content">
        <div role="tabpanel" class="tab-pane active" id="limited">
            {include './blocks/tabLimited.tpl' packageUrl=$packageUrl}
        </div>
        <div role="tabpanel" class="tab-pane" id="contractors">
            {include './blocks/tabContractors.tpl' packageUrl=$packageUrl}
        </div>
        <div role="tabpanel" class="tab-pane" id="nonresidents">
            {include './blocks/tabNonResidents.tpl' packageUrl=$packageUrl}
        </div>
        <div role="tabpanel" class="tab-pane" id="guarantee">
            {include './blocks/tabGuarantee.tpl' packageUrl=$packageUrl}
        </div>
        <div role="tabpanel" class="tab-pane" id="llp">
            {include './blocks/tabLLP.tpl' packageUrl=$packageUrl}
        </div>
    </div>

    <div class="container-fluid home-banner6 padcard-mobile">
        <div class="container">
            <div class="row">
                <h2 class="top0 text-center">{$featuresStrip.title nofilter}</h2>
                {foreach $featuresStrip.features as $feature}
                    <div class="col-xs-12 col-md-4 text-center">
                        <i class="fa fa-4x fa-fw padcard-mobile {$feature.icon nofilter}" aria-hidden="true"></i>
                        <p class="lead">{$feature.title nofilter}</p>
                        <p>{$feature.description nofilter}</p>
                    </div>
                {/foreach}
            </div>
        </div>
    </div>
    <div class="width100 plus-matrix"><img src="{$urlImgs}plus-matrix.png" class="center-block" alt=""/></div>
    
    {include file="@structure/toolkitStrip.tpl"}
   
    <div class="width100 home-banner6 padcard-mobile">
        <div class="container"> 
            <div class="row">
                <div class="col-xs-12 text-center">
                    <h2 class="top0">{$videoStrip.title nofilter}</h2>
                    <p class="lead">{$videoStrip.subtitle nofilter}</p>
                </div>
                <div class="col-xs-12 col-md-4">
                    <div class="embed-responsive embed-responsive-16by9">
                        <iframe class="embed-responsive-item" src="{$videoStrip.videoUrl nofilter}" allowfullscreen></iframe>
                    </div>
                </div>
                <div class="col-xs-12 col-md-8">
                    <p class="lead top10">{$videoStrip.description1 nofilter}</p>
                    <p class="lead margin0">{$videoStrip.description2 nofilter}</p>
                </div>
            </div>
        </div>
    </div>

    {include file="@structure/feefoStrip.tpl"}

    <div class="width100 bg-white padcard-mobile" style="border-bottom: 2px solid #ddd;">
        <div class="container">
            <div class="row">
                <div class="col-xs-12">
                    <h2 class="top0 text-center">{$faqStrip.title nofilter}</h2>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-12">
                    {if isset($faqStrip.faqs)}
                        {foreach $faqStrip.faqs as $faq}
                            <h4 class="display-inline">{$faq.title nofilter}</h4>
                            {$faq.description nofilter}
                        {/foreach}
                    {/if}
                </div>
            </div>
        </div>
    </div>

    <script>
        (function(){
            $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
                if($(e.target).attr("href") === '#contractors'){
                    $('#contractor-terms-and-conditions').addClass('show');
                } else {
                    $('#contractor-terms-and-conditions').removeClass('show');
                }
            });
        })();

        (function() {
            if (window.location.hash == '#callback-form-container') {
                $('a[href=#nonresidents]').click();
            }
        })();
    </script>
{/block}

{include file="@footer.tpl"}
