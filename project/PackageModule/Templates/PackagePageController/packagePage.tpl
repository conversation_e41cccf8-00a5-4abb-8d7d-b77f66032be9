{extends '@structure/layout.tpl'}

{block content}
    <div class="width100 bg-white padcard-mobile">
        <div class="container">
            <div class="row">
                <div class="col-xs-12 col-md-9">
                    <h1>{$introductionStrip.title nofilter}</h1>
                    <p class="lead">
                        {$introductionStrip.lead nofilter}

                    </p>
                    {if isset($introductionStrip.description)}<p class="btm20">{$introductionStrip.description nofilter}</p>{/if}
                </div>
                <div class="col-xs-12  col-md-3 hidden-xs">
                    <a class="pull-right" href="//www.feefo.com/feefo/viewvendor.jsp?logon=www.madesimplegroup.com/companiesmadesimple" onclick="window.open(this.href, 'Feefo', 'width=1000,height=600,scrollbars,resizable'); return false;">
                        <img alt="Feefo logo" border="0" src="//www.feefo.com/feefo/feefologo.jsp?logon=www.madesimplegroup.com/companiesmadesimple&amp;template=service-percentage-white-150x150_en.png" title="See what our customers say about us">
                    </a>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-12 col-md-4 {if isset($introductionStrip.disabled)}col-md-push-3{else}col-md-push-2{/if} pad0 text-center-xs">
                    <h2 class="top10">{if isset($introductionStrip.textPrice)}<span class="h4">{$introductionStrip.textPrice} </span>{/if}{$introductionStrip.price nofilter}<span class="xsmall">+VAT</span></h2>
                </div>

                <div class="col-xs-12 col-md-2 col-md-pull-4">
                    {if isset($introductionStrip.disabled)}
                        <a href="{$introductionStrip.link nofilter}" class="btn btn-lg btn-default col-md-push-4">{$introductionStrip.cta nofilter}</a>
                    {else}
                        {assign var=packageId value=$introductionStrip.packageId}
                        {assign var=packageUrl value=$introductionStrip.packageUrl}
                        {productImpressionBlock data=$introductionStrip listName=$introductionStrip.title}
                            {ui name="buy_package_button" buttonId="buyNowTop" actionUrl="$packageUrl" inputName="packageId" productId="$packageId" text="Buy Now" loading="slide-left" size="m"}
                        {/productImpressionBlock}
                    {/if}
                </div>
            </div>
        </div>
    </div>

    <div class="width100 bg-white padcard-mobile">
        <div class="container">
            <div class="row">
                <div class="col-xs-12 text-center">
                    <h2 class="top0">{$servicesStrip.title}</h2>
                    {if isset($servicesStrip.description)}<p class="lead">{$servicesStrip.description nofilter}</p>{/if}
                </div>
            </div>
            <div class="row">
                {foreach $servicesStrip.services as $service}
                    <div class="col-xs-12 top20">
                        <div class="row">
                            <div class="col-xs-2 col-sm-1">
                                <i class="green fa {$service.icon nofilter} fa-2x fa-fw" aria-hidden="true"></i>
                            </div>
                            <div class="col-xs-10 col-sm-11">
                                <h4 class="top0">{$service.title nofilter}</h4>
                                <p>{$service.description nofilter}</p>
                            </div>
                        </div>
                    </div>
                {/foreach}
            </div>
            <div class="row">
                <div class="col-xs-12 pad0 text-center">
                    <h2 class="top10">{$introductionStrip.price nofilter}<span class="xsmall">+VAT</span></h2>
                </div>
                <div class="col-xs-12 text-center">
                    {if isset($introductionStrip.disabled)}
                        <a href="{$introductionStrip.link nofilter}" class="btn btn-lg btn-default col-md-push-4">{$introductionStrip.cta nofilter}</a>
                    {else}
                        {assign var=packageId value=$introductionStrip.packageId}
                        {assign var=packageUrl value=$introductionStrip.packageUrl}
                        {productImpressionBlock data=$introductionStrip listName=$introductionStrip.title}
                            {ui name="buy_package_button" buttonId="buyNowBottom" actionUrl="$packageUrl" inputName="packageId" productId="$packageId" text="Buy Now" loading="slide-left" size="m"}
                        {/productImpressionBlock}
                    {/if}
                </div>
            </div>
        </div>
    </div>

    {if isset($extrasStrip.title)}
        <div class="width100 bg-grey1 padcard-mobile">
            <div class="container">
                <div class="row">
                    <div class="col-xs-12 text-center">
                        <h2 class="top0">{$extrasStrip.title}</h2>
                        <p class="lead">{$extrasStrip.description nofilter}</p>
                    </div>
                </div>
                <div class="row">
                    {foreach $extrasStrip.extras as $extra}
                        <div class="col-xs-12 top20">
                            <div class="row">
                                <div class="col-xs-2 col-sm-1">
                                    <i class="green fa {$extra.icon nofilter} fa-2x fa-fw" aria-hidden="true"></i>
                                </div>
                                <div class="col-xs-10 col-sm-11">
                                    <h4 class="top0">{$extra.title nofilter}</h4>
                                    <p>{$extra.description nofilter}</p>
                                </div>
                            </div>
                         </div>
                    {/foreach}
                </div>
                <div class="row">
                    <div class="col-xs-12 text-center">
                        {if isset($extrasStrip.upgradePrice)}<h3 class="lead">{$extrasStrip.upgradePrice}</h3>{/if}
                        {if isset($extrasStrip.link)}<a href="{$extrasStrip.link nofilter}" class="btn btn-lg btn-secondary">{$extrasStrip.cta nofilter}</a>{/if}
                        {if isset($extrasStrip.viewPackages)}<p class="mbottom0 top10">{$extrasStrip.viewPackages nofilter}</p>{/if}
                    </div>
                </div>
            </div>
        </div>
    {/if}

    {if isset($toolkitStrip)}
        {include file="@structure/toolkitStrip.tpl"}
    {/if}

    {if isset($faqStrip.title)}
        <div class="width100 bg-grey1 padcard-mobile">
            <div class="container">
                <div class="row">
                    <div class="col-xs-12">
                        <h3 class="top0">{$faqStrip.title nofilter}</h3>
                        {if isset($faqStrip.faqs)}
                            {foreach $faqStrip.faqs as $faqBlock}
                                <h4 class="display-inline">{$faqBlock.title nofilter}</h4>
                                {$faqBlock.description nofilter}
                            {/foreach}
                        {elseif isset($faqStrip.faqBlock)}
                            {foreach $faqStrip.faqBlock as $faqBlock}
                                <h4 class="display-inline">{$faqBlock.title nofilter}</h4>
                                {$faqBlock.description nofilter}
                            {/foreach}
                        {/if}
                    </div>
                </div>
            </div>
        </div>
    {/if}
{/block}
