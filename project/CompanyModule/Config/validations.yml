CompanyModule\Dto\NameSearch:
  properties:
    companyName:
      - NotBlank: { message: 'Company name must be between 3 and 160 characters long.' }
      - Length: { max: 160, maxMessage: 'Company name must be between 3 and 160 characters long.', min: 3, minMessage: 'Company name must be between 3 and 160 characters long.' }
  constraints:
    - Callback: 'validateCompanyName'
        
CompanyModule\Dto\SicCodesData:
  properties:
    sicCode1:
      - NotBlank: { message: 'Please provide at least one SIC code' }
      - Regex: {pattern: '/^[0-9]{5}$/i', message: 'Please provide correct SIC code'}
    sicCode2:
      - Regex: {pattern: '/^[0-9]{5}$/i', message: 'Please provide correct SIC code'}
    sicCode3:
      - Regex: {pattern: '/^[0-9]{5}$/i', message: 'Please provide correct SIC code'}
    sicCode4:
      - Regex: {pattern: '/^[0-9]{5}$/i', message: 'Please provide correct SIC code'}
        
CompanyModule\Dto\DeleteCompanyData:
  properties:
    reason:
      - NotBlank: { message: 'Please provide reason' }
