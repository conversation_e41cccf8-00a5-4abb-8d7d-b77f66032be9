<?php

namespace CompanyModule\Entities\Settings;

use FrontModule\Forms\CFRegisteredOffice\CFRegisteredOfficeForm;
use Entities\Company;
use Models\Products\Package;
use Doctrine\ORM\Mapping as Orm;
use Models\Products\Product;

/**
 * @Orm\Entity
 */
class RegisteredOfficeUpsellSetting extends CompanySetting
{
    /**
     * @param Company $company
     * @param int|null $type
     * @param int|null $tokenId
     */
    public function __construct(Company $company, $type = NULL, $tokenId = NULL)
    {
        parent::__construct($company, ['type' => $type, 'token_id' => $tokenId]);
    }

    /**
     * @return int
     */
    public function getType()
    {
        return $this->value['type'];
    }

    /**
     * @param int $type
     */
    public function setType($type)
    {
        $this->value['type'] = $type;
    }

    /**
     * @return int
     */
    public function getTokenId()
    {
        return $this->value['token_id'];
    }

    /**
     * @param int $tokenId
     */
    public function setTokenId($tokenId)
    {
        $this->value['token_id'] = $tokenId;
    }

    /**
     * @return bool
     */
    public function isFullPrivacy()
    {
        return $this->getType() == Package::PACKAGE_FULL_PRIVACY;
    }

    /**
     * @return bool
     */
    public function isRegisteredPrivacy()
    {
        return $this->getType() == Product::PRODUCT_REGISTERED_OFFICE_SERVICE;
    }

    /**
     * @return bool
     */
    public function isNotUsingOwnAddress()
    {
        return $this->getType() != CFRegisteredOfficeForm::USE_OWN_ADDRESS;
    }

    /**
     * @return bool
     */
    public function hasType()
    {
        return !empty($this->getType());
    }


}
