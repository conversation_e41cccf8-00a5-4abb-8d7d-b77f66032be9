<?php

namespace MailScanModule\Dto;

class Service
{
    public const STATUS_ACTIVE = 'ACTIVE';
    public const STATUS_NON_ACTIVE_PENDING = 'PENDING';
    public const STATUS_NON_ACTIVE_LOST = 'LOST';

    private string $companyId;
    private string $type;
    private string $status;
    private ?string $mailForwardingId;
    private ?string $serviceStatus;
    private ?string $mailHandlingId;
    private ?string $cssClass;
    private ?\DateTime $dateStart;

    public function __construct(
        string $companyId,
        string $type,
        string $status,
        ?string $mailForwardingId,
        ?string $serviceStatus,
        ?string $mailHandlingId,
        ?string $cssClass = null,
        ?\DateTime $dateStart = null,
    ) {
        $this->companyId = $companyId;
        $this->type = $type;
        $this->status = $status;
        $this->mailForwardingId = $mailForwardingId;
        $this->serviceStatus = $serviceStatus;
        $this->mailHandlingId = $mailHandlingId;
        $this->cssClass = $cssClass;
        $this->dateStart = $dateStart;
    }

    public static function from(
        string $companyId,
        string $type,
        string $status,
        ?string $cssClass = null,
        ?\DateTime $dateStart = null,
        ?string $mailForwardingId = null,
        ?string $serviceStatus = null,
        ?string $mailHandlingId = null,
    ): self {
        return new self($companyId, $type, $status, $mailForwardingId, $serviceStatus, $mailHandlingId, $cssClass, $dateStart);
    }

    public static function empty(): Service
    {
        return new self('', '', '', null, null, null, null, null);
    }

    public function isEmpty(): bool
    {
        return $this->companyId === '';
    }

    public function isActive(): bool
    {
        return $this->getServiceStatus() === self::STATUS_ACTIVE;
    }

    public function getCompanyId(): string
    {
        return $this->companyId;
    }

    public function getType(): string
    {
        return $this->type;
    }

    public function getStatus(): string
    {
        return $this->status;
    }

    public function getCssClass(): ?string
    {
        return $this->cssClass;
    }

    public function getDateStart(): ?\DateTime
    {
        return $this->dateStart;
    }

    public function getMailForwardingId(): ?string
    {
        return $this->mailForwardingId;
    }

    public function getServiceStatus(): string
    {
        if (!is_null($this->serviceStatus)) {
            if (mb_strtoupper($this->serviceStatus) === self::STATUS_ACTIVE && mb_strtoupper($this->status) === self::STATUS_ACTIVE) {
                return self::STATUS_ACTIVE;
            }

            return mb_strtoupper($this->serviceStatus);
        }

        return mb_strtoupper($this->status);
    }

    public function getMailHandlingId(): ?string
    {
        return $this->mailHandlingId;
    }
}
