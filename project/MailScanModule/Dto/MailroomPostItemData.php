<?php

declare(strict_types=1);

namespace MailScanModule\Dto;

use MailScanModule\ApiClient\MailroomApiClient;
use MailScanModule\Enums\InboxStatusEnum;
use MailScanModule\Enums\PostItemTypeEnum;
use MailScanModule\Enums\StatusEnum;
use MailScanModule\Helpers\MailboxProductPropertyHelper;
use Utils\Helpers\ArrayHelper;

class MailroomPostItemData implements \JsonSerializable
{
    public function __construct(
        private readonly string|int|null $id,
        private readonly ?string $companyName,
        private readonly ?string $companyNumber,
        private ?string $type,
        private readonly ?string $sender,
        private ?string $fileName,
        private readonly ?string $batch,
        private readonly ?string $operator,
        private ?array $details,
        private ?array $events,
        private readonly ?\DateTime $dtc,
        private ?string $status = null,
        private ?int $transactionId = null,
        private ?string $tooltip = null,
        private ?string $cta = null,
        private ?string $inboxStatus = null,
        private bool $canBeDownloaded = false,
        private ?int $companyId = null,
        private ?string $desiredStatus = null,
        private bool $requiresPayment = false,
        private ?float $inboxPrice = null,
        private readonly ?bool $isLegacy = false,
        private readonly ?bool $legacyReleasedStatus = false,
    ) {
    }

    public function __toString(): string
    {
        return json_encode($this->toArray(), JSON_PRETTY_PRINT);
    }

    public function getId(): string|int|null
    {
        return $this->id;
    }

    public function getCompanyName(): ?string
    {
        return $this->companyName;
    }

    public function getCompanyNumber(): ?string
    {
        return $this->companyNumber;
    }

    public function getType(): ?string
    {
        if (
            !in_array(
                PostItemTypeEnum::from($this->type),
                [
                    PostItemTypeEnum::TYPE_STATUTORY,
                    PostItemTypeEnum::TYPE_NON_STATUTORY,
                    PostItemTypeEnum::TYPE_PARCEL,
                ]
            )
        ) {
            return PostItemTypeEnum::TYPE_NON_STATUTORY->value;
        }

        return $this->type;
    }

    public function getSender(): ?string
    {
        return $this->sender;
    }

    public function getFileName(): ?string
    {
        return $this->fileName;
    }

    public function getBatch(): ?string
    {
        return $this->batch;
    }

    public function getOperator(): ?string
    {
        return $this->operator;
    }

    public function getDetails(): ?array
    {
        return $this->details;
    }

    public function getDetail(string $key): ?string
    {
        return ArrayHelper::get($this->details, $key, null);
    }

    public function setDetail(string $key, string $value): self
    {
        $this->details[$key] = $value;

        return $this;
    }

    public function getEvents(): ?array
    {
        return $this->events;
    }

    public function getDtc(): ?\DateTime
    {
        return $this->dtc;
    }

    public function getTransactionId(): ?int
    {
        return $this->transactionId;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getDesiredStatus(): ?string
    {
        return $this->desiredStatus;
    }

    public function setDesiredStatus(string $desiredStatus): self
    {
        $this->desiredStatus = $desiredStatus;

        return $this;
    }

    public function hasUnpaidQuotaCharges(): bool
    {
        return intval($this->getDetail(MailroomApiClient::CHARGING_ATTEMPTS_DETAIL_NAME)) > 0;
    }


    public function setTooltip(string $tooltip): self
    {
        $this->tooltip = $tooltip;

        return $this;
    }

    public function setCta(?string $cta): self
    {
        $this->cta = $cta;

        return $this;
    }

    public function setInboxStatus(InboxStatusEnum $inboxStatus): self
    {
        $this->inboxStatus = $inboxStatus->value;

        return $this;
    }

    public function getInboxStatus(): ?string
    {
        return InboxStatusEnum::fromStatus(StatusEnum::from($this->status))->value;
    }

    public function isStatutory(): bool
    {
        return PostItemTypeEnum::from($this->type) === PostItemTypeEnum::TYPE_STATUTORY;
    }

    public function isNonStatutory(): bool
    {
        return PostItemTypeEnum::from($this->type) === PostItemTypeEnum::TYPE_NON_STATUTORY;
    }

    public function isParcel(): bool
    {
        return PostItemTypeEnum::from($this->type) === PostItemTypeEnum::TYPE_PARCEL;
    }

    public function isLegacy(): bool
    {
        return $this->isLegacy;
    }

    public function legacyReleasedStatus(): bool
    {
        return $this->legacyReleasedStatus;
    }

    public function setCompanyId(?int $companyId): self
    {
        $this->companyId = $companyId;

        return $this;
    }

    public function setCanBeDownloaded(bool $canBeDownloaded): self
    {
        $this->canBeDownloaded = $canBeDownloaded;

        return $this;
    }

    public function setType(PostItemTypeEnum $type): self
    {
        $this->type = $type->value;

        return $this;
    }

    public function getFormat(): string
    {
        return $this->isParcel()
            ? MailboxProductPropertyHelper::FORMAT_PARCEL
            : MailboxProductPropertyHelper::FORMAT_POST_ITEM;
    }

    public function setRequiresPayment(bool $requiresPayment): self
    {
        $this->requiresPayment = $requiresPayment;

        return $this;
    }

    public function removePdfFileInfo(): self
    {
        $this->fileName = null;

        if (isset($this->details['pdf_link'])) {
            unset($this->details['pdf_link']);
        }

        return $this;
    }

    /**
     * @throws \Exception
     */
    public function setInboxStatusByStatus(): self
    {
        $this->setInboxStatus(InboxStatusEnum::fromStatus(StatusEnum::from($this->status)));

        return $this;
    }

    public function setInboxPrice(float|string $inboxPrice): self
    {
        $this->inboxPrice = (float) $inboxPrice;

        return $this;
    }

    public function isStatusAdded(): bool
    {
        return StatusEnum::from($this->getStatus()) === StatusEnum::STATUS_ADDED;
    }

    public function isStatusScanOnly(): bool
    {
        return StatusEnum::from($this->getStatus()) === StatusEnum::STATUS_SCAN_ONLY;
    }

    public function isStatusWaitingPayment(): bool
    {
        return StatusEnum::from($this->getStatus()) === StatusEnum::STATUS_WAITING_PAYMENT;
    }

    public function isStatusForwarded(): bool
    {
        return StatusEnum::from($this->getStatus()) === StatusEnum::STATUS_FORWARDED;
    }

    public function isStatusToBeForwarded(): bool
    {
        return StatusEnum::from($this->getStatus()) === StatusEnum::STATUS_TO_BE_FORWARDED;
    }

    public function isStatusToBeCollected(): bool
    {
        return StatusEnum::from($this->getStatus()) === StatusEnum::STATUS_TO_BE_COLLECTED;
    }

    public function isStatusCollected(): bool
    {
        return StatusEnum::from($this->getStatus()) === StatusEnum::STATUS_COLLECTED;
    }

    public function isDownloadableStatus(): bool
    {
        return $this->isStatusScanOnly()
            || $this->isStatusToBeForwarded()
            || $this->isStatusForwarded()
            || $this->isStatusToBeCollected()
            || $this->isStatusCollected();
    }

    public function toArray(): ?array
    {
        $vars = get_object_vars($this);
        $vars['dtc'] = $this->dtc->format('Y-m-d H:i:s');

        return $vars;
    }

    public function jsonSerialize(): array
    {
        return $this->toArray();
    }
}
