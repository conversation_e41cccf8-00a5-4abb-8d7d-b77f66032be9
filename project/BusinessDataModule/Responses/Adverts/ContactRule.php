<?php

namespace BusinessDataModule\Responses\Adverts;

use Utils\Helpers\ArrayHelper;

class ContactRule
{
    /**
     * @var string
     */
    private $type;

    /**
     * @var string
     */
    private $label;

    public function __construct(string $type, string $label)
    {
        $this->type = $type;
        $this->label = $label;
    }

    public static function fromData(string $type, array $data): self
    {
        return new self(
            $type,
            ArrayHelper::get($data, 'label')
        );
    }

    public function getType(): string
    {
        return $this->type;
    }

    public function getLabel(): string
    {
        return $this->label;
    }

}