<?php

namespace BusinessDataModule\Gateways;

use BusinessDataModule\Responses\AddressResponse;
use BusinessDataModule\Responses\AdvertsResponse;
use BusinessDataModule\Responses\AdvertResponse;
use BusinessDataModule\Responses\LeadResponse;
use BusinessServicesModule\Entities\Lead;
use Entities\Company;

interface IApiGateway
{
    public function getAdvert(Company $company, string $advertId): AdvertResponse;
    public function getAdverts(Company $company, array $data): AdvertsResponse;
    public function getAddresses(Lead $lead, array $data): AddressResponse;
    public function sendLead(Lead $lead, array $data): LeadResponse;
}