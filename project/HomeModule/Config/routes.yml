homepage:
  path: /
  defaults:
    controller: home_module.controllers.home_controller
    action: renderHomepage

homepageV2:
  path: /v2/
  defaults:
    controller: home_module.controllers.home_controller
    action: renderHomepage
    variant: 0
    version: 2
  options:
    excludeFromSitemap: true

homepage_registration:
  path: /hp-company-registration.html
  defaults:
    controller: home_module.controllers.home_controller
    action: renderAlternate1
  options:
    excludeFromSitemap: true

homepage_ltd:
  path: /limited-company.html
  defaults:
    controller: home_module.controllers.home_controller
    action: renderAlternate2
  options:
    excludeFromSitemap: true

homepage_incorporation:
  path: /company-incorporation.html
  defaults:
    controller: home_module.controllers.home_controller
    action: renderAlternate3
  options:
    excludeFromSitemap: true

homepage_formation:
  path: /company-formation.html
  defaults:
    controller: home_module.controllers.home_controller
    action: renderAlternate4
  options:
    excludeFromSitemap: true

homepage_setup:
  path: /company-setup.html
  defaults:
    controller: home_module.controllers.home_controller
    action: renderAlternate5
  options:
    excludeFromSitemap: true

homepage_ch_formation:
  path: /companies-house-formation.html
  defaults:
    controller: home_module.controllers.home_controller
    action: renderAlternate6
  options:
    excludeFromSitemap: true

homepage_register_company_online:
  path: /register-a-limited-company.html
  defaults:
    controller: home_module.controllers.home_controller
    action: renderAlternate7
  options:
    excludeFromSitemap: true

homepage_business_startup:
  path: /business-startup/
  defaults:
    controller: home_module.controllers.home_controller
    action: renderAlternate8
  options:
    excludeFromSitemap: true

homepage_setup_business:
  path: /setup-business/
  defaults:
    controller: home_module.controllers.home_controller
    action: renderAlternate9
  options:
    excludeFromSitemap: true

home_award_winning_support:
  path: /value.html
  defaults:
    controller: home_module.controllers.home_controller
    action: homeAwardWinningSupport
  options:
    excludeFromSitemap: true

change_your_life:
  path: /changeyourlife.html
  defaults:
    controller: home_module.controllers.home_controller
    action: changeYourLife
  options:
    excludeFromSitemap: true

homepage_questionnaire:
  path: /questionnaire.html
  defaults:
    controller: home_module.controllers.questionnaire_controller
    action: showForm
  options:
    excludeFromSitemap: true

package_offer:
  path: /package-offer.html
  defaults:
    controller: home_module.controllers.questionnaire_controller
    action: renderPackageOffer
  options:
    excludeFromSitemap: true
  methods:  [POST]