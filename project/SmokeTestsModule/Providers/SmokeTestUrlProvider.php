<?php

namespace SmokeTestsModule\Providers;

use SmokeTestsModule\Views\SmokeTestUrlView;
use Symfony\Component\Routing\Route;

abstract class SmokeTestUrlProvider
{
    /**
     * @return SmokeTestUrlView[]
     */
    public abstract function getSmokeTestUrls(): array;

    public function getUrlsAsArray(): array
    {
        $smokeTestUrls = $this->getSmokeTestUrls();
        $urls = [];
        foreach ($smokeTestUrls as $smokeTestUrl) {
            array_push($urls, $smokeTestUrl->toArray());
        }
        sort($urls);
        return $urls;
    }
}