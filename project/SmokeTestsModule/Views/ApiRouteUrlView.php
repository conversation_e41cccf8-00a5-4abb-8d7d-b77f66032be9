<?php

namespace SmokeTestsModule\Views;

use HttpClient\Responses\ResponseInterface;
use Symfony\Component\Routing\Route;

class ApiRouteUrlView extends SmokeTestUrlView
{
    /**
     * @var Route
     */
    private $route;

    /**
     * @var string
     */
    private $method;

    public function __construct(Route $route, string $method)
    {
        $this->route = $route;
        $this->method = $method;
    }

    public function getContext(): string
    {
        return SmokeTestUrlView::CONTEXT_PUBLIC;
    }

    public function getMethod(): string
    {
        return $this->method;
    }

    public function getParams(): string
    {
        $urlParams = $this->getUrlParamsNames();

        if (!empty($requirements = $this->route->getRequirements())) {
            foreach ($requirements as $key => $value) {
                if (strpos($key, '_method') === false
                    && !in_array($key, $urlParams)
                    && !in_array(sprintf('{%s}', $key), $urlParams)
                ) {
                    $urlParams[] = $key;
                }
            }
        }

        return implode(',', $urlParams);
    }

    public function getStatusCode(): string
    {
        return ResponseInterface::UNAUTHORIZED;
    }

    public function getUrl(): string
    {
        return $this->route->getPath();
    }

}