<?php

namespace Remote\VirtualOffice\Api\Message;

use GuzzleHttp\Psr7\Response;
use InvalidArgumentException;
use Psr\Http\Message\StreamInterface;

class JsonResponse extends Response
{
    /**
     * @inheritdoc
     */
    public function __construct($statusCode, array $headers = [], StreamInterface $body = NULL, array $options = [])
    {
        parent::__construct($statusCode, $headers, $body, '1.1', null);

        $response = $this->json();
        if (!array_key_exists('success', $response)) {
            throw JsonResponseException::invalidBody();
        }
        if ($response['success'] === FALSE && $statusCode < 400) {
            throw JsonResponseException::invalidStatusCode($statusCode);
        }
    }

    /**
     * Decode the JSON response body.
     *
     * @return array
     * @throws InvalidArgumentException if the body cannot be decoded to JSON.
     */
    public function json(): array
    {
        $body = (string) $this->getBody();
        $json = json_decode($body, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new \InvalidArgumentException('Invalid JSON provided');
        }

        return $json;
    }
}
