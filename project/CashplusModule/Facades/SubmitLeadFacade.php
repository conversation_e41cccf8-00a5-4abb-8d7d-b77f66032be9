<?php

namespace CashplusModule\Facades;

use BusinessServicesModule\Entities\Lead;
use BusinessServicesModule\Exceptions\LeadSubmissionFailedException;
use CashplusModule\Clients\Client;
use CashplusModule\Factories\LtdApplicationDataFactory;
use CompanySyncModule\Services\ISynchronization;
use CompanySyncModule\Services\SynchronizationService;
use DateTime;
use Doctrine\ORM\EntityManager;
use Exception;
use HttpClient\Responses\ResponseInterface;
use JMS\Serializer\Exception\Exception as JMSException;
use Psr\Log\LoggerInterface;
use SerializingModule\Context;
use SerializingModule\SerializerInterface;
use Symfony\Component\HttpFoundation\Response;
use Utils\Helpers\ArrayHelper;

class SubmitLeadFacade
{
    /**
     * @var Client
     */
    private $client;

    /**
     * @var LtdApplicationDataFactory
     */
    private $factory;

    /**
     * @var EntityManager
     */
    private $em;

    /**
     * @var SerializerInterface
     */
    private $serializer;

    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * @var SynchronizationService
     */
    private $synchronizationService;

    public function __construct(
        Client $client,
        LtdApplicationDataFactory $factory,
        EntityManager $em,
        SerializerInterface $serializer,
        LoggerInterface $logger,
        SynchronizationService $synchronizationService
    ) {
        $this->client = $client;
        $this->factory = $factory;
        $this->em = $em;
        $this->serializer = $serializer;
        $this->logger = $logger;
        $this->synchronizationService = $synchronizationService;
    }

    public function submitLead(string $token, Lead $lead): bool
    {
        $result = true;
        $company = $lead->getCompany();

        try {
            $this->synchronizationService->synchronizeCompany($company);
        } catch (Exception | JMSException $e) {
            // ignore
        }

        $cashplusService = $lead->getPartnerCashplusService();

        $data = $this->factory->createLtdApplicationData(
            $company,
            $lead->getPartnerServicesInformation(),
            $cashplusService
        );

        $this->logger->debug(sprintf('Sending lead %s...', $lead->getId()), ['id' => $lead->getId(), 'data' => $data]);

        try {
            $serializedData = $this->serializer->serialize(
                $data, SerializerInterface::FORMAT_JSON, new Context(true)
            );

            $cashplusService->setDateSent(new DateTime());
            $cashplusService->setRequestBody($serializedData);

            $this->logger->debug(sprintf('Request data from lead %s.', $lead->getId()), ['id' => $lead->getId(), 'request' => $serializedData]);
            $responseData = $this->client->submitLead($token, $data);

            $cashplusService->setResponseCode(ResponseInterface::OK);
            $cashplusService->setMessage(ArrayHelper::get($responseData, 'message', ''));
            $cashplusService->setResponseBody(json_encode($responseData));
            $cashplusService->setReferenceNumber(ArrayHelper::get($responseData, 'referenceNumber', ''));
            $lead->markAsProcessed();
            $this->logger->debug(sprintf('Response data from lead %s.', $lead->getId()), ['id' => $lead->getId(), 'responseData' => $responseData]);
        } catch (LeadSubmissionFailedException $e) {
            $cashplusService->setResponseCode($e->getCode());
            $cashplusService->setResponseBody($e->getResponseBody());

            $responseCode = $e->getCode();
            $result = false;

            if ($responseCode === Response::HTTP_CONFLICT) {
                $logMessage = sprintf(
                    'Lead %s marked as processed but an error has been generated (%s).',
                    $lead->getId(),
                    $responseCode
                );
                $lead->markAsProcessed();
            } else {
                $logMessage = sprintf(
                    'Cashplus API returned an error (%s) when sending lead %s.',
                    $responseCode,
                    $lead->getId()
                );
            }

            $this->logger->debug($logMessage, [
                'id' => $lead->getId(),
                'code' => $e->getCode(),
                'body' => $e->getResponseBody(),
                'exception' => $e
            ]);

        }

        $this->em->flush([$lead, $cashplusService]);

        return $result;
    }
}
