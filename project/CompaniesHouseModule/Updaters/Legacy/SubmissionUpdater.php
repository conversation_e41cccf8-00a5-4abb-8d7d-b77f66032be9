<?php

namespace CompaniesHouseModule\Updaters\Legacy;

use <PERSON>bi\Connection as DibiConnection;

class SubmissionUpdater
{
    /**
     * @var DibiConnection
     */
    private $connection;

    public function __construct(DibiConnection $connection)
    {
        $this->connection = $connection;
    }

    public function removeError(int $formSubmissionId)
    {
        $this->connection
            ->update('ch_form_submission', ['response' => NULL])
            ->where('form_submission_id=%i', $formSubmissionId)
            ->execute();
    }
}