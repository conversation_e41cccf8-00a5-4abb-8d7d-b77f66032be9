<?php

namespace CompaniesHouseModule\Datagrids\Admin;

use CompaniesHouseModule\Entities\Member;
use CompanyFormationModule\Entities\IMember;
use DataGrid\src\DataGrid\DataGrid;
use DataGrid\src\DataGrid\DataGridTextColumn;
use <PERSON><PERSON>\Row as DibiRow;
use <PERSON>bi\IDataSource;
use RouterModule\Generators\IUrlGenerator;

class OfficersDatagrid extends DataGrid
{
    /**
     * @var IUrlGenerator
     */
    private $urlGenerator;

    public function __construct(IUrlGenerator $urlGenerator, IDataSource $datasource, string $primaryKey)
    {
        $this->urlGenerator = $urlGenerator;
        parent::__construct($datasource, $primaryKey);
    }

    public function init()
    {
        $this->getPaginator()->setItemsPerPage('5');
        $this->disableExport();
        $this->disableOrder();
        $this->addTextColumn('company_member_id', 'Officer Id', 30);
        $this->addFormatColumn('officerName', 'Officer Name', 30, null, [$this, 'renderName']);
        $this->addFormatColumn('companyName', 'Company Name', 30, null, [$this, 'renderCompany']);
        $this->addFormatColumn('type', 'Officer Type', 40, NULL, [$this, 'renderType']);
        $this->addFormatColumn('action', 'Action', 60, NULL, [$this, 'renderAction']);

        $this->addTextFilter('forename', 'Forename:', 'm');
        $this->addTextFilter('surname', 'Surname:', 'm');
        $this->addTextFilter('corporate_name', 'Corporate name:', 'm');
    }

    public function renderName(DataGridTextColumn $column, $text, DibiRow $row)
    {
        if ($row->corporate) {
            return $row->corporate_name;
        }

        return implode(' ', array_filter([$row->forename, $row->middle_name, $row->surname]));
    }

    public function renderCompany(DataGridTextColumn $column, $text, DibiRow $row)
    {
        return $row->company_name;
    }

    public function renderType(DataGridTextColumn $column, $text, DibiRow $row)
    {
        return sprintf(
            '%s - %s',
            $row->type,
            $row->corporate ? 'corporate' : 'person'
        );
    }

    public function renderAction(DataGridTextColumn $column, $text, DibiRow $row)
    {
        return sprintf(
            '<a href="%s">view company</a> | <a href="%s">view officer</a>',
            $this->urlGenerator->url('551 view', ['company_id' => $row->company_id], IUrlGenerator::OLD_LINK | IUrlGenerator::ADMIN_LINK),
            $this->getOfficerUrl($row)
        );
    }

    private function getOfficerUrl(DibiRow $row): string
    {
        if ($row->type == IMember::TYPE_DIRECTOR) {
            if ($row->corporate) {
                return $this->urlGenerator->url('551 CUshowDirectorCorporate', ['company_id' => $row->company_id, 'director_id' => $row->company_member_id], IUrlGenerator::OLD_LINK | IUrlGenerator::ADMIN_LINK);
            } else {
                return $this->urlGenerator->url('551 CUshowDirectorPerson', ['company_id' => $row->company_id, 'director_id' => $row->company_member_id], IUrlGenerator::OLD_LINK | IUrlGenerator::ADMIN_LINK);
            }
        }

        if ($row->type == IMember::TYPE_PSC) {
            if ($row->corporate) {
                return $this->urlGenerator->url('admin_psc_view_corporate', ['company' => $row->company_id, 'psc' => $row->company_member_id]);
            } else {
                return $this->urlGenerator->url('admin_psc_view_person', ['company' => $row->company_id, 'psc' => $row->company_member_id]);
            }
        }

        return '#';
    }

}