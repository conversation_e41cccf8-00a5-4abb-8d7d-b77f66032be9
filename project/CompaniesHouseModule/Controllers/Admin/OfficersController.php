<?php

namespace CompaniesHouseModule\Controllers\Admin;

use CompaniesHouseModule\Datagrids\Admin\IncorporationOfficersDatagrid;
use CompaniesHouseModule\Datagrids\Admin\OfficersDatagrid;
use dibi;
use RouterModule\Generators\IUrlGenerator;
use TemplateModule\Renderers\IRenderer;

class OfficersController
{
    /**
     * @var IRenderer
     */
    private $renderer;

    /**
     * @var IUrlGenerator
     */
    private $urlGenerator;

    public function __construct(IRenderer $renderer, IUrlGenerator $urlGenerator)
    {
        $this->renderer = $renderer;
        $this->urlGenerator = $urlGenerator;
    }

    public function list()
    {

        $datasource = dibi::select('
            DISTINCT
                m.company_member_id,
                m.type,
                m.corporate,
                m.forename,
                m.middle_name,
                m.surname, 
                m.corporate_name,
                co.company_id,
                co.company_name
            ')
            ->from('ch_company_member m')
            ->join('ch_company co')->on('co.company_id = m.company_id')
            ->where("m.type IN ('DIR','PSC')")
            ->where('co.deleted = 0');

        $datagrid = new OfficersDatagrid(
            $this->urlGenerator,
            $datasource,
            'company_member_id'
        );

        $incorporationDatasource = dibi::select('
            DISTINCT
                m.incorporation_member_id,
                m.type,
                m.corporate,
                m.forename,
                m.middle_name,
                m.surname, 
                m.corporate_name,
                co.company_name,
                co.company_id
            ')
            ->from('ch_incorporation_member m')
            ->join('ch_form_submission fs')->on('fs.form_submission_id = m.form_submission_id')
            ->join('ch_company co')->on('co.company_id = fs.company_id')
            ->where("m.type IN ('DIR','PSC')")
            ->where('co.deleted = 0')
            ->where("(fs.response != 'ACCEPT' || fs.response is null)");

        $incorporationDatagrid = new IncorporationOfficersDatagrid(
            $this->urlGenerator,
            $incorporationDatasource,
            'incorporation_member_id'
        );

        return $this->renderer->render(
            [
                'datagrid' => $datagrid,
                'incorporationDatagrid' => $incorporationDatagrid,
            ]
        );
    }


}