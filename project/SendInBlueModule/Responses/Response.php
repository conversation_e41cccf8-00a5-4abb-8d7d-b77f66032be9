<?php

namespace SendInBlueModule\Responses;

use HttpClient\Responses\Response as HttpResponse;

abstract class Response
{
    /**
     * @var array
     */
    private $header;

    /**
     * @var string
     */
    private $body;

    /**
     * @var int
     */
    private $code;

    public function getHeader(): array
    {
        return $this->header;
    }

    public function setHeader(array $header): void
    {
        $this->header = $header;
    }

    public function getBody(): string
    {
        return $this->body;
    }

    public function setBody(?string $body): void
    {
        $this->body = $body;
    }

    public function getCode(): int
    {
        return $this->code ?? $this->isSuccess() ? HttpResponse::OK : HttpResponse::BAD_REQUEST;
    }

    public function setCode(?int $code): void
    {
        $this->code = $code;
    }

    abstract function isSuccess(): bool;

}