<?php

namespace CsvParserModule\Observers;

use CsvParserModule\Exceptions\IncorrectStructureException;

class HeaderObserver implements IObserver
{
    private array $columns;

    private bool $checked = false;

    public function __construct(array $columns)
    {
        $this->columns = $columns;
    }

    /**
     * @throws IncorrectStructureException
     */
    public function observe(array $row): void
    {
        if (!$this->checked && $row != $this->columns)
            throw IncorrectStructureException::invalidHeader($this->columns, $row);

        $this->checked = TRUE;
    }
}
