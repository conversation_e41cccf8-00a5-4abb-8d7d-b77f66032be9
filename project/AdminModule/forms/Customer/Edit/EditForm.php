<?php

namespace AdminModule\forms\Customer\Edit;

use AdminModule\forms\Customer\Edit\IEditFormDelegate;
use CustomerModule\Entities\InvoiceAddress;
use Egulias\EmailValidator\EmailValidator;
use Entities\Customer;
use Entities\CustomerNote;
use Models\OldModels\Customer as OldCustomer;
use Framework\Forms\Controls\FControl;
use FeatureModule\Feature;
use FormModule\Dto\Address;
use Framework\Forms\FForm;
use Framework\FUser;
use Services\CustomerNoteService;
use Services\CustomerService;
use Entities\User;
use Exception;
use Utils\Date;

class EditForm extends FForm
{
    /**
     * @var IEditFormDelegate
     */
    private $delegate;

    /**
     * @var Customer
     */
    private $customer;

    /**
     * @var User
     */
    private $user;

    /**
     * @var CustomerNoteService
     */
    private $noteService;

    /**
     * @var CustomerService
     */
    private $customerService;

    /**
     * @param IEditFormDelegate $delegate
     * @param Customer $customer
     * @param User $user
     * @param CustomerNoteService $noteService
     * @param CustomerService $customerService
     */
    public function startup(IEditFormDelegate $delegate, Customer $customer, User $user, CustomerNoteService $noteService, CustomerService $customerService)
    {
        $this->delegate = $delegate;
        $this->customer = $customer;
        $this->user = $user;
        $this->noteService = $noteService;
        $this->customerService = $customerService;

        $this->buildForm();
        $this->onValid = array($this, 'process');

        $initValues = $this->customer->toArray();

        $invoiceAddress = $this->customer->getInvoiceAddress();
        if ($invoiceAddress) {
            $initValues = array_merge($initValues, [
                'differentInvoiceAddress' => TRUE,
                'invoiceRecipientName' => $invoiceAddress->getRecipientName(),
                'invoiceAddress1' => $invoiceAddress->getAddress1(),
                'invoiceAddress2' => $invoiceAddress->getAddress2(),
                'invoiceCity' => $invoiceAddress->getCity(),
                'invoicePostcode' => $invoiceAddress->getPostcode(),
                'invoiceCountryId' => $invoiceAddress->getCountryId(),
            ]);
        }

        $this->setInitValues($initValues);

        $this->start();
    }

    public function process(): void
    {
        try {
            $data = $this->getValues();

            $customer = $this->customer;

            $customer->statusId = $data['statusId'];
            $customer->titleId = $data['titleId'];
            $customer->firstName = $data['firstName'];
            $customer->middleName = $data['middleName'];
            $customer->lastName = $data['lastName'];
            $customer->companyName = $data['companyName'];
            $customer->address1 = $data['address1'];
            $customer->address2 = $data['address2'];
            $customer->city = $data['city'];
            $customer->postcode = $data['postcode'];
            $customer->countryId = $data['countryId'];
            $customer->phone = $data['phone'];
            $customer->tagId = $data['tagId'];
            $customer->roleId = $data['roleId'];
            $dateOfBirth = Date::createFromFormat(DATE_DEFAULT, $data['dateOfBirth']);
            $customer->setDateOfBirth($dateOfBirth);

            // invoice address
            $invoiceAddress = $customer->getInvoiceAddress();
            if ($data['differentInvoiceAddress']) {

                $invoiceAddress = $invoiceAddress ?: InvoiceAddress::fromAddress(Address::createDefault());

                $invoiceAddress->setRecipientName($data['invoiceRecipientName']);
                $invoiceAddress->setAddress1($data['invoiceAddress1']);
                $invoiceAddress->setAddress2($data['invoiceAddress2']);
                $invoiceAddress->setCity($data['invoiceCity']);
                $invoiceAddress->setPostcode($data['invoicePostcode']);
                $invoiceAddress->setCountryId($data['invoiceCountryId']);
                $customer->setInvoiceAddress($invoiceAddress);

            } elseif ($invoiceAddress) {
                $this->customerService->removeInvoiceAddress($invoiceAddress);
            }

            $this->customerService->save($customer);

            if ($data['statusId'] == Customer::STATUS_BLOCKED && $data['blockedReason']) {
                $entity = new CustomerNote;
                $entity->customer = $this->customer;
                $entity->user = $this->user;
                $entity->note = sprintf("%s %s", CustomerNote::BLOCKED_TEXT, $data['blockedReason']);
                $this->noteService->save($entity);
            }

            $this->clean();
            $this->delegate->customerEditSucceeded();
        } catch (Exception $e) {
            $this->delegate->customerEditFailed($e);
        }
    }

    /**
     * @param FControl $control
     * @param string $error
     * @param FControl $differentInvoiceAddressControl
     * @return bool
     */
    public function Validator_invoiceAddress(FControl $control, $error, FControl $differentInvoiceAddressControl)
    {
        if ($differentInvoiceAddressControl->getValue() && !$control->getValue()) {
            return $error;
        }
        return TRUE;
    }

    /**
     * @param FControl $control
     * @param string $error
     * @return bool
     */
    public function Validator_dateOfBirth($control, $error)
    {
        if (!$control->getValue()) {
            return $error;
        }
        $date = Date::createFromFormat(DATE_DEFAULT, $control->getValue());
        if (!$date) {
            return $error;
        }
        return TRUE;
    }

    /**
     * @param mixed $control
     * @param mixed $error
     * @param mixed $params
     * @return bool
     */
    public function Validator_blockedRequired($control, $error, $params)
    {
        $form = $control->owner;
        $blockedReason = $control->getValue();
        $statusId = $form['statusId']->getValue();
        if ($statusId != Customer::STATUS_BLOCKED) {
            return TRUE;
        }
        if (!empty($blockedReason)) {
            return TRUE;
        }
        return $error;
    }

    public function Validator_unblockPermission($control, $error, $params)
    {
        $statusId = $control->getValue();

        if ($this->customer->getStatusId() !== Customer::STATUS_BLOCKED) {
            return TRUE;
        }

        if ($statusId === Customer::STATUS_BLOCKED) {
            return TRUE;
        }

        return $this->user->isAdmin() ? TRUE : $error;
    }

    private function buildForm(): void
    {
        // personal details
        $this->addFieldset('Personal details');
        $this->addSelect('statusId', 'Status *', OldCustomer::$statuses)
            ->setFirstOption('--- Select ---')
            ->addRule([$this, 'Validator_unblockPermission'], 'Sorry, only admins can unblock accounts. Please check the customer notes to see why they were blocked and speak to Grant / Operations team to see if they can be unblocked');
        if ($this->customer->statusId != OldCustomer::STATUS_BLOCKED) {
            $this->addArea('blockedReason', 'Reason *')
                ->addRule(array($this, 'Validator_blockedRequired'), 'Please provide reason for blocking this customer')
                ->cols(40)
                ->rows(5);
        }
        $this->addSelect('titleId', 'Title', OldCustomer::$titles)
            ->setFirstOption('--- Select ---');
        $this->addText('firstName', 'Firstname *')
            ->addRule(self::Required, 'Please provide firstname!');
        $this->addText('middleName', 'Middle name');
        $this->addText('lastName', 'Lastname *')
            ->addRule(self::Required, 'Please provide lastname!');

        $this->addText('dateOfBirth', 'Date of birth *')
            ->addRule(self::Required, 'Please provide date of birth!')
            ->addRule(array($this, 'Validator_dateOfBirth'), 'Please provide a valid date of birth')
            ->placeholder('dd/mm/yyyy');
        $this->addText('companyName', 'Company name');

        // address details
        $this->addFieldset('Address details');
        $this->addText('address1', 'Address 1 *')
            ->addRule(self::Required, 'Please provide address 1!')
            ->addRule(self::MAX_LENGTH, "Address 1 can't be more than 40 characters", 100);
        $this->addText('address2', 'Address 2')
            ->addRule(self::MAX_LENGTH, "Address 2 can't be more than 40 characters", 40);
        $this->addText('city', 'City *')
            ->addRule(self::Required, 'Please provide city!')
            ->addRule(self::MAX_LENGTH, "City can't be more than 40 characters", 40);
        $this->addText('postcode', 'Postcode *')
            ->addRule(self::Required, 'Please provide post code!')
            ->addRule(self::MAX_LENGTH, "Postcode can't be more than 40 characters", 40);
        $this->addSelect('countryId', 'Country *', OldCustomer::$countries)
            ->setFirstOption('--- Select ---')
            ->addRule(self::Required, 'Please provide country!');
        $this->addText('phone', 'Phone');

        // invoice address
        $this->addFieldset('Invoice address');
        $this->addCheckbox('differentInvoiceAddress', 'Use different address', 1);
        $this->addText('invoiceRecipientName', 'Recipient name');
        $this->addText('invoiceAddress1', 'Address 1 *')
            ->addRule([$this, 'Validator_invoiceAddress'], 'Please provide address 1', $this['differentInvoiceAddress']);
        $this->addText('invoiceAddress2', 'Address 2');
        $this->addText('invoiceCity', 'City *')
            ->addRule([$this, 'Validator_invoiceAddress'], 'Please provide city!', $this['differentInvoiceAddress']);
        $this->addText('invoicePostcode', 'Postcode *')
            ->addRule([$this, 'Validator_invoiceAddress'], 'Please provide post code!', $this['differentInvoiceAddress']);
        $this->addSelect('invoiceCountryId', 'Country *', OldCustomer::$countries)
            ->setFirstOption('--- Select ---')
            ->addRule([$this, 'Validator_invoiceAddress'], 'Please provide country!', $this['differentInvoiceAddress']);

        // other
        $this->addFieldset('Other');

        $this->addSelect('tagId', 'Tag: ', OldCustomer::$tags);
        $this->addSelect('roleId', 'Role: ', Customer::$roles);
        $this->endFieldset();
        // action
        $this->addSubmit('update', 'Update Customer Details')
            ->class('btn');
    }
}
