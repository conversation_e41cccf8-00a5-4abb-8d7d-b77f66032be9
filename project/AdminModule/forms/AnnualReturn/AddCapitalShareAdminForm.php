<?php

namespace AdminModule\forms\AnnualReturn;

use AdminModule\Controlers\ARAdminControler;
use Entities\Capital;
use Entities\CapitalShare;
use Framework\Forms\FForm;
use Services\AnnualReturnService;

class AddCapitalShareAdminForm extends FForm
{
    /**
     * @var ARAdminControler
     */
    private $controler;

    /**
     * @var array
     */
    private $callback;

    /**
     * @var Capital
     */
    public $capital;

    /**
     * @var AnnualReturnService
     */
    protected $arService;

    public function startup(
        ARAdminControler $controler,
        Capital $capital,
        array $callback,
        AnnualReturnService $arService
    ) {
        $this->controler = $controler;
        $this->callback = $callback;
        $this->capital = $capital;
        $this->arService = $arService;
        $this->init();
    }

    private function init()
    {
        $this->addFieldset('Capital Share');
        $this->addText('share_class', 'Share Class *')
            ->addRule(FForm::Required, 'Please provide Share Class')
            ->setDescription('(Share Class should be unique)');

        $this->addArea('prescribed_particulars', 'Prescribed Particulars *')
            ->addRule(FForm::Required, 'Please provide Prescribed Particulars');

        $this->addText('num_shares', 'Number of Shares *')
            ->addRule(FForm::Required, 'Please provide Number of Shares')
            ->addRule(FForm::NUMERIC, 'Only digits allowed')
            ->addRule(FForm::GREATER_THAN, 'Value must be greater than 0', 0);

        $this->addText('aggregate_nominal_value', 'Aggregate Nominal Value *')
            ->addRule(FForm::Required, 'Please provide Aggregate Nominal Value')
            ->addRule(FForm::NUMERIC, 'Only digits allowed')
            ->addRule(FForm::GREATER_THAN, 'Value must be greater than 0', 0);

        $this->addText('share_value', 'Share Value ')
            ->setValue('0.0')->readonly()
            ->style('background: white;  border: 0;');

        $this->addFieldset('Action');
        $this->addSubmit('submit', 'Save and continue >')->style('width: 200px; height: 30px;')->class('btn');

        $this->onValid = $this->callback;
        $this->start();
    }

    public function process(): void
    {
        $d = $this->getValues();
        $share = new CapitalShare(
            $d['share_class'],
            $d['prescribed_particulars'],
            $d['num_shares'],
            $d['aggregate_nominal_value']
        );

        $this->capital->addShare($share);
        $this->arService->saveCapitalShare($share);
        $this->clean();
    }
}
