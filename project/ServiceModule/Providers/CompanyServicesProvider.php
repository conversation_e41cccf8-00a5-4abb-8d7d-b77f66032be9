<?php

namespace ServiceModule\Providers;

use Entities\Company;
use Entities\Service;

class CompanyServicesProvider
{
    /**
     * @return Service[]
     */
    public function getServices(Company $company): array
    {
        return $company
            ->getGroupedServices()
            ->filter(
                function (Service $service) {
                    return $service->isEnabled() && $service->isActivatedAndNotExpired();
                }
            )
            ->toArray();
    }
}
