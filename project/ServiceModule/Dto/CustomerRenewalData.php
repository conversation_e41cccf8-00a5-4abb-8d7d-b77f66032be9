<?php

declare(strict_types=1);

namespace ServiceModule\Dto;

use Entities\Company;
use Entities\Customer;
use Entities\Order;
use Libs\Basket;
use Utils\Date;
use Utils\Helpers\ArrayHelper;

class CustomerRenewalData
{
    public function __construct(
        private Customer $customer,
        private array $companies,
        private array $services,
        private Basket $basket,
        private Order $order,
        private array $newServices,
        private array $warnings,
    ) {
    }

    public function getCustomer(): Customer
    {
        return $this->customer;
    }

    public function getCompanies(): array
    {
        return $this->companies;
    }

    public function getServices(): array
    {
        return array_merge(...array_values($this->services));
    }

    public function getServicesByCompany(Company $company): array
    {
        return ArrayHelper::get($this->services, $company->getId(), []);
    }

    public function getEmailServicesByCompany(Company $company): array
    {
        $return = [];
        $failedEmailDates = [
            (new Date())->modify('-7 days'),
            (new Date())->modify('-14 days'),
        ];

        foreach ($this->getServicesByCompany($company) as $service) {
            $serviceExpiryDate = clone $service->getDtExpires();
            $serviceExpiryDate->setTime(0, 0, 0);

            foreach ($failedEmailDates as $emailDate) {
                if ($serviceExpiryDate == $emailDate) {
                    $return[] = $service;
                }
            }
        }

        return $return;
    }

    public function getBasket(): Basket
    {
        return $this->basket;
    }

    public function getOrder(): Order
    {
        return $this->order;
    }

    public function getNewServices(): array
    {
        return $this->newServices;
    }

    public function getWarnings(): array
    {
        return $this->warnings;
    }

    public function getTotalPrice(): mixed
    {
        return $this->basket->getPrice('total', true, $this->customer);
    }

    public function getSerializedBasket(): string
    {
        return $this->basket->getSerializedBasket($this->customer);
    }
}
