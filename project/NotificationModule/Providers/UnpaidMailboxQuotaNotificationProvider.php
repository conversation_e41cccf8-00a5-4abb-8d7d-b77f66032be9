<?php

declare(strict_types=1);

namespace NotificationModule\Providers;

use Entities\Customer;
use MailScanModule\ApiClient\MailroomApiClient;
use NotificationModule\Notifications\Notification;
use UserModule\Services\CustomerAvailability;

readonly class UnpaidMailboxQuotaNotificationProvider implements INotificationProvider
{
    public function __construct(
        private CustomerAvailability $customerAvailability,
        private MailroomApiClient $mailroomApiClient,
    ) {
    }

    public function getNotifications(mixed $entity): array
    {
        /** @var Customer $customer */
        $customer = $this->customerAvailability->requireLoggedInCustomer();

        $companiesWithUnpaidQuotas = $this->getCompaniesWithUnpaidQuotas($customer);

        if (empty($companiesWithUnpaidQuotas)) {
            return [];
        }

        return [
            new Notification(
                $this,
                ['companiesWithUnpaidQuotas' => $companiesWithUnpaidQuotas],
                isDismissible: false
            ),
        ];
    }

    private function getCompaniesWithUnpaidQuotas(Customer $customer): array
    {
        $companyNumbers = $customer->getCompanyNumbers();

        if (empty($companyNumbers)) {
            return [];
        }

        try {
            return array_unique(
                $this->extractCompanyNames(
                    $this->mailroomApiClient->getFailedPaymentPostItems(
                        $companyNumbers
                    )
                )
            );
        } catch (\Throwable) {
            return [];
        }
    }

    private function extractCompanyNames(array $postItems): array
    {
        return array_map(fn ($postItem) => $postItem->getCompanyName(), $postItems);
    }
}
