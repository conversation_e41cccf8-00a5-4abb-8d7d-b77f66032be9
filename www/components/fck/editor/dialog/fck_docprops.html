<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<!--
 * FCKeditor - The text editor for Internet - http://www.fckeditor.net
 * Copyright (C) 2003-2008 <PERSON><PERSON>
 *
 * == BEGIN LICENSE ==
 *
 * Licensed under the terms of any of the following licenses at your
 * choice:
 *
 *  - GNU General Public License Version 2 or later (the "GPL")
 *    http://www.gnu.org/licenses/gpl.html
 *
 *  - GNU Lesser General Public License Version 2.1 or later (the "LGPL")
 *    http://www.gnu.org/licenses/lgpl.html
 *
 *  - Mozilla Public License Version 1.1 or later (the "MPL")
 *    http://www.mozilla.org/MPL/MPL-1.1.html
 *
 * == END LICENSE ==
 *
 * Link dialog window.
-->
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<title></title>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<meta content="noindex, nofollow" name="robots" />
	<script src="common/fck_dialog_common.js" type="text/javascript"></script>
	<script type="text/javascript">

var oEditor		= window.parent.InnerDialogLoaded() ;
var FCK			= oEditor.FCK ;
var FCKLang		= oEditor.FCKLang ;
var FCKConfig	= oEditor.FCKConfig ;

//#### Dialog Tabs

// Set the dialog tabs.
window.parent.AddTab( 'General'		, FCKLang.DlgDocGeneralTab ) ;
window.parent.AddTab( 'Background'	, FCKLang.DlgDocBackTab ) ;
window.parent.AddTab( 'Colors'		, FCKLang.DlgDocColorsTab ) ;
window.parent.AddTab( 'Meta'		, FCKLang.DlgDocMetaTab ) ;

// Function called when a dialog tag is selected.
function OnDialogTabChange( tabCode )
{
	ShowE( 'divGeneral'		, ( tabCode == 'General' ) ) ;
	ShowE( 'divBackground'	, ( tabCode == 'Background' ) ) ;
	ShowE( 'divColors'		, ( tabCode == 'Colors' ) ) ;
	ShowE( 'divMeta'		, ( tabCode == 'Meta' ) ) ;

	ShowE( 'ePreview'		, ( tabCode == 'Background' || tabCode == 'Colors' ) ) ;
}

//#### Get Base elements from the document: BEGIN

// The HTML element of the document.
var oHTML = FCK.EditorDocument.getElementsByTagName('html')[0] ;

// The HEAD element of the document.
var oHead = oHTML.getElementsByTagName('head')[0] ;

var oBody = FCK.EditorDocument.body ;

// This object contains all META tags defined in the document.
var oMetaTags = new Object() ;

// Get all META tags defined in the document.
AppendMetaCollection( oMetaTags, oHead.getElementsByTagName('meta') ) ;
AppendMetaCollection( oMetaTags, oHead.getElementsByTagName('fck:meta') ) ;

function AppendMetaCollection( targetObject, metaCollection )
{
	// Loop throw all METAs and put it in the HashTable.
	for ( var i = 0 ; i < metaCollection.length ; i++ )
	{
		// Try to get the "name" attribute.
		var sName = GetAttribute( metaCollection[i], 'name', GetAttribute( metaCollection[i], '___fcktoreplace:name', '' ) ) ;

		// If no "name", try with the "http-equiv" attribute.
		if ( sName.length == 0 )
		{
			if ( oEditor.FCKBrowserInfo.IsIE )
			{
				// Get the http-equiv value from the outerHTML.
				var oHttpEquivMatch = metaCollection[i].outerHTML.match( oEditor.FCKRegexLib.MetaHttpEquiv ) ;
				if ( oHttpEquivMatch )
					sName = oHttpEquivMatch[1] ;
			}
			else
				sName = GetAttribute( metaCollection[i], 'http-equiv', '' ) ;
		}

		if ( sName.length > 0 )
			targetObject[ sName.toLowerCase() ] = metaCollection[i] ;
	}
}

//#### END

// Set a META tag in the document.
function SetMetadata( name, content, isHttp )
{
	if ( content.length == 0 )
	{
		RemoveMetadata( name ) ;
		return ;
	}

	var oMeta = oMetaTags[ name.toLowerCase() ] ;

	if ( !oMeta )
	{
		oMeta = oHead.appendChild( FCK.EditorDocument.createElement('META') ) ;

		if ( isHttp )
			SetAttribute( oMeta, 'http-equiv', name ) ;
		else
		{
			// On IE, it is not possible to set the "name" attribute of the META tag.
			// So a temporary attribute is used and it is replaced when getting the
			// editor's HTML/XHTML value. This is sad, I know :(
			if ( oEditor.FCKBrowserInfo.IsIE )
				SetAttribute( oMeta, '___fcktoreplace:name', name ) ;
			else
				SetAttribute( oMeta, 'name', name ) ;
		}

		oMetaTags[ name.toLowerCase() ] = oMeta ;
	}

	SetAttribute( oMeta, 'content', content ) ;
//	oMeta.content = content ;
}

function RemoveMetadata( name )
{
	var oMeta = oMetaTags[ name.toLowerCase() ] ;

	if ( oMeta && oMeta != null )
	{
		oMeta.parentNode.removeChild( oMeta ) ;
		oMetaTags[ name.toLowerCase() ] = null ;
	}
}

function GetMetadata( name )
{
	var oMeta = oMetaTags[ name.toLowerCase() ] ;

	if ( oMeta && oMeta != null )
		return oMeta.getAttribute( 'content', 2 ) ;
	else
		return '' ;
}

window.onload = function ()
{
	// Show/Hide the "Browse Server" button.
	GetE('tdBrowse').style.display = oEditor.FCKConfig.ImageBrowser ? "" : "none";

	// First of all, translate the dialog box texts
	oEditor.FCKLanguageManager.TranslatePage( document ) ;

	FillFields() ;

	UpdatePreview() ;

	// Show the "Ok" button.
	window.parent.SetOkButton( true ) ;

	window.parent.SetAutoSize( true ) ;
}

function FillFields()
{
	// ### General Info
	GetE('txtPageTitle').value = FCK.EditorDocument.title ;

	GetE('selDirection').value	= GetAttribute( oHTML, 'dir', '' ) ;
	GetE('txtLang').value		= GetAttribute( oHTML, 'xml:lang', GetAttribute( oHTML, 'lang', '' ) ) ;	// "xml:lang" takes precedence to "lang".

	// Character Set Encoding.
//	if ( oEditor.FCKBrowserInfo.IsIE )
//		var sCharSet = FCK.EditorDocument.charset ;
//	else
		var sCharSet = GetMetadata( 'Content-Type' ) ;

	if ( sCharSet != null && sCharSet.length > 0 )
	{
//		if ( !oEditor.FCKBrowserInfo.IsIE )
			sCharSet = sCharSet.match( /[^=]*$/ ) ;

		GetE('selCharSet').value = sCharSet ;

		if ( GetE('selCharSet').selectedIndex == -1 )
		{
			GetE('selCharSet').value = '...' ;
			GetE('txtCustomCharSet').value = sCharSet ;

			CheckOther( GetE('selCharSet'), 'txtCustomCharSet' ) ;
		}
	}

	// Document Type.
	if ( FCK.DocTypeDeclaration && FCK.DocTypeDeclaration.length > 0 )
	{
		GetE('selDocType').value = FCK.DocTypeDeclaration ;

		if ( GetE('selDocType').selectedIndex == -1 )
		{
			GetE('selDocType').value = '...' ;
			GetE('txtDocType').value = FCK.DocTypeDeclaration ;

			CheckOther( GetE('selDocType'), 'txtDocType' ) ;
		}
	}

	// Document Type.
	GetE('chkIncXHTMLDecl').checked = ( FCK.XmlDeclaration && FCK.XmlDeclaration.length > 0 ) ;

	// ### Background
	GetE('txtBackColor').value = GetAttribute( oBody, 'bgColor'		, '' ) ;
	GetE('txtBackImage').value = GetAttribute( oBody, 'background'	, '' ) ;
	GetE('chkBackNoScroll').checked = ( GetAttribute( oBody, 'bgProperties', '' ).toLowerCase() == 'fixed' ) ;

	// ### Colors
	GetE('txtColorText').value		= GetAttribute( oBody, 'text'	, '' ) ;
	GetE('txtColorLink').value		= GetAttribute( oBody, 'link'	, '' ) ;
	GetE('txtColorVisited').value	= GetAttribute( oBody, 'vLink'	, '' ) ;
	GetE('txtColorActive').value	= GetAttribute( oBody, 'aLink'	, '' ) ;

	// ### Margins
	GetE('txtMarginTop').value		= GetAttribute( oBody, 'topMargin'		, '' ) ;
	GetE('txtMarginLeft').value		= GetAttribute( oBody, 'leftMargin'		, '' ) ;
	GetE('txtMarginRight').value	= GetAttribute( oBody, 'rightMargin'	, '' ) ;
	GetE('txtMarginBottom').value	= GetAttribute( oBody, 'bottomMargin'	, '' ) ;

	// ### Meta Data
	GetE('txtMetaKeywords').value		= GetMetadata( 'keywords' ) ;
	GetE('txtMetaDescription').value	= GetMetadata( 'description' ) ;
	GetE('txtMetaAuthor').value			= GetMetadata( 'author' ) ;
	GetE('txtMetaCopyright').value		= GetMetadata( 'copyright' ) ;
}

// Called when the "Ok" button is clicked.
function Ok()
{
	// ### General Info
	FCK.EditorDocument.title = GetE('txtPageTitle').value ;

	var oHTML = FCK.EditorDocument.getElementsByTagName('html')[0] ;

	SetAttribute( oHTML, 'dir'		, GetE('selDirection').value ) ;
	SetAttribute( oHTML, 'lang'		, GetE('txtLang').value ) ;
	SetAttribute( oHTML, 'xml:lang'	, GetE('txtLang').value ) ;

	// Character Set Enconding.
	var sCharSet = GetE('selCharSet').value ;
	if ( sCharSet == '...' )
		sCharSet = GetE('txtCustomCharSet').value ;

	if ( sCharSet.length > 0 )
			sCharSet = 'text/html; charset=' + sCharSet ;

//	if ( oEditor.FCKBrowserInfo.IsIE )
//		FCK.EditorDocument.charset = sCharSet ;
//	else
		SetMetadata( 'Content-Type', sCharSet, true ) ;

	// Document Type
	var sDocType = GetE('selDocType').value ;
	if ( sDocType == '...' )
		sDocType = GetE('txtDocType').value ;

	FCK.DocTypeDeclaration = sDocType ;

	// XHTML Declarations.
	if ( GetE('chkIncXHTMLDecl').checked )
	{
		if ( sCharSet.length == 0 )
			sCharSet = 'utf-8' ;

		FCK.XmlDeclaration = '<' + '?xml version="1.0" encoding="' + sCharSet + '"?>' ;

		SetAttribute( oHTML, 'xmlns', 'http://www.w3.org/1999/xhtml' ) ;
	}
	else
	{
		FCK.XmlDeclaration = null ;
		oHTML.removeAttribute( 'xmlns', 0 ) ;
	}

	// ### Background
	SetAttribute( oBody, 'bgcolor'		, GetE('txtBackColor').value ) ;
	SetAttribute( oBody, 'background'	, GetE('txtBackImage').value ) ;
	SetAttribute( oBody, 'bgproperties'	, GetE('chkBackNoScroll').checked ? 'fixed' : '' ) ;

	// ### Colors
	SetAttribute( oBody, 'text'	, GetE('txtColorText').value ) ;
	SetAttribute( oBody, 'link'	, GetE('txtColorLink').value ) ;
	SetAttribute( oBody, 'vlink', GetE('txtColorVisited').value ) ;
	SetAttribute( oBody, 'alink', GetE('txtColorActive').value ) ;

	// ### Margins
	SetAttribute( oBody, 'topmargin'	, GetE('txtMarginTop').value ) ;
	SetAttribute( oBody, 'leftmargin'	, GetE('txtMarginLeft').value ) ;
	SetAttribute( oBody, 'rightmargin'	, GetE('txtMarginRight').value ) ;
	SetAttribute( oBody, 'bottommargin'	, GetE('txtMarginBottom').value ) ;

	// ### Meta data
	SetMetadata( 'keywords'		, GetE('txtMetaKeywords').value ) ;
	SetMetadata( 'description'	, GetE('txtMetaDescription').value ) ;
	SetMetadata( 'author'		, GetE('txtMetaAuthor').value ) ;
	SetMetadata( 'copyright'	, GetE('txtMetaCopyright').value ) ;

	return true ;
}

var bPreviewIsLoaded = false ;
var oPreviewWindow ;
var oPreviewBody ;

// Called by the Preview page when loaded.
function OnPreviewLoad( previewWindow, previewBody )
{
	oPreviewWindow	= previewWindow ;
	oPreviewBody	= previewBody ;

	bPreviewIsLoaded = true ;
	UpdatePreview() ;
}

function UpdatePreview()
{
	if ( !bPreviewIsLoaded )
		return ;

	// ### Background
	SetAttribute( oPreviewBody, 'bgcolor'		, GetE('txtBackColor').value ) ;
	SetAttribute( oPreviewBody, 'background'	, GetE('txtBackImage').value ) ;
	SetAttribute( oPreviewBody, 'bgproperties'	, GetE('chkBackNoScroll').checked ? 'fixed' : '' ) ;

	// ### Colors
	SetAttribute( oPreviewBody, 'text', GetE('txtColorText').value ) ;

	oPreviewWindow.SetLinkColor( GetE('txtColorLink').value ) ;
	oPreviewWindow.SetVisitedColor( GetE('txtColorVisited').value ) ;
	oPreviewWindow.SetActiveColor( GetE('txtColorActive').value ) ;
}

function CheckOther( combo, txtField )
{
	var bNotOther = ( combo.value != '...' ) ;

	GetE(txtField).style.backgroundColor = ( bNotOther ? '#cccccc' : '' ) ;
	GetE(txtField).disabled = bNotOther ;
}

function SetColor( inputId, color )
{
	GetE( inputId ).value = color + '' ;
	UpdatePreview() ;
}

function SelectBackColor( color )		{ SetColor('txtBackColor', color ) ; }
function SelectColorText( color )		{ SetColor('txtColorText', color ) ; }
function SelectColorLink( color )		{ SetColor('txtColorLink', color ) ; }
function SelectColorVisited( color )	{ SetColor('txtColorVisited', color ) ; }
function SelectColorActive( color )		{ SetColor('txtColorActive', color ) ; }

function SelectColor( wich )
{
	switch ( wich )
	{
		case 'Back'			: oEditor.FCKDialog.OpenDialog( 'FCKDialog_Color', FCKLang.DlgColorTitle, 'dialog/fck_colorselector.html', 410, 320, SelectBackColor, window ) ; return ;
		case 'ColorText'	: oEditor.FCKDialog.OpenDialog( 'FCKDialog_Color', FCKLang.DlgColorTitle, 'dialog/fck_colorselector.html', 410, 320, SelectColorText, window ) ; return ;
		case 'ColorLink'	: oEditor.FCKDialog.OpenDialog( 'FCKDialog_Color', FCKLang.DlgColorTitle, 'dialog/fck_colorselector.html', 410, 320, SelectColorLink, window ) ; return ;
		case 'ColorVisited'	: oEditor.FCKDialog.OpenDialog( 'FCKDialog_Color', FCKLang.DlgColorTitle, 'dialog/fck_colorselector.html', 410, 320, SelectColorVisited, window ) ; return ;
		case 'ColorActive'	: oEditor.FCKDialog.OpenDialog( 'FCKDialog_Color', FCKLang.DlgColorTitle, 'dialog/fck_colorselector.html', 410, 320, SelectColorActive, window ) ; return ;
	}
}

function BrowseServerBack()
{
	OpenFileBrowser( FCKConfig.ImageBrowserURL, FCKConfig.ImageBrowserWindowWidth, FCKConfig.ImageBrowserWindowHeight ) ;
}

function SetUrl( url )
{
	GetE('txtBackImage').value = url ;
	UpdatePreview() ;
}

	</script>
</head>
<body style="overflow: hidden">
	<table cellspacing="0" cellpadding="0" width="100%" border="0" style="height: 100%">
		<tr>
			<td valign="top" style="height: 100%">
				<div id="divGeneral">
					<span fcklang="DlgDocPageTitle">Page Title</span><br />
					<input id="txtPageTitle" style="width: 100%" type="text" />
					<br />
					<table cellspacing="0" cellpadding="0" border="0">
						<tr>
							<td>
								<span fcklang="DlgDocLangDir">Language Direction</span><br />
								<select id="selDirection">
									<option value="" selected="selected"></option>
									<option value="ltr" fcklang="DlgDocLangDirLTR">Left to Right (LTR)</option>
									<option value="rtl" fcklang="DlgDocLangDirRTL">Right to Left (RTL)</option>
								</select>
							</td>
							<td>
								&nbsp;&nbsp;&nbsp;</td>
							<td>
								<span fcklang="DlgDocLangCode">Language Code</span><br />
								<input id="txtLang" type="text" />
							</td>
						</tr>
					</table>
					<br />
					<table cellspacing="0" cellpadding="0" width="100%" border="0">
						<tr>
							<td style="white-space: nowrap">
								<span fcklang="DlgDocCharSet">Character Set Encoding</span><br />
								<select id="selCharSet" onchange="CheckOther( this, 'txtCustomCharSet' );">
									<option value="" selected="selected"></option>
									<option value="us-ascii">ASCII</option>
									<option fcklang="DlgDocCharSetCE" value="iso-8859-2">Central European</option>
									<option fcklang="DlgDocCharSetCT" value="big5">Chinese Traditional (Big5)</option>
									<option fcklang="DlgDocCharSetCR" value="iso-8859-5">Cyrillic</option>
									<option fcklang="DlgDocCharSetGR" value="iso-8859-7">Greek</option>
									<option fcklang="DlgDocCharSetJP" value="iso-2022-jp">Japanese</option>
									<option fcklang="DlgDocCharSetKR" value="iso-2022-kr">Korean</option>
									<option fcklang="DlgDocCharSetTR" value="iso-8859-9">Turkish</option>
									<option fcklang="DlgDocCharSetUN" value="utf-8">Unicode (UTF-8)</option>
									<option fcklang="DlgDocCharSetWE" value="iso-8859-1">Western European</option>
									<option fcklang="DlgOpOther" value="...">&lt;Other&gt;</option>
								</select>
							</td>
							<td>
								&nbsp;&nbsp;&nbsp;</td>
							<td width="100%">
								<span fcklang="DlgDocCharSetOther">Other Character Set Encoding</span><br />
								<input id="txtCustomCharSet" style="width: 100%; background-color: #cccccc" disabled="disabled"
									type="text" />
							</td>
						</tr>
						<tr>
							<td colspan="3">
								&nbsp;</td>
						</tr>
						<tr>
							<td nowrap="nowrap">
								<span fcklang="DlgDocDocType">Document Type Heading</span><br />
								<select id="selDocType" name="selDocType" onchange="CheckOther( this, 'txtDocType' );">
									<option value="" selected="selected"></option>
									<option value='<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">'>HTML
										4.01 Transitional</option>
									<option value='<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">'>
										HTML 4.01 Strict</option>
									<option value='<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Frameset//EN" "http://www.w3.org/TR/html4/frameset.dtd">'>
										HTML 4.01 Frameset</option>
									<option value='<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">'>
										XHTML 1.0 Transitional</option>
									<option value='<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">'>
										XHTML 1.0 Strict</option>
									<option value='<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Frameset//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-frameset.dtd">'>
										XHTML 1.0 Frameset</option>
									<option value='<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">'>
										XHTML 1.1</option>
									<option value='<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 3.2 Final//EN">'>HTML 3.2</option>
									<option value='<!DOCTYPE HTML PUBLIC "-//IETF//DTD HTML//EN">'>HTML 2.0</option>
									<option value="..." fcklang="DlgOpOther">&lt;Other&gt;</option>
								</select>
							</td>
							<td>
							</td>
							<td width="100%">
								<span fcklang="DlgDocDocTypeOther">Other Document Type Heading</span><br />
								<input id="txtDocType" style="width: 100%; background-color: #cccccc" disabled="disabled"
									type="text" />
							</td>
						</tr>
					</table>
					<br />
					<input id="chkIncXHTMLDecl" type="checkbox" />
					<label for="chkIncXHTMLDecl" fcklang="DlgDocIncXHTML">
						Include XHTML Declarations</label>
				</div>
				<div id="divBackground" style="display: none">
					<span fcklang="DlgDocBgColor">Background Color</span><br />
					<input id="txtBackColor" type="text" onchange="UpdatePreview();" onkeyup="UpdatePreview();" />&nbsp;<input
						id="btnSelBackColor" onclick="SelectColor( 'Back' )" type="button" value="Select..."
						fcklang="DlgCellBtnSelect" /><br />
					<br />
					<span fcklang="DlgDocBgImage">Background Image URL</span><br />
					<table cellspacing="0" cellpadding="0" width="100%" border="0">
						<tr>
							<td width="100%">
								<input id="txtBackImage" style="width: 100%" type="text" onchange="UpdatePreview();"
									onkeyup="UpdatePreview();" /></td>
							<td id="tdBrowse" nowrap="nowrap">
								&nbsp;<input id="btnBrowse" onclick="BrowseServerBack();" type="button" fcklang="DlgBtnBrowseServer"
									value="Browse Server" /></td>
						</tr>
					</table>
					<input id="chkBackNoScroll" type="checkbox" onclick="UpdatePreview();" />
					<label for="chkBackNoScroll" fcklang="DlgDocBgNoScroll">
						Nonscrolling Background</label>
				</div>
				<div id="divColors" style="display: none">
					<table cellspacing="0" cellpadding="0" width="100%" border="0">
						<tr>
							<td>
								<span fcklang="DlgDocCText">Text</span><br />
								<input id="txtColorText" type="text" onchange="UpdatePreview();" onkeyup="UpdatePreview();" /><input
									onclick="SelectColor( 'ColorText' )" type="button" value="Select..." fcklang="DlgCellBtnSelect" />
								<br />
								<span fcklang="DlgDocCLink">Link</span><br />
								<input id="txtColorLink" type="text" onchange="UpdatePreview();" onkeyup="UpdatePreview();" /><input
									onclick="SelectColor( 'ColorLink' )" type="button" value="Select..." fcklang="DlgCellBtnSelect" />
								<br />
								<span fcklang="DlgDocCVisited">Visited Link</span><br />
								<input id="txtColorVisited" type="text" onchange="UpdatePreview();" onkeyup="UpdatePreview();" /><input
									onclick="SelectColor( 'ColorVisited' )" type="button" value="Select..." fcklang="DlgCellBtnSelect" />
								<br />
								<span fcklang="DlgDocCActive">Active Link</span><br />
								<input id="txtColorActive" type="text" onchange="UpdatePreview();" onkeyup="UpdatePreview();" /><input
									onclick="SelectColor( 'ColorActive' )" type="button" value="Select..." fcklang="DlgCellBtnSelect" />
							</td>
							<td valign="middle" align="center">
								<table cellspacing="2" cellpadding="0" border="0">
									<tr>
										<td>
											<span fcklang="DlgDocMargins">Page Margins</span></td>
									</tr>
									<tr>
										<td style="border: #000000 1px solid; padding: 5px">
											<table cellpadding="0" cellspacing="0" border="0" dir="ltr">
												<tr>
													<td align="center" colspan="3">
														<span fcklang="DlgDocMaTop">Top</span><br />
														<input id="txtMarginTop" type="text" size="3" />
													</td>
												</tr>
												<tr>
													<td align="left">
														<span fcklang="DlgDocMaLeft">Left</span><br />
														<input id="txtMarginLeft" type="text" size="3" />
													</td>
													<td>
														&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td>
													<td align="right">
														<span fcklang="DlgDocMaRight">Right</span><br />
														<input id="txtMarginRight" type="text" size="3" />
													</td>
												</tr>
												<tr>
													<td align="center" colspan="3">
														<span fcklang="DlgDocMaBottom">Bottom</span><br />
														<input id="txtMarginBottom" type="text" size="3" />
													</td>
												</tr>
											</table>
										</td>
									</tr>
								</table>
							</td>
						</tr>
					</table>
				</div>
				<div id="divMeta" style="display: none">
					<span fcklang="DlgDocMeIndex">Document Indexing Keywords (comma separated)</span><br />
					<textarea id="txtMetaKeywords" style="width: 100%" rows="2" cols="20"></textarea>
					<br />
					<span fcklang="DlgDocMeDescr">Document Description</span><br />
					<textarea id="txtMetaDescription" style="width: 100%" rows="4" cols="20"></textarea>
					<br />
					<span fcklang="DlgDocMeAuthor">Author</span><br />
					<input id="txtMetaAuthor" style="width: 100%" type="text" /><br />
					<br />
					<span fcklang="DlgDocMeCopy">Copyright</span><br />
					<input id="txtMetaCopyright" type="text" style="width: 100%" />
				</div>
			</td>
		</tr>
		<tr id="ePreview" style="display: none">
			<td>
				<span fcklang="DlgDocPreview">Preview</span><br />
				<iframe id="frmPreview" src="fck_docprops/fck_document_preview.html" width="100%"
					height="100"></iframe>
			</td>
		</tr>
	</table>
</body>
</html>
