$color = #3f86e4

body
  font-family "Proxima Nova", serif
  font-size 16px
  font-weight 400
  line-height 1.5
  text-rendering optimizeLegibility
  color #747a7e

h1, h2, h3, h4, h5, h6
  font-family "Proxima Nova", serif
  font-weight 400
  color #545a5e
  margin 2em 0 1em

article
  h2
    font-size 38px

    &:first-child
      margin-top 0

  h3
    font-size 22px

p, pre
  margin 16px 0

blockquote
  border none
  position relative

  &:before
    content "\201C"
    font-size 84px
    color #DADEDF
    position absolute
    top -20px
    left 0

  p
    margin 0 0 0 1.5em
    font-size inherit

p > code, em > code
  background-color inherit
  color #2B3E4E

pre
  border none
  border-radius 0
  font-size 13px
  padding 2em

code
  font-size 12px

i
  font-family "Entypo"
  font-style normal

  &.social
    font-family "Entypo Social"

.btn
  border-radius 0

  i
    font-size 38px
    line-height 0
    position relative
    top 4px
    right 10px

blockquote
  background-color white
  border-color #e0e4e5

img.graphic
  opacity 0.1

.logotype
  display inline-block
  color inherit
  letter-spacing 3px
  font-weight 400
  border 2px solid
  padding 10px 9px 8px 11px

  &.sg
    font-size 11px
    font-weight 500
    padding 8px 6px 6px 8px
    position relative
    top -2px
    margin-left 8px
    color #89a

  a, a:hover
    color inherit
    text-decoration none


// HEADER

header
  background-color $color
  color white

  img
    height 50px
    margin 20px 0
    float left

  .logotype
    font-size 16px
    margin 30px 0

  .center
    text-align center

  .right
    text-align right

    li
      display inline-block
      font-weight 400
      margin 40px 0 0 20px

      &:first-child
        margin-left 0

      a
        color: inherit

  i#hamburger
    cursor pointer
    font-size 68px

section.intro
  text-align center
  padding 60px 0 80px
  background-color $color
  color: white

  img
    height 70px

  .blurb
    margin 0 auto 20px
    font-weight 200
    font-size 32px
    max-width 800px

  .btn + .btn
    margin-left 5px

section.highlights
  padding: 40px 0 0

  span
    font-family monospace
    font-size: 14px

    .highlighted
      background-color #F0F2F5
      padding 2px 6px 3px

section.using
  padding 40px 0
  text-align center

  a
    opacity 0.75
    -webkit-transition all 0.2s
    transition all 0.2s

    &:hover
      opacity 1

    img
      max-height 60px
      margin 50px 30px

section.author
  text-align center
  background-color #f0f2f5

  h2
    font-weight 300
    font-size 36px

  img
    margin 40px 0 20px
    width 200px
    height 200px
    border-radius 500px

  a
    font-size: 46px
    padding 0 5px
    position relative
    bottom 155px
    color white
    transition all .2s

    &:hover
      text-decoration none
      color $color

section.docs
  margin 80px 0 40px

section
  .separator
    text-align center
    border-top 2px solid #eee
    margin 40px 0 -30px
    letter-spacing 1px

    h5
      position relative
      top -36px
      display inline-block
      padding 0 1em
      background-color white
      font-weight 500

nav
  text-align right

  &.affix
    top 40px

  ul
    list-style none
    margin 0

#nav
  .nav
    width 200px

  a
    padding inherit
    display inline
    color #919E9F

    &:hover
      text-decoration underline
      background-color inherit

  > .nav > li
    > a
      color #444e4e

    ul
      display none

  li.active
    ul
      display block

    > a
      color $color

strong
  font-weight 500

.btn
  padding 1em 1.5em
  font-weight 400
  font-size 13px

.btn-clear
  padding 1.5em 6em
  border none
  color white
  background-color darken(desaturate($color, 15), 10)
  transition all 0.2s

.btn-clear:hover
  background-color darken(desaturate($color, 10), 15)
  color white

.btn-primary
  background-color #96c0c0
  border-color #96c0c0

.btn-primary:hover,
.btn-primary:focus,
.btn-primary:active
  background-color #86baba
  border-color #86baba

a
  color $color

a:hover
  color $color
  text-decoration underline

.check-list
  list-style none
  padding-left 1em

.check-list li:before
  content "\2713\0020"
  padding-right 0.5em
