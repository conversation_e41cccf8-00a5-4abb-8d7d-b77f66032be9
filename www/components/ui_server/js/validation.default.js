$.validator.setDefaults({
    highlight: function(element) {
        $(element).closest('.form-group').addClass('has-error');
    },
    unhighlight: function(element) {
        var formGroup = $(element).closest('.form-group'),
            inputGroup = $(element).parent('.input-group');
        if (inputGroup.length === 0) {
            inputGroup = $(element).parent('.form-error-after');
        }
        if (inputGroup.length) {
            var valid = true,
                errorList = this.invalid;
            $('select,input', inputGroup).each(function() {
                var name = $(this).attr('name'),
                    errorVisible = $('#' + $(this).attr('id') + '-error ul').is(':visible');
                if (errorVisible) {
                    valid = false;
                } else if (errorList && errorList.hasOwnProperty(name) && errorList[name]) {
                    valid = false;
                }
            });
            if (valid) {
                formGroup.removeClass('has-error');
            }
        } else {
            formGroup.removeClass('has-error');
        }
    },
    invalidHandler: function(event, validator) {
        var errors = validator.numberOfInvalids();
        if (errors > 0 && validator.errorList.length > 0) {
            if ('element' in validator.errorList[0]) {
                var first = $(validator.errorList[0].element);
                if (first.attr('id')) {
                    var offset = $("label[for='" + first.attr('id') + "']").offset();
                    if (offset && 'top' in offset) {
                        $('html, body').animate({
                            scrollTop: offset.top - 15
                        }, 750);
                    }
                }
                first.focus();
            }
        }

        /**
         * Dealing with ladda buttons
         */
        var formElement = validator.currentForm;
        var button = $(formElement).find('.ladda-button');
        if (button.length > 0) {
            $.ladda('stopAll');
        }
    },
    errorElement: 'span',
    errorClass: 'help-block',
    errorPlacement: function(error, element) {
        var errorPlacementAfter = element.parents('.form-error-after:eq(0)');
        if(errorPlacementAfter.length) {
            error.insertAfter(errorPlacementAfter);
        } else if(element.parent('.input-group').length) {
            var parent = element.parent();
            if (parent.nextAll('.help-block').length) {
                error.insertAfter(parent.nextAll('.help-block:last'));
            } else {
                error.insertAfter(parent);
            }
        } else if (element.attr('type') === 'radio' && !element.parent('.radio-inline').length) {
            error.insertAfter(element.parent().parent().parent());
        } else if (element.prop('type') === 'checkbox' || element.prop('type') === 'radio') {
            error.appendTo(element.parent().parent());
        } else {
            error.insertAfter(element);
        }
    }
});

/**
 *
 * @param {string} formName
 * @param {object} errors
 * @returns {object}
 */
function formatValidationErrors(formName, errors) {
    var firstErrors = {};
    for (var name in errors) {
        var elementName = formName ? formName + '[' + name + ']' : name;
        for (var i in errors[name]) {
            firstErrors[elementName] = errors[name][i];
        }
    }
    return firstErrors;
}

/**
 * @param {jQuery} form
 * @param {Object} options
 * @param {string} firstErrorElementName
 *
 * firstErrorElementName has to be passed from the controller:
 * ErrorHelper::getFirstErrorElementName($form)
 */
function msgValidation(form, options, firstErrorElementName) {
    options = options || {};

    var formName = $(form).attr('name'),
        firstErrorElement = null,
        errorSuffix = '-error';
    if (firstErrorElementName) {
        firstErrorElement = $('#' + formName + "_" + firstErrorElementName)
    } else {
        var errorElementId = $('.help-block:first', form).attr('id');
        if (errorElementId && errorElementId.indexOf(errorSuffix) !== -1) {
            firstErrorElement = $('#' + errorElementId.replace(errorSuffix, ''));
        }
    }

    // positioning to the first error when server side validation fails
    if (firstErrorElement && firstErrorElement.length) {
        $(document).ready(function () {
            $('body').animate({scrollTop: firstErrorElement.offset().top - 25}, 750);
        });
    }

    $(form).on( "submit.validate", function( event ) {
        $(this).data('validator').isSubmitted = true;
    });

    // ignore readonly
    options.ignore = ':hidden, [disabled=disabled], [disabled=true], [readonly=readonly], [readonly=true], .readonly';
    $(form).validate(options);
};

$.validator.addMethod('validExpiryDate', function (value, element, params) {
    var now = new Date();
    var month = $('select[name="' + params[0] + '"]').val(),
        year = $('select[name="' + params[1] + '"]').val();
    if (!this.isSubmitted) {
        if (!month || !year) return true;
    }
    if (!month || !year) return false;
    if (now.getFullYear() < year) return true;
    if (now.getFullYear() == year && now.getMonth() <= month - 1) return true;
}, "Must be a valid Expiration Date.");

$.validator.addMethod('validStartDate', function (value, element, params) {
    $.validator.messages.validStartDate = "default";
    var issueNumber = $(params[2]).val();
    var now = new Date();
    var month = $('select[name="' + params[0] + '"]').val(),
        year = $('select[name="' + params[1] + '"]').val();

    var isRequired = issueNumber ? false : true;
    var isValid = false;
    var isEmpty = true;

    if (month && year) isEmpty = false;
    if (!month || !year) isValid = false;

    if (!isEmpty && now.getFullYear() > year) isValid = true;
    if (!isEmpty && now.getFullYear() == year && now.getMonth() > month - 1) isValid = true;

    if (!this.isSubmitted) {
        return true;
    } else {
        $('select[name="' + params[0] + '"], select[name="' + params[1] + '"]').on(
            'change', function(){
                $(params[2]).valid();
            });
    }
    if (isValid) return true;
    if (!isRequired && isEmpty) return true;
    if (!isValid) {
        $.validator.messages.validStartDate = "Please provide a valid date for Valid From";
    }
    if (isRequired && isEmpty) {
        $.validator.messages.validStartDate = "Please provide Issue number or Valid From";
    }
    return false;
}, "");

$.validator.addMethod('isIssueNumberRequired', function (value, element, params) {
    var month = $('select[name="' + params[0] + '"]').val(),
        year = $('select[name="' + params[1] + '"]').val();

    if (this.isSubmitted) {
        $(element).on('keyup', function(){
            $('select[name="' + params[0] + '"]').valid();
        });
    }

    if (value) return true;
    return (month && year);
}, "Please provide Issue number or Valid From");

$.validator.addMethod('phoneValid', function (value, element, params) {
    var testPattern = new RegExp("^(\\+)?(\\d+)$");
    return testPattern.test(value);
}, "Please provide a valid phone number!");

$.validator.addMethod('cardNumberValid', function (value, element, params) {
    var sanitizedValue = value.replace(/\s+/g, '');
    var testPattern = new RegExp('^[0-9]+$');
    return testPattern.test(sanitizedValue);
}, "Please use numbers only.");

$.validator.addMethod('sagepayCompliantPostcode', function (value, element, params) {
    var testPattern = new RegExp('^[0-9A-Za-z\\s\\-]+$');
    return testPattern.test(value);
}, "Sorry, there is an invalid character in your postcode. Only letters, numbers, spaces and hyphens are allowed.");

$.validator.addMethod('pcaPredict', function (value, element, params) {
    return params.isAddressChosen();
}, "Please provide a valid address");


$.validator.addMethod('ukLicenseNumber', function (value, element, params) {
    if (value.length === 8) {
        return true;
    }
    var testPattern = /^[A-Z9]{5}\d{6}[A-Z9]{2}\d[A-Z]{2}$/;
    return testPattern.test(value.toUpperCase());
}, "The Driving Licence information you have entered is not valid. This could be because of a typo in the licence number. If this is not the case, please ensure that your personal information (eg: full name, date of birth, gender, etc) matches the information on your -Driving Licence.");

$.validator.addMethod('passportLine1', function(value, element, params) {
    var testPattern = /^P[A-Z<][A-Z<]{3}[<A-Z]+?<<[<A-Z]+$/;
    return testPattern.test(value.toUpperCase());
}, "The passport information you have entered is not valid. The most common issue is a typo in the passport numbers. If that is not the case, make sure that your personal information (eg: full name, date of birth, gender, etc) matches the information in your passport.");

$.validator.addMethod('passportLine2', function (value, element, params) {
    var combined = null;
    if ('combine' in params) {
        combined = params.combine();
    } else {
        combined = value;
    }
    var testPattern = /^[0-9A-Z<]{9}[0-9][0-9A-Z<]{3}[0-9]{2}[0-9]{2}[0-9]{2}[0-9][MF<][0-9]{2}[0-9]{2}[0-9]{2}[0-9][<A-Z0-9]{14}[0-9<][0-9]$/;
    return testPattern.test(combined.toUpperCase());
}, "The passport information you have entered is not valid. The most common issue is a typo in the passport numbers. If that is not the case, make sure that your personal information (eg: full name, date of birth, gender, etc) matches the information in your passport.");

$.validator.addMethod('equalLength', function (value, element, params) {
    for (var i in params) {
        if (value.length === params[i]) {
            return true;
        }
    }
    return false;
}, "Value must be equal to length");


$.validator.addMethod('remoteObject', function( value, element, param, method ) {
    if ( this.optional( element ) ) {
        return "dependency-mismatch";
    }

    method = typeof method === "string" && method || "remoteObject";

    var previous = this.previousValue( element, method ),
        validator, data, optionDataString;

    if ( !this.settings.messages[ element.name ] ) {
        this.settings.messages[ element.name ] = {};
    }
    previous.originalMessage = previous.originalMessage || this.settings.messages[ element.name ][ method ];
    this.settings.messages[ element.name ][ method ] = previous.message;

    param = typeof param === "string" && { url: param } || param;
    optionDataString = $.param( $.extend( { data: value }, param.data ) );
    if ( previous.old === optionDataString ) {
        return previous.valid;
    }
    previous.old = optionDataString;
    validator = this;
    this.startRequest( element );
    data = {};
    data[ element.name ] = value;
    $.ajax( $.extend( true, {
        mode: "abort",
        port: "validate" + element.name,
        dataType: "json",
        data: data,
        context: validator.currentForm,
        success: function( response ) {
            var status = null,
                errorMessage = null;
            if ('success' in response) {
                status = response['success'];
            }
            if ('message' in response) {
                errorMessage = response['message'];
            }
            var valid = status === true || status === "true",
                errors, message, submitted;

            validator.settings.messages[ element.name ][ method ] = previous.originalMessage;
            if ( valid ) {
                submitted = validator.formSubmitted;
                validator.resetInternals();
                validator.toHide = validator.errorsFor( element );
                validator.formSubmitted = submitted;
                validator.successList.push( element );
                validator.invalid[ element.name ] = false;
                validator.showErrors();
            } else {
                errors = {};
                message = errorMessage || validator.defaultMessage( element, { method: method, parameters: value } );
                errors[ element.name ] = previous.message = message;
                validator.invalid[ element.name ] = true;
                validator.showErrors( errors );
                if ('position' in response && 'length' in response && response.position >= 0 && response.length > 0) {
                    var endIndex = response['position'] + response['length'];
                    if (endIndex <= element.maxLength) {
                        element.focus();
                        element.setSelectionRange(response['position'], endIndex);
                    }
                }
            }
            previous.valid = valid;
            validator.stopRequest( element, valid );
        }
    }, param ) );
    return "pending";
}, "Please fix this field!");


$.validator.addMethod('dateInTime', function (value, element, params) {
    var dates = params.getValues();
    return window.DateInTime.validDate(dates[0], dates[1], dates[2]);
}, "Sorry, this date doesn't look right. Please double check.");

$.validator.addMethod('customUkPostcode', function (value, element, params) {
    // testing postcodes for getAddress.io API
    const testingPostcodes = ['XX2 00X', 'XX4 00X', 'XX4 01X', 'XX4 04X', 'XX4 29X', 'XX5 00X'];
    for (let i in testingPostcodes) {
        if (
            value.toUpperCase() === testingPostcodes[i] ||
            value.toUpperCase() === testingPostcodes[i].replace(/\s/g, ''))
        {
            return true;
        }
    }

    return $.validator.methods.postcodeUK.call(this, value, element);
}, "Please specify a valid UK postcode");