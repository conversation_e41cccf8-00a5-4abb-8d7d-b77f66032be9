/*! jQuery UI - v1.10.4 - 2014-02-16
* http://jqueryui.com
* Copyright 2014 jQuery Foundation and other contributors; Licensed MIT */

jQuery(function(t){t.datepicker.regional.fi={closeText:"<PERSON><PERSON>",prevText:"&#xAB;Edellinen",nextText:"Seuraava&#xBB;",currentText:"<PERSON>än<PERSON>än",monthNames:["<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON>ik<PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>","<PERSON>uk<PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON>ys<PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>"],monthNamesShort:["<PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON>","Syys","Lok<PERSON>","<PERSON><PERSON>","<PERSON><PERSON>"],dayNamesShort:["<PERSON>","<PERSON>","<PERSON><PERSON>","<PERSON>","<PERSON>","<PERSON><PERSON>","<PERSON>"],dayNames:["<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","Lauantai"],dayNamesMin:["Su","Ma","Ti","Ke","To","Pe","La"],weekHeader:"Vk",dateFormat:"d.m.yy",firstDay:1,isRTL:!1,showMonthAfterYear:!1,yearSuffix:""},t.datepicker.setDefaults(t.datepicker.regional.fi)});