div.datagrid {
}

/* === FILTER === */
div.datagrid div.filter .form-table fieldset {
}
div.datagrid div.filter fieldset legend {
    font-size: 18px;
}
div.datagrid div.filter .form-table label {
    margin: 0 10px 0 0;
    height: 30px;
    line-height: 30px;
    font-size: 12px;
}
div.datagrid div.filter .form-table select {
    font-size: 12px;
    height: 30px;
    max-width: 200px;
}

div.datagrid div.filter .submit_button[type="submit"] {
    text-decoration:none;
    display:inline-block;
    background:#e8593f;
    color:#fff;
    text-transform:uppercase;
    border:1px solid #af2e1c;
    margin:15px 20px 20px 0;
    padding:14px 20px 14px 20px;
    font-weight:bold;
    font-family:Verdana, Geneva, sans-serif;
    font-size:13px;
    text-shadow:0px 1px 0px #853223;
    border-radius: 25px;
    -moz-border-radius: 25px;
    -webkit-border-radius: 25px;
    box-shadow:0px 2px 0px #3da7bb;
    -moz-box-shadow: inset 0 0 0px 1px #f36447;
    -webkit-box-shadow: inset 0 0 0px 1px #f36447;
    box-shadow: inset 0 0 0px 1px #f36447;
    width:auto;
    font-size:12px;
    margin: 0 0 0 0px;
    padding:4px 8px;
    text-transform:uppercase;
}
div.datagrid div.filter .reset_button[type="submit"] {
    background: none;
    border: 0;
    width: 100px;
    font-size: 11px;
    margin-left: 5px;
    text-decoration: underline;
    cursor: pointer;
    box-shadow: none;
}
div.datagrid div.filter .reset_button[type="submit"]:hover {
    text-decoration: none;
}

/* === EXPORT === */


div.datagrid ul.top-export {
    padding: 0;
    margin: 3px 0 0 0;
    text-align: right;
    float: left;
}
div.datagrid ul.top-export li {
    display: inline;
}
div.datagrid ul.top-export li a {
    text-decoration: underline;
    font-weight: normal;
    color: #000;
    padding: 0;
}
div.datagrid ul.top-export li a:hover {
    text-decoration: none;
}


/* === TOP ACTIONS === */


div.datagrid ul.top-action {
    padding: 0;
    margin: 0 0 7px 0;
    list-style: none;
    text-align: right;
    float: right;
}
div.datagrid ul.top-action li {
    display: inline;
}
div.datagrid ul.top-action li a {
    font-size: 14px;
}
div.datagrid ul.top-action li a:hover {

}


/* === GRID === */

div.datagrid table.grid {
    border:1px solid #e4e4e4;
    margin-bottom:20px;
    width:100%;
}
div.datagrid table.grid tr {
}
div.datagrid table.grid tr.even {
}
div.datagrid table.grid tr:hover {
}
div.datagrid table.grid th, table.grid td {
}
div.datagrid table.grid th {
    padding:7px 0px;
    font-size:13px;
    font-family:Arial, Helvetica, sans-serif;
    font-weight: bold;
    text-align:center;
    color:#333333;
    border:1px solid #e4e4e4;
    background-color:#f2f2f2;
}
div.datagrid table.grid th a {
    color: silver;
    text-decoration: none;
}
div.datagrid table.grid th span.order {
    position: relative;
}
div.datagrid table.grid th span.order a {
    font-size: 9px;
}
div.datagrid table.grid th span.order a:hover {
    color: black;
}
div.datagrid table.grid th span.order a.active {
    color: black;
}
div.datagrid table.grid th span.order a.asc {
    position: absolute;
    right: -11px;
    top: -2px;
}
div.datagrid table.grid th span.order a.desc {
    position: absolute;
    right: -11px;
    top: 6px;
}
div.datagrid table.grid td {
    font-family:Arial, Helvetica, sans-serif;
    font-size:14px;
    padding: 15px 25px;
    color:#000;
    border:1px solid #e4e4e4;
    background:#fff;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
}
div.datagrid table.grid td.no-overflow {
    overflow: auto;
    white-space: normal;
}
div.datagrid table.grid td.action {
    text-align: center;
}
div.datagrid table.grid td a {
}
div.datagrid table.grid td img {
    border: 0;
}
div.datagrid table.grid th.right, table.grid td.right {
    text-align: right;
}
div.datagrid table.grid th.left, table.grid td.left {
    text-align: left;
}
div.datagrid table.grid th.center, table.grid td.center {
    text-align: center;
}

div.datagrid table.grid .no-wite-space {
    white-space: normal;
}


/* === PAGINATOR === */

div.datagrid div.paginator p.pager {
}
div.datagrid div.paginator p.pager .htmlFirst,
div.datagrid div.paginator p.pager .html_prev,
div.datagrid div.paginator p.pager .htmlNext,
div.datagrid div.paginator p.pager .htmlLast,
div.datagrid div.paginator p.pager span,
div.datagrid div.paginator p.pager a {
    border: 1px solid #d3d3d3;
    background: #F3F3F3;
    margin: 0 3px 0 0;
    padding: 2px 8px;
    color: silver;
}
div.datagrid div.paginator p.pager a {
    text-decoration: none;
    color: black;
}
div.datagrid div.paginator p.pager span.curr {
    color: white;
    border: 1px solid #D54E21;
    background: #D54E21;
}
div.datagrid div.paginator p.pager span.result_info {
    color: black;
    border: 0;
    background: none;
    font-weight: bold;
}
div.datagrid div.paginator p.pager a.htmlFirst,
div.datagrid div.paginator p.pager a.html_prev,
div.datagrid div.paginator p.pager a.htmlNext,
div.datagrid div.paginator p.pager a.htmlLast {
    color: black;
    padding: 2px 4px;
}
div.datagrid div.paginator p.pager span.htmlFirst,
div.datagrid div.paginator p.pager span.html_prev,
div.datagrid div.paginator p.pager span.htmlNext,
div.datagrid div.paginator p.pager span.htmlLast {
    padding: 2px 4px;
}












