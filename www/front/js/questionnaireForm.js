(function (CMS, $) {

    /**
     * @param options
     * @constructor
     */
    var Toggler = function (options) {
        this.options = $.extend({}, Toggler.DEFAULTS, options);
    }

    Toggler.DEFAULTS = {
        radios: 'input[name="questionnaireForm[ukResident]"]',
        groups: '.group-0, .group-1'
    }


    Toggler.prototype.toggleGroups = function (selector) {

        $(this.options.groups).addClass('hidden');
        var id = $(selector).val();
        $('.group-' + id).removeClass('hidden');

    }

    Toggler.prototype.init = function () {

        var self = this;

        $(document).on('change', this.options.radios, function () {
            self.toggleGroups($(this));
        });

        $(document).on('mouseenter', '[data-toggle="popover"]', function () {
            $(this).popover('show');
        });

        $(document).on('mouseleave', '[data-toggle="popover"]', function () {
            $(this).popover('hide');
        });

    }

    /**
     * @param options
     * @constructor
     */
    var DataHandler = function (options, validator) {
        this.options = $.extend({}, DataHandler.DEFAULTS, options);
        this.validator = validator;
    }

    DataHandler.DEFAULTS = {
        form: '#questionnaire-form',
        button: '#questionnaire-button',
        modal: '.questionnaire-modal',
        ajaxUrl: 'questionnaire.html',
        postUrl: 'package-offer.html',
        submit: '#questionnaireForm_save'
    }

    DataHandler.prototype.postLoad = function (url, data) {

        var self = this;

        var request = $.ajax({
            url: url,
            method: "POST",
            data: data,
            dataType: "json"
        });

        request.success(function (msg) {
            $(self.options.modal).find('.modal-body').html(msg.message);
        });

        request.fail(function (jqXHR) {
            var response = JSON.parse(jqXHR.responseText);
            alert(response.message);
        });

    }

    DataHandler.prototype.init = function () {

        var self = this;

        $(document).on('click', this.options.button, function () {

            $(self.options.modal).find('.modal-body').html('<p class="text-center"><i class="fa fa-spinner fa-spin fa-5x top20 btm20"></i></p>');

            $(self.options.modal).modal();

            $(self.options.modal).find('.modal-body').load(self.options.ajaxUrl);

        });

        $(document).on('click', this.options.submit, function (e) {

            e.preventDefault();

            if (self.validator.validateForm()) {
                self.postLoad(self.options.postUrl, $(self.options.form).serialize());
            } else {
                $.ladda('stopAll');
            }

        });

    }

    /**
     * @param options
     * @constructor
     */
    var Validator = function (options) {
        this.options = $.extend({}, Validator.DEFAULTS, options);
    }

    Validator.DEFAULTS = {
        radios: 'input[name="questionnaireForm[ukResident]"]'
    }

    Validator.prototype.showError = function (selector) {
        selector.closest('div').addClass('has-feedback has-error');
        selector.find('small').removeClass('hidden');
    }

    Validator.prototype.hideError = function (selector) {
        selector.closest('div').removeClass('has-feedback has-error');
        selector.find('small').addClass('hidden');
    }

    Validator.prototype.validateForm = function () {
        var self = this;
        var formValid = true;

        if (!$(this.options.radios).is(":checked")) {

            this.showError($(this.options.radios).closest('div').parent('div'));
            formValid = false;

        } else {

            this.hideError($(this.options.radios).parent('div'));

            var id = $(this.options.radios + ':checked').val();
            var group = '.group-' + id;

            $(group).each(function () {

                if ($("input:radio:checked", this).length == 0) {
                    self.showError($(this));
                    formValid = false;
                } else {
                    self.hideError($(this));
                }

            });

        }

        return formValid;
    }


    CMS.questionnaire = function (options) {

        var toggler = new Toggler(options);
        var dataHandler = new DataHandler(options, new Validator(options));

        toggler.init();
        dataHandler.init();

    }

}(window.CMS = window.CMS || {}, $));