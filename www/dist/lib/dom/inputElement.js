import * as R from 'ramda';
import { getElementByName, getElementsByNames } from './element';
var getInputByName = R.pipe(getElementByName, R.ifElse(R.allPass([R.isNil, function (el) { return !el.hasAttribute(el); }]), function () { return undefined; }, R.identity));
var getInputsByNames = R.pipe(getElementsByNames, 
// R.filter((e: HTMLElement) => e.hasAttribute("value")),
function (r) { return r; });
var getInputValueByName = R.pipe(getInputByName, R.ifElse(R.isNil, function () { return undefined; }, R.prop("value")));
var getInputsValuesByNames = R.pipe(getInputsByNames, R.map(function (el) { return [el.name, el.value]; }));
export { getInputByName, getInputsByNames, getInputValueByName, getInputsValuesByNames };
