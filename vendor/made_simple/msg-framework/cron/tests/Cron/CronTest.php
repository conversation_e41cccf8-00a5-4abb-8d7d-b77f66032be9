<?php

namespace Cron;

use Doctrine\Common\Cache\FilesystemCache;
use TestModule\PhpUnit\TestCase;
use tests\helpers\MockHelper;

/**
 * Generated by PHPUnit_SkeletonGenerator 1.2.0 on 2013-03-26 at 14:37:38.
 */
class CronTest extends TestCase
{

    /**
     * @var Cron
     */
    protected $object;

    /**
     * Sets up the fixture, for example, opens a network connection.
     * This method is called before a test is executed.
     */
    public function setUp(): void
    {
        $temp = sys_get_temp_dir();
        $logger = $notifier = $this->getMockBuilder('Cron\CronLogger')
            ->disableOriginalConstructor()
            ->setConstructorArgs(
                [
                    ['logMessage'],
                    [$temp . '/cron.output']
                ]
            )
            ->getMock();
        $notifier = MockHelper::mock($this, 'Cron\INotifier');
        $this->object = new Cron(new FilesystemCache($temp), $logger, dirname(__FILE__), $notifier);
    }

    /**
     * Tears down the fixture, for example, closes a network connection.
     * This method is called after a test is executed.
     */
    protected function tearDown(): void
    {

    }

    /**
     * @covers Cron\Cron::executeConfig
     */
    public function testExecuteConfig()
    {
         $config = array('Command' => array('TestNamespace1' => array('subscriptionExample1' => '5 sec', 'subscriptionExample2' => '10 sec'), 'TestNamespace2' => array('subscriptionExample2' => '10 sec')));
         $executedCommands = $this->object->executeConfig($config, 'Command');
         $index = 0;
         $this->assertCount(3, $executedCommands);
         $this->assertInstanceOf('Cron\\Command\\TestNamespace1\\SubscriptionExample1', $executedCommands[0]);
         $this->assertInstanceOf('Cron\\Command\\TestNamespace1\\SubscriptionExample2', $executedCommands[1]);
         $this->assertInstanceOf('Cron\\Command\\TestNamespace2\\SubscriptionExample2', $executedCommands[2]);
    }

    /**
     * @covers Cron\Cron::executeCommand
     */
    public function testExecuteCommand()
    {
        $executedCommand = $this->object->executeCommand('TestNamespace1\\SubscriptionExample1', NULL, 'Command');
        $this->assertInstanceOf('Cron\\Command\\TestNamespace1\\SubscriptionExample1', $executedCommand);
    }

}
