<?php

namespace TestModule\Extensions;

use PhpSpec\Extension;
use PhpSpec\ServiceContainer;
use Prophecy\Comparator\FactoryProvider;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Comparator\DateTimeComparator;
use TestModule\Comparators\DateTimeSecondsComparator;

class PhpSpecLoader implements Extension
{
    /**
     * @param ServiceContainer $container
     * @param array $params
     */
    public function load(ServiceContainer $container, array $params)
    {
        $factory = FactoryProvider::getInstance();
        $factory->unregister(new DateTimeComparator());
        $factory->register(new DateTimeSecondsComparator());
        require_once __DIR__ . '/../../../../../../tests/index.php';
    }
}
