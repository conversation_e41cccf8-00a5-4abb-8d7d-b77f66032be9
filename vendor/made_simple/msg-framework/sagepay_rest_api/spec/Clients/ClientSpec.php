<?php

namespace spec\SagePayRestApi\Clients;

use HttpClient\ClientInterface;
use HttpClient\Exceptions\RequestException as HttpClientRequestException;
use HttpClient\Requests\RequestInterface;
use HttpClient\Requests\RequestOptions;
use PhpSpec\ObjectBehavior;
use Psr\Http\Message\ResponseInterface;
use SagePayRestApi\Exceptions\RequestException;

class ClientSpec extends ObjectBehavior
{
    /**
     * @var ClientInterface
     */
    private $httpClient;

    public function let(
        ClientInterface $httpClient
    ) {
        $this->httpClient = $httpClient;
        $this->beConstructedWith($this->httpClient);
    }

    public function it_should_send_request(RequestInterface $request)
    {
        $this->httpClient->sendRequest($request)->shouldBeCalled();
        $this->sendRequest($request);
    }

    public function it_should_throw_exception(RequestInterface $request, ResponseInterface $response, RequestOptions $options)
    {
        $e = new \Exception();

        $request->getUrl()->willReturn('url');
        $request->getBody()->willReturn('body');
        $request->getMethod()->willReturn('method');
        $request->getHeaders()->willReturn('header');
        $request->getOptions()->willReturn($options);

        $this->httpClient->sendRequest($request)->willThrow(new HttpClientRequestException($request->getWrappedObject(), $e, NULL));
        $this->shouldThrow(RequestException::class)->during('sendRequest', ['request' => $request]);
    }
}
