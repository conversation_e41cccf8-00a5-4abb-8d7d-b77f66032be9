<?php

namespace spec\FeatureModule;

use FeatureModule\Context;
use FeatureModule\FeatureManager;
use FeatureModule\Matchers\Identity;
use PhpSpec\ObjectBehavior;
use Prophecy\Argument;
use Utils\Date;
use FeatureModule\FeatureToggle;

class FeatureToggleSpec extends ObjectBehavior
{
    /**
     * @var FeatureManager
     */
    private $featureManager;

    function let(Context $context)
    {
        $this->featureManager = new FeatureManager();
        $this->beConstructedWith($this->featureManager, $context);
    }

    function it_is_initializable()
    {
        $this->shouldHaveType(FeatureToggle::class);
    }

    function it_should_be_enabled_for_date_in_future()
    {
        $featureName = 'feature_flag';

        $date = new Date('+1 day');
        $this->featureManager->addMatcher($featureName, Context::TYPE_ENABLED, new Identity(['to' => $date->format('Y-m-d')]));

        $this->isEnabled($featureName)->shouldBe(TRUE);
    }

    function it_should_be_enabled_for_current_date()
    {
        $featureName = 'feature_flag';

        $date = new Date('today');
        $this->featureManager->addMatcher($featureName, Context::TYPE_ENABLED, new Identity(['to' => $date->format('Y-m-d')]));

        $this->isEnabled($featureName)->shouldBe(TRUE);
    }

    function it_should_be_disabled_for_past_date()
    {
        $featureName = 'feature_flag';

        $date = new Date('-1 day');
        $this->featureManager->addMatcher($featureName, Context::TYPE_ENABLED, new Identity(['to' => $date->format('Y-m-d')]));

        $this->isEnabled($featureName)->shouldBe(FALSE);
    }

    function it_should_be_enabled_within_range()
    {
        $featureName = 'feature_flag';

        $from = new \DateTime('-2 minute');
        $to = new \DateTime('+2 minute');

        $config = $this->createRange($from, $to);

        $this->featureManager->addMatcher($featureName, Context::TYPE_ENABLED, new Identity($config));

        $this->isEnabled($featureName)->shouldBe(true);
    }

    function it_should_be_disabled_within_range()
    {
        $featureName = 'feature_flag';

        $to = new \DateTime('-2 minute');
        $from = new \DateTime('+2 minute');

        $config = $this->createRange($from, $to);

        $this->featureManager->addMatcher($featureName, Context::TYPE_ENABLED, new Identity($config));

        $this->isEnabled($featureName)->shouldBe(false);
    }

    function it_should_be_enabled_before_range()
    {
        $featureName = 'feature_flag';

        $to = new \DateTime('+2 minute');
        $from = new \DateTime('+3 minute');

        $config = $this->createRange($from, $to);

        $this->featureManager->addMatcher($featureName, Context::TYPE_ENABLED, new Identity($config));

        $this->isEnabled($featureName)->shouldBe(true);
    }

    function it_should_be_enabled_after_range()
    {
        $featureName = 'feature_flag';

        $to = new \DateTime('-3 minute');
        $from = new \DateTime('-2 minute');

        $config = $this->createRange($from, $to);

        $this->featureManager->addMatcher($featureName, Context::TYPE_ENABLED, new Identity($config));

        $this->isEnabled($featureName)->shouldBe(true);
    }

    function it_should_be_disabled_before_range()
    {
        $featureName = 'feature_flag';

        $from = new \DateTime('+2 minute');
        $to = new \DateTime('+3 minute');

        $config = $this->createRange($from, $to);

        $this->featureManager->addMatcher($featureName, Context::TYPE_ENABLED, new Identity($config));

        $this->isEnabled($featureName)->shouldBe(false);
    }

    function it_should_be_disabled_after_range()
    {
        $featureName = 'feature_flag';

        $from = new \DateTime('-3 minute');
        $to = new \DateTime('-2 minute');

        $config = $this->createRange($from, $to);

        $this->featureManager->addMatcher($featureName, Context::TYPE_ENABLED, new Identity($config));

        $this->isEnabled($featureName)->shouldBe(false);
    }

    private function createRange(\DateTime $from, \DateTime $to): array
    {
        return [
            'range' => [
                'from' => $from->format('Y-m-d H:i'),
                'to' => $to->format('Y-m-d H:i')
            ]
        ];
    }
}
