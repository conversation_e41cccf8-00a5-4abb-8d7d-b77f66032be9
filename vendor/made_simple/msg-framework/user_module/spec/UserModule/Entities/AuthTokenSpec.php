<?php

namespace spec\UserModule\Entities;

use PhpSpec\ObjectBehavior;
use UserModule\Contracts\ICustomer;
use UserModule\Contracts\IAuthToken;
use UserModule\Entities\AuthToken;
use Utils\Date;

class AuthTokenSpec extends ObjectBehavior
{
    function let(ICustomer $customer)
    {
        $this->beConstructedWith(IAuthToken::TYPE_FORGOTTEN_PASSWORD, $customer, 'token string', new Date("+ 30days"));
    }

    function it_is_initializable()
    {
        $this->shouldHaveType(AuthToken::class);
        $this->shouldHaveType(IAuthToken::class);
    }

    function it_should_test_validity_in_future(ICustomer $customer)
    {
        $this->beConstructedWith(IAuthToken::TYPE_FORGOTTEN_PASSWORD, $customer, 'token string', new Date("+ 30days"));
        $this->isValid()->shouldBe(TRUE);
    }

    function it_should_test_validity_today(ICustomer $customer)
    {
        $this->beConstructedWith(IAuthToken::TYPE_FORGOTTEN_PASSWORD, $customer, 'token string', new Date());
        $this->isValid()->shouldBe(TRUE);
    }

    function it_should_test_validity_in_past(ICustomer $customer)
    {
        $this->beConstructedWith(IAuthToken::TYPE_FORGOTTEN_PASSWORD, $customer, 'token string', new Date("- 1day"));
        $this->isValid()->shouldBe(FALSE);
    }

}
