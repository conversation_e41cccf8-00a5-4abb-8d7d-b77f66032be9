<?php

namespace spec\PaymentModule\Dto;

use PaymentModule\Contracts\IPaymentData;
use PaymentModule\Dto\PaymentDetails;
use PhpSpec\ObjectBehavior;
use Prophecy\Argument;

class PaymentDetailsSpec extends ObjectBehavior
{
    function let()
    {
        $this->beConstructedThrough([PaymentDetails::class, 'createFromArray'], [PaymentDetails::TYPE_SAGE, 1, true, 'EUR', []]);
    }

    function it_is_initializable()
    {
        $this->shouldHaveType('PaymentModule\Dto\PaymentDetails');
        $this->shouldHaveType(IPaymentData::class);
    }

    function it_should_format_phone()
    {
        $this::formatPhone('07234234 234234, 013554545454')->shouldBe('013554545454');
        $this::formatPhone('0723 4234 234234, +448554545454')->shouldBe('07234234234234');
        $this::formatPhone('+4427234234 234234, 013554545454')->shouldBe('+4427234234234234');
        $this::formatPhone('234234,0773554545454')->shouldBe('0773554545454');
        $this::formatPhone('07712335010, 01712335010')->shouldBe('01712335010');
        $this::formatPhone('007712335010, 01712335010')->shouldBe('01712335010');
        $this::formatPhone('00447712335010, 07712335010')->shouldBe('+447712335010');
        $this::formatPhone('07872969517,,01482 629225,07872969517')->shouldBe('01482629225');
        $this::formatPhone('aaaa')->shouldBe(NULL);
    }
}
