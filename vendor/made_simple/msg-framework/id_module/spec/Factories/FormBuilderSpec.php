<?php

namespace spec\IdModule\Factories;

use Id3GlobalApiClient\Entities\Address;
use Id3GlobalApiClient\Entities\PersonalDetails;
use IdModule\Domain\IdEntity;
use IdModule\Factories\FormBuilder;
use PhpSpec\ObjectBehavior;
use Prophecy\Argument;
use RouterModule\Helpers\IFormBuilderHelper;
use Symfony\Component\Form\Form;

class FormBuilderSpec extends ObjectBehavior
{
    function let(IFormBuilderHelper $builderHelper)
    {
        $this->beConstructedWith($builderHelper);
    }

    function it_is_initializable()
    {
        $this->shouldHaveType(FormBuilder::class);
    }
    //
    //function it_should_create_a_form_based_on_entity()
    //{
    //    $entity = IdEntity::fromPerson(new PersonalDetails(), new Address());
    //    $this->createForm($entity)->shouldBeAnInstanceOf(Form::class);
    //}
}
