<?php

namespace FunctionalModule\Transformations;

use ArrayIterator;
use function FunctionalModule\Helpers\Object\apply;
use function FunctionalModule\Helpers\Object\reject;
use TestModule\PhpUnit\TestCase;

class TransformationTest extends TestCase
{
    /**
     * @dataProvider getHasAllValues
     */
    public function testHasAllValues(bool $expected, array $values, iterable $arr)
    {
        $this->assertEquals($expected, hasAllValues($values, $arr));
    }

    /**
     * @dataProvider getHasAnyValue
     */
    public function testHasAnyValue(bool $expected, array $values, iterable $arr)
    {
        $this->assertEquals($expected, hasAnyValue($values, $arr));
    }

    /**
     * @dataProvider getHasValue
     */
    public function testHasValue(bool $expected, $value, iterable $arr)
    {
        $this->assertEquals($expected, hasValue($value, $arr));
    }

    public function testReject()
    {
        $odd = function(int $i) { return $i % 2 === 1; };
        $this->assertEquals([1 => 2, 3 => 4], iterator_to_array(reject($odd, [1, 2, 3, 4, 5])));
    }


    public function getHasValue()
    {
        return [
            'ok' => [true, 'a', ['d', 'c', 'b', 'a']],
            'iterable' => [true, 'b', new ArrayIterator(['d', 'c', 'b', 'a'])],
            'missing b' => [false, 'b', ['d', 'a']],
            'empty' => [false, null, []],
            'has null' => [true, null, [null]],
            'has false' => [true, false, ['a', false]],
        ];
    }

    public function getHasAllValues()
    {
        return [
            'ok' => [true, ['a', 'b', 'c'], ['d', 'c', 'b', 'a']],
            'iterable' => [true, ['b', 'a', 'c'], new ArrayIterator(['d', 'c', 'b', 'a'])],
            'iterable2' => [true, ['a'], new ArrayIterator(['d', 'c', 'b', 'a'])],
            'missing b' => [false, ['a', 'c'], ['d', 'b', 'a']],
            'empty' => [false, [], []],
            'empty2' => [false, [], ['a']],
        ];
    }

    public function getHasAnyValue()
    {
        return [
            'ok' => [true, ['a', 'b', 'c'], ['d', 'c', 'b', 'a']],
            'iterable' => [true, ['b', 'a', 'c'], new ArrayIterator(['d', 'c', 'b', 'a'])],
            'iterable2' => [true, ['a'], new ArrayIterator(['d', 'c', 'b', 'a'])],
            'missing b' => [true, ['a', 'c'], ['d', 'b', 'a']],
            'empty' => [false, [], []],
            'empty2' => [false, [], ['a']],
        ];
    }

    public function testRunEvery()
    {
        $called = [];
        $c = runEvery(2, function() use (&$called) {
            $called[] = TRUE;
        });
        $values = apply($c, range(1, 5));
        $this->assertCount(2, $called);
        $this->assertEquals([1, 2, 3, 4, 5], $values);
    }

    public function testRunOnce()
    {
        $called = [];
        $c = runOnce(function() use (&$called) {
            $called[] = TRUE;
        });
        $values = apply($c, range(1, 5));
        $this->assertCount(1, $called);
        $this->assertEquals([1, 2, 3, 4, 5], $values);
    }

    public function testEmptyToNull()
    {
        $this->assertEquals('a', emptyToNull('a'));
        $this->assertEquals(null, emptyToNull(''));
        $this->assertEquals([], emptyToNull([]));
        $this->assertEquals([''], emptyToNull(['']));
        $this->assertEquals(0.01, emptyToNull(0.01));
        $this->assertEquals(0, emptyToNull(0));
        $this->assertEquals(null, emptyToNull(' '));
        $this->assertEquals(null, emptyToNull('  '));
        $this->assertEquals(' b ', emptyToNull(' b '));
    }
}