<?php

use Factories\Console\CommandFactory;
use RouterModule\Commands\RouterDebugCommand;
use RouterModule\Commands\RouterMatcherCommand;

require_once __DIR__ . DIRECTORY_SEPARATOR . 'cli-config.php';

$commands = array(
    CommandFactory::getDeployCommand(),
    CommandFactory::getCronInfoCommand(),
    CommandFactory::getCronRunCommand(),
    CommandFactory::getSyncNodeCommand(),
    CommandFactory::getGenerateEntityCommand(),
    CommandFactory::getGenerateDataCommand(),
    new RouterDebugCommand($container),
    new RouterMatcherCommand($container),
);

Console\ConsoleRunner::run($helperSet, $container->getParameter('environment'), $commands, $container);
