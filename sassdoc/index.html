<!doctype html><html lang="en"><head><meta charset="utf-8"><title>Cms - v1.0.0</title><link rel="stylesheet" href="assets/css/main.css"><link href="https://fonts.googleapis.com/css?family=Open+Sans:400,500,700" rel="stylesheet" type="text/css"><meta name="viewport" content="width=device-width"><meta content="IE=edge, chrome=1" http-equiv="X-UA-Compatible"><!-- Open Graph tags --><meta property="og:title" content="Cms - SassDoc"><meta property="og:type" content="website"><meta property="og:description" content="<p>cms js modules</p>
"><!-- Thanks to Sass-lang.com for the icons --><link href="assets/images/favicon.png" rel="shortcut icon"></head><body><aside class="sidebar" role="nav"><div class="sidebar__header"><h1 class="sidebar__title"><a href="https://bitbucket.org/made_simple/cms#readme">Cms - v1.0.0</a></h1></div><div class="sidebar__body"><button type="button" class="btn-toggle js-btn-toggle" data-alt="Open all">Close all</button><p class="sidebar__item sidebar__item--heading" data-slug="colors"><a href="#colors">colors</a></p><div><p class="sidebar__item sidebar__item--sub-heading" data-slug="colors-mixin"><a href="#colors-mixin">mixins</a></p><ul class="list-unstyled"><li class="sidebar__item sassdoc__item" data-group="colors" data-name="shades_of_background_color" data-type="mixin"><a href="#colors-mixin-shades_of_background_color">shades_of_background_color</a></li><li class="sidebar__item sassdoc__item" data-group="colors" data-name="color" data-type="mixin"><a href="#colors-mixin-color">color</a></li><li class="sidebar__item sassdoc__item" data-group="colors" data-name="bg-color" data-type="mixin"><a href="#colors-mixin-bg-color">bg-color</a></li><li class="sidebar__item sassdoc__item" data-group="colors" data-name="hover-color" data-type="mixin"><a href="#colors-mixin-hover-color">hover-color</a></li><li class="sidebar__item sassdoc__item" data-group="colors" data-name="hover-bg-color" data-type="mixin"><a href="#colors-mixin-hover-bg-color">hover-bg-color</a></li><li class="sidebar__item sassdoc__item" data-group="colors" data-name="bg-gray" data-type="mixin"><a href="#colors-mixin-bg-gray">bg-gray</a></li><li class="sidebar__item sassdoc__item" data-group="colors" data-name="color-gray" data-type="mixin"><a href="#colors-mixin-color-gray">color-gray</a></li><li class="sidebar__item sassdoc__item" data-group="colors" data-name="shades-of-border-color" data-type="mixin"><a href="#colors-mixin-shades-of-border-color">shades-of-border-color</a></li></ul><p class="sidebar__item sidebar__item--sub-heading" data-slug="colors-css"><a href="#colors-css">css</a></p><ul class="list-unstyled"><li class="sidebar__item sassdoc__item" data-group="colors" data-name="._msg-focus\:outline-thin-dotted" data-type="css"><a href="#colors-css-._msg-focus\:outline-thin-dotted">._msg-focus:outline-thin-dotted</a></li><li class="sidebar__item sassdoc__item" data-group="colors" data-name="._msg-focus\:outline-primary" data-type="css"><a href="#colors-css-._msg-focus\:outline-primary">._msg-focus:outline-primary</a></li><li class="sidebar__item sassdoc__item" data-group="colors" data-name="._msg-focus\:outline-dark" data-type="css"><a href="#colors-css-._msg-focus\:outline-dark">._msg-focus:outline-dark</a></li><li class="sidebar__item sassdoc__item" data-group="colors" data-name="._msg-border-red" data-type="css"><a href="#colors-css-._msg-border-red">._msg-border-red</a></li></ul><p class="sidebar__item sidebar__item--sub-heading" data-slug="colors-variable"><a href="#colors-variable">variables</a></p><ul class="list-unstyled"><li class="sidebar__item sassdoc__item" data-group="colors" data-name="dark" data-type="variable"><a href="#colors-variable-dark">dark</a></li><li class="sidebar__item sassdoc__item" data-group="colors" data-name="light" data-type="variable"><a href="#colors-variable-light">light</a></li><li class="sidebar__item sassdoc__item" data-group="colors" data-name="primary" data-type="variable"><a href="#colors-variable-primary">primary</a></li><li class="sidebar__item sassdoc__item" data-group="colors" data-name="secondary_1" data-type="variable"><a href="#colors-variable-secondary_1">secondary_1</a></li><li class="sidebar__item sassdoc__item" data-group="colors" data-name="secondary_2" data-type="variable"><a href="#colors-variable-secondary_2">secondary_2</a></li><li class="sidebar__item sassdoc__item" data-group="colors" data-name="secondary_3" data-type="variable"><a href="#colors-variable-secondary_3">secondary_3</a></li><li class="sidebar__item sassdoc__item" data-group="colors" data-name="secondary_4" data-type="variable"><a href="#colors-variable-secondary_4">secondary_4</a></li><li class="sidebar__item sassdoc__item" data-group="colors" data-name="secondary_5" data-type="variable"><a href="#colors-variable-secondary_5">secondary_5</a></li><li class="sidebar__item sassdoc__item" data-group="colors" data-name="secondary_6" data-type="variable"><a href="#colors-variable-secondary_6">secondary_6</a></li><li class="sidebar__item sassdoc__item" data-group="colors" data-name="success" data-type="variable"><a href="#colors-variable-success">success</a></li><li class="sidebar__item sassdoc__item" data-group="colors" data-name="danger" data-type="variable"><a href="#colors-variable-danger">danger</a></li><li class="sidebar__item sassdoc__item" data-group="colors" data-name="danger-darker" data-type="variable"><a href="#colors-variable-danger-darker">danger-darker</a></li><li class="sidebar__item sassdoc__item" data-group="colors" data-name="palette" data-type="variable"><a href="#colors-variable-palette">palette</a></li></ul></div><p class="sidebar__item sidebar__item--heading" data-slug="components"><a href="#components">components</a></p><div><p class="sidebar__item sidebar__item--sub-heading" data-slug="components-css"><a href="#components-css">css</a></p><ul class="list-unstyled"><li class="sidebar__item sassdoc__item" data-group="components" data-name="#megamenu" data-type="css"><a href="#components-css-#megamenu">#megamenu</a></li><li class="sidebar__item sassdoc__item" data-group="components" data-name="#mobile-menu" data-type="css"><a href="#components-css-#mobile-menu">#mobile-menu</a></li><li class="sidebar__item sassdoc__item" data-group="components" data-name="#siteHead" data-type="css"><a href="#components-css-#siteHead">#siteHead</a></li><li class="sidebar__item sassdoc__item" data-group="components" data-name="#in-window-content" data-type="css"><a href="#components-css-#in-window-content">#in-window-content</a></li></ul></div><p class="sidebar__item sidebar__item--heading" data-slug="global"><a href="#global">global</a></p><div><p class="sidebar__item sidebar__item--sub-heading" data-slug="global-css"><a href="#global-css">css</a></p><ul class="list-unstyled"><li class="sidebar__item sassdoc__item" data-group="global" data-name="body" data-type="css"><a href="#global-css-body">body</a></li></ul></div><p class="sidebar__item sidebar__item--heading" data-slug="layout"><a href="#layout">layout</a></p><div><p class="sidebar__item sidebar__item--sub-heading" data-slug="layout-mixin"><a href="#layout-mixin">mixins</a></p><ul class="list-unstyled"><li class="sidebar__item sassdoc__item" data-group="layout" data-name="media-breakpoint-down" data-type="mixin"><a href="#layout-mixin-media-breakpoint-down">media-breakpoint-down</a></li><li class="sidebar__item sassdoc__item" data-group="layout" data-name="media-breakpoint-up" data-type="mixin"><a href="#layout-mixin-media-breakpoint-up">media-breakpoint-up</a></li><li class="sidebar__item sassdoc__item" data-group="layout" data-name="hide" data-type="mixin"><a href="#layout-mixin-hide">hide</a></li><li class="sidebar__item sassdoc__item" data-group="layout" data-name="hide-down" data-type="mixin"><a href="#layout-mixin-hide-down">hide-down</a></li><li class="sidebar__item sassdoc__item" data-group="layout" data-name="hide-up" data-type="mixin"><a href="#layout-mixin-hide-up">hide-up</a></li></ul></div><p class="sidebar__item sidebar__item--heading" data-slug="typography"><a href="#typography">typography</a></p><div><p class="sidebar__item sidebar__item--sub-heading" data-slug="typography-mixin"><a href="#typography-mixin">mixins</a></p><ul class="list-unstyled"><li class="sidebar__item sassdoc__item" data-group="typography" data-name="font-weight" data-type="mixin"><a href="#typography-mixin-font-weight">font-weight</a></li><li class="sidebar__item sassdoc__item" data-group="typography" data-name="font-size-em" data-type="mixin"><a href="#typography-mixin-font-size-em">font-size-em</a></li><li class="sidebar__item sassdoc__item" data-group="typography" data-name="line-height-em" data-type="mixin"><a href="#typography-mixin-line-height-em">line-height-em</a></li></ul></div><p class="sidebar__item sidebar__item--heading" data-slug="utils"><a href="#utils">utils</a></p><div><p class="sidebar__item sidebar__item--sub-heading" data-slug="utils-css"><a href="#utils-css">css</a></p><ul class="list-unstyled"><li class="sidebar__item sassdoc__item" data-group="utils" data-name="._msg-ul-reset" data-type="css"><a href="#utils-css-._msg-ul-reset">._msg-ul-reset</a></li><li class="sidebar__item sassdoc__item" data-group="utils" data-name="._msg-a-reset" data-type="css"><a href="#utils-css-._msg-a-reset">._msg-a-reset</a></li></ul></div></div></aside><article class="main" role="main"><header class="header" role="banner"><div class="container"><div class="sassdoc__searchbar searchbar"><label for="js-search-input" class="visually-hidden">Search</label><div class="searchbar__form" id="js-search"><input name="search" type="search" class="searchbar__field" autocomplete="off" autofocus id="js-search-input" placeholder="Search"><ul class="searchbar__suggestions" id="js-search-suggestions"></ul></div></div></div></header><section class="main__section"><h1 class="main__heading" id="colors"><div class="container">colors</div></h1><section class="main__sub-section" id="colors-mixin"><h2 class="main__heading--secondary"><div class="container">mixins</div></h2><section class="main__item container item" id="colors-mixin-shades_of_background_color"><h3 class="item__heading"><a class="item__name" href="#mixin-shades_of_background_color">shades_of_background_color</a></h3><div class="item__code-wrapper"><pre class="item__code item__code--togglable language-scss" data-current-state="collapsed" data-expanded="@mixin shades_of_background_color() { 
  @each $color, $value in $theme-colors {
    @for $i from 1 through 9 {
      ._msg-bg-#{$color}-lighter-#{$i} {
        background-color: lighten($value, $i * 10) !important;
      }
      ._msg-bg-#{$color}-darker-#{$i} {
        background-color: darken($value, $i * 10) !important;
      }
    }
  }
  @each $color, $value in $palette {
    @for $i from 1 through 9 {
      ._msg-bg-#{$color}-lighter-#{$i} {
        background-color: lighten($value, $i * 10) !important;
      }
      ._msg-bg-#{$color}-darker-#{$i} {
        background-color: darken($value, $i * 10) !important;
      }
    }
  }
 }" data-collapsed="@mixin shades_of_background_color() { ... }"><code>@mixin shades_of_background_color() { ... }</code></pre></div><h3 class="item__sub-heading">Description</h3><div class="item__description"><p>generates shades of background color utility classes</p></div><h3 class="item__sub-heading">Parameters</h3><p>None.</p><h3 class="item__sub-heading">Example</h3><div class="item__example example"><pre class="example__code language-html"><code>&lt;div class=&quot;_msg-bg-primary-lighter-1&quot;&gt;...&lt;/div&gt;</code></pre></div><h3 class="item__sub-heading">Output</h3><div class="item__description"><p>_msg-bg-#{$color}-{lighter|darker}</p></div><h3 class="item__sub-heading">Requires</h3><ul class="list-unstyled"><li class="item__description item__description--inline"><span class="item__cross-type">[variable]</span> <a href="#colors-variable-palette"><code>palette</code></a></li></ul></section><section class="main__item container item" id="colors-mixin-color"><h3 class="item__heading"><a class="item__name" href="#mixin-color">color</a></h3><div class="item__code-wrapper"><pre class="item__code item__code--togglable language-scss" data-current-state="collapsed" data-expanded="@mixin color() { 
  @each $color, $value in $palette {
    ._msg-color-#{$color} {
      color: $value !important;
    }
  }
 }" data-collapsed="@mixin color() { ... }"><code>@mixin color() { ... }</code></pre></div><h3 class="item__sub-heading">Description</h3><div class="item__description"><p>generates color classes from the custom color variables</p></div><h3 class="item__sub-heading">Parameters</h3><p>None.</p><h3 class="item__sub-heading">Example</h3><div class="item__example example"><pre class="example__code language-html"><code>&lt;div class=&quot;_msg-color-orange&quot;&gt;...&lt;/div&gt;</code></pre></div><h3 class="item__sub-heading">Output</h3><div class="item__description"><p>_msg-color-{color}</p></div><h3 class="item__sub-heading">Requires</h3><ul class="list-unstyled"><li class="item__description item__description--inline"><span class="item__cross-type">[variable]</span> <a href="#colors-variable-palette"><code>palette</code></a></li></ul></section><section class="main__item container item" id="colors-mixin-bg-color"><h3 class="item__heading"><a class="item__name" href="#mixin-bg-color">bg-color</a></h3><div class="item__code-wrapper"><pre class="item__code item__code--togglable language-scss" data-current-state="collapsed" data-expanded="@mixin bg-color() { 
  @each $color, $value in $palette {
    ._msg-bg-#{$color} {
      background-color: $value !important;
    }
  }
 }" data-collapsed="@mixin bg-color() { ... }"><code>@mixin bg-color() { ... }</code></pre></div><h3 class="item__sub-heading">Description</h3><div class="item__description"><p>generates background color classes from the custom color variables</p></div><h3 class="item__sub-heading">Parameters</h3><p>None.</p><h3 class="item__sub-heading">Example</h3><div class="item__example example"><pre class="example__code language-html"><code>&lt;div class=&quot;_msg-bg-orange&quot;&gt;...&lt;/div&gt;</code></pre></div><h3 class="item__sub-heading">Output</h3><div class="item__description"><p>_msg-bg-{color}</p></div><h3 class="item__sub-heading">Requires</h3><ul class="list-unstyled"><li class="item__description item__description--inline"><span class="item__cross-type">[variable]</span> <a href="#colors-variable-palette"><code>palette</code></a></li></ul></section><section class="main__item container item" id="colors-mixin-hover-color"><h3 class="item__heading"><a class="item__name" href="#mixin-hover-color">hover-color</a></h3><div class="item__code-wrapper"><pre class="item__code item__code--togglable language-scss" data-current-state="collapsed" data-expanded="@mixin hover-color() { 
  @each $color, $value in $theme-colors {
    ._msg-hover\:color-#{$color} {
      &amp;:hover {
        color: $value !important;
      }
    }
  }
  @each $color, $value in $palette {
    ._msg-hover\:color-#{$color} {
      &amp;:hover {
        color: $value !important;
      }
    }
  }
 }" data-collapsed="@mixin hover-color() { ... }"><code>@mixin hover-color() { ... }</code></pre></div><h3 class="item__sub-heading">Description</h3><div class="item__description"><p>generates hover color classes from the custom color variables</p></div><h3 class="item__sub-heading">Parameters</h3><p>None.</p><h3 class="item__sub-heading">Example</h3><div class="item__example example"><pre class="example__code language-html"><code>&lt;div class=&quot;_msg-hover:color-orange&quot;&gt;...&lt;/div&gt;</code></pre></div><h3 class="item__sub-heading">Output</h3><div class="item__description"><p>_msg-color-{color}</p></div><h3 class="item__sub-heading">Requires</h3><ul class="list-unstyled"><li class="item__description item__description--inline"><span class="item__cross-type">[variable]</span> <a href="#colors-variable-palette"><code>palette</code></a></li></ul></section><section class="main__item container item" id="colors-mixin-hover-bg-color"><h3 class="item__heading"><a class="item__name" href="#mixin-hover-bg-color">hover-bg-color</a></h3><div class="item__code-wrapper"><pre class="item__code item__code--togglable language-scss" data-current-state="collapsed" data-expanded="@mixin hover-bg-color() { 
  @each $color, $value in $theme-colors {
    ._msg-hover\:bg-#{$color} {
      &amp;:hover {
        background-color: $value !important;
      }
    }
  }
 }" data-collapsed="@mixin hover-bg-color() { ... }"><code>@mixin hover-bg-color() { ... }</code></pre></div><h3 class="item__sub-heading">Description</h3><div class="item__description"><p>generates hover background color classes from the custom color variables</p></div><h3 class="item__sub-heading">Parameters</h3><p>None.</p><h3 class="item__sub-heading">Example</h3><div class="item__example example"><pre class="example__code language-html"><code>&lt;div class=&quot;_msg-hover:bg-orange&quot;&gt;...&lt;/div&gt;</code></pre></div><h3 class="item__sub-heading">Output</h3><div class="item__description"><p>_msg-hover:bg-{color}</p></div></section><section class="main__item container item" id="colors-mixin-bg-gray"><h3 class="item__heading"><a class="item__name" href="#mixin-bg-gray">bg-gray</a></h3><div class="item__code-wrapper"><pre class="item__code item__code--togglable language-scss" data-current-state="collapsed" data-expanded="@mixin bg-gray() { 
  @for $i from 1 through 9 {
    ._msg-bg-gray-#{$i * 100} {
      background-color: gray(&quot;#{$i * 100}&quot;);
    }
  }
  @for $i from 1 through 9 {
    ._msg-hover\:bg-gray-#{$i * 100} {
      &amp;:hover {
        background-color: gray(&quot;#{$i * 100}&quot;);
      }
    }
  }
 }" data-collapsed="@mixin bg-gray() { ... }"><code>@mixin bg-gray() { ... }</code></pre></div><h3 class="item__sub-heading">Description</h3><div class="item__description"><p>generates grayscale of background colors plus hover</p></div><h3 class="item__sub-heading">Parameters</h3><p>None.</p><h3 class="item__sub-heading">Example</h3><div class="item__example example"><pre class="example__code language-html"><code>&lt;div class=&quot;_msg-hover:bg-gray-2&quot;&gt;...&lt;/div&gt;</code></pre></div><h3 class="item__sub-heading">Output</h3><div class="item__description"><p>_msg-bg-gray-{1 - 9}, _msg-hover:bg-gray-{1 - 9}</p></div></section><section class="main__item container item" id="colors-mixin-color-gray"><h3 class="item__heading"><a class="item__name" href="#mixin-color-gray">color-gray</a></h3><div class="item__code-wrapper"><pre class="item__code item__code--togglable language-scss" data-current-state="collapsed" data-expanded="@mixin color-gray() { 
  @for $i from 1 through 9 {
    ._msg-color-gray-#{$i * 100} {
      color: gray(&quot;#{$i * 100}&quot;);
    }
  }
  @for $i from 1 through 9 {
    ._msg-hover\:color-gray-#{$i * 100} {
      &amp;:hover {
        color: gray(&quot;#{$i * 100}&quot;);
      }
    }
  }
 }" data-collapsed="@mixin color-gray() { ... }"><code>@mixin color-gray() { ... }</code></pre></div><h3 class="item__sub-heading">Description</h3><div class="item__description"><p>generates grayscale of colors plus hover</p></div><h3 class="item__sub-heading">Parameters</h3><p>None.</p><h3 class="item__sub-heading">Example</h3><div class="item__example example"><pre class="example__code language-html"><code>&lt;div class=&quot;_msg-hover:color-gray-2&quot;&gt;...&lt;/div&gt;</code></pre></div><h3 class="item__sub-heading">Output</h3><div class="item__description"><p>_msg-color-gray-{1 - 9}, _msg-hover:color-gray-{1 - 9}</p></div></section><section class="main__item container item" id="colors-mixin-shades-of-border-color"><h3 class="item__heading"><a class="item__name" href="#mixin-shades-of-border-color">shades-of-border-color</a></h3><div class="item__code-wrapper"><pre class="item__code item__code--togglable language-scss" data-current-state="collapsed" data-expanded="@mixin shades-of-border-color() { 
  @for $i from 1 through 9 {
    @each $color, $value in $theme-colors {
      @each $side in (&quot;top&quot;, &quot;right&quot;, &quot;bottom&quot;, &quot;left&quot;) {
        ._msg-border-#{$side}-#{$color}-lighter-#{$i} {
          border-#{$side}: 1px solid lighten($value, $i * 10) !important;
        }

        ._msg-border-#{$color}-darker-#{$i} {
          border-#{$side}: 1px solid darken($value, $i * 10) !important;
        }
      }
    }
  }
 }" data-collapsed="@mixin shades-of-border-color() { ... }"><code>@mixin shades-of-border-color() { ... }</code></pre></div><h3 class="item__sub-heading">Description</h3><div class="item__description"><p>generates grayscale of border colors plus hover</p></div><h3 class="item__sub-heading">Parameters</h3><p>None.</p><h3 class="item__sub-heading">Example</h3><div class="item__example example"><pre class="example__code language-html"><code>&lt;div class=&quot;_msg-hover:border-gray-2&quot;&gt;...&lt;/div&gt;</code></pre></div><h3 class="item__sub-heading">Output</h3><div class="item__description"><p>_msg-border-gray-{1 - 9}, _msg-hover:border-gray-{1 - 9}</p></div></section></section><section class="main__sub-section" id="colors-css"><h2 class="main__heading--secondary"><div class="container">css</div></h2><section class="main__item container item" id="colors-css-._msg-focus\:outline-thin-dotted"><h3 class="item__heading"><a class="item__name" href="#css-._msg-focus\:outline-thin-dotted">._msg-focus:outline-thin-dotted</a></h3><div class="item__code-wrapper"><pre class="item__code item__code--togglable language-scss" data-current-state="collapsed" data-expanded="<code>._msg-focus\:outline-thin-dotted {
  &amp;:focus {
    outline: thin dotted;
  }
}</code>" data-collapsed="._msg-focus\:outline-thin-dotted { ... }"><code>._msg-focus\:outline-thin-dotted { ... }</code></pre></div><h3 class="item__sub-heading">Description</h3><div class="item__description"><p>generates grayscale of border colors plus hover</p></div></section><section class="main__item container item" id="colors-css-._msg-focus\:outline-primary"><h3 class="item__heading"><a class="item__name" href="#css-._msg-focus\:outline-primary">._msg-focus:outline-primary</a></h3><div class="item__code-wrapper"><pre class="item__code item__code--togglable language-scss" data-current-state="collapsed" data-expanded="<code>._msg-focus\:outline-primary {
  &amp;:focus {
    outline-color: $primary;
  }
}</code>" data-collapsed="._msg-focus\:outline-primary { ... }"><code>._msg-focus\:outline-primary { ... }</code></pre></div><h3 class="item__sub-heading">Description</h3><div class="item__description"><p>outlined focus with primary color</p></div></section><section class="main__item container item" id="colors-css-._msg-focus\:outline-dark"><h3 class="item__heading"><a class="item__name" href="#css-._msg-focus\:outline-dark">._msg-focus:outline-dark</a></h3><div class="item__code-wrapper"><pre class="item__code item__code--togglable language-scss" data-current-state="collapsed" data-expanded="<code>._msg-focus\:outline-dark {
  &amp;:focus {
    outline-color: $dark;
  }
}</code>" data-collapsed="._msg-focus\:outline-dark { ... }"><code>._msg-focus\:outline-dark { ... }</code></pre></div><h3 class="item__sub-heading">Description</h3><div class="item__description"><p>outlined focus with dark color</p></div></section><section class="main__item container item" id="colors-css-._msg-border-red"><h3 class="item__heading"><a class="item__name" href="#css-._msg-border-red">._msg-border-red</a></h3><div class="item__code-wrapper"><pre class="item__code item__code--togglable language-scss" data-current-state="collapsed" data-expanded="<code>._msg-border-red {
  border: 1px solid red;
}</code>" data-collapsed="._msg-border-red { ... }"><code>._msg-border-red { ... }</code></pre></div><h3 class="item__sub-heading">Description</h3><div class="item__description"><p>adds red border</p></div></section></section><section class="main__sub-section" id="colors-variable"><h2 class="main__heading--secondary"><div class="container">variables</div></h2><section class="main__item container item" id="colors-variable-dark"><h3 class="item__heading"><a class="item__name" href="#variable-dark">dark</a></h3><div class="item__code-wrapper"><pre class="item__code language-scss"><code>$dark: #333333;</code></pre><span class="color-preview--block" style="background: #333333;"></span></div><h3 class="item__sub-heading">Type</h3><p><code>Color</code></p></section><section class="main__item container item" id="colors-variable-light"><h3 class="item__heading"><a class="item__name" href="#variable-light">light</a></h3><div class="item__code-wrapper"><pre class="item__code language-scss"><code>$light: #eae4e2;</code></pre><span class="color-preview--block" style="background: #eae4e2;"></span></div><h3 class="item__sub-heading">Type</h3><p><code>Color</code></p></section><section class="main__item container item" id="colors-variable-primary"><h3 class="item__heading"><a class="item__name" href="#variable-primary">primary</a></h3><div class="item__code-wrapper"><pre class="item__code language-scss"><code>$primary: #009FDA;</code></pre><span class="color-preview--block" style="background: #009FDA;"></span></div><h3 class="item__sub-heading">Type</h3><p><code>Color</code></p></section><section class="main__item container item" id="colors-variable-secondary_1"><h3 class="item__heading"><a class="item__name" href="#variable-secondary_1">secondary_1</a></h3><div class="item__code-wrapper"><pre class="item__code language-scss"><code>$secondary_1: #88C444;</code></pre><span class="color-preview--block" style="background: #88C444;"></span></div><h3 class="item__sub-heading">Type</h3><p><code>Color</code></p></section><section class="main__item container item" id="colors-variable-secondary_2"><h3 class="item__heading"><a class="item__name" href="#variable-secondary_2">secondary_2</a></h3><div class="item__code-wrapper"><pre class="item__code language-scss"><code>$secondary_2: #EEAF30;</code></pre><span class="color-preview--block" style="background: #EEAF30;"></span></div><h3 class="item__sub-heading">Type</h3><p><code>Color</code></p></section><section class="main__item container item" id="colors-variable-secondary_3"><h3 class="item__heading"><a class="item__name" href="#variable-secondary_3">secondary_3</a></h3><div class="item__code-wrapper"><pre class="item__code language-scss"><code>$secondary_3: #FF7900;</code></pre><span class="color-preview--block" style="background: #FF7900;"></span></div><h3 class="item__sub-heading">Type</h3><p><code>Color</code></p></section><section class="main__item container item" id="colors-variable-secondary_4"><h3 class="item__heading"><a class="item__name" href="#variable-secondary_4">secondary_4</a></h3><div class="item__code-wrapper"><pre class="item__code language-scss"><code>$secondary_4: #00204E;</code></pre><span class="color-preview--block" style="background: #00204E;"></span></div><h3 class="item__sub-heading">Type</h3><p><code>Color</code></p></section><section class="main__item container item" id="colors-variable-secondary_5"><h3 class="item__heading"><a class="item__name" href="#variable-secondary_5">secondary_5</a></h3><div class="item__code-wrapper"><pre class="item__code language-scss"><code>$secondary_5: #44697D;</code></pre><span class="color-preview--block" style="background: #44697D;"></span></div><h3 class="item__sub-heading">Type</h3><p><code>Color</code></p></section><section class="main__item container item" id="colors-variable-secondary_6"><h3 class="item__heading"><a class="item__name" href="#variable-secondary_6">secondary_6</a></h3><div class="item__code-wrapper"><pre class="item__code language-scss"><code>$secondary_6: #0F4DBC;</code></pre><span class="color-preview--block" style="background: #0F4DBC;"></span></div><h3 class="item__sub-heading">Type</h3><p><code>Color</code></p></section><section class="main__item container item" id="colors-variable-success"><h3 class="item__heading"><a class="item__name" href="#variable-success">success</a></h3><div class="item__code-wrapper"><pre class="item__code language-scss"><code>$success: $secondary_1;</code></pre><span class="color-preview--block" style="background: #88C444;"></span></div><h3 class="item__sub-heading">Type</h3><p><code>Color</code></p></section><section class="main__item container item" id="colors-variable-danger"><h3 class="item__heading"><a class="item__name" href="#variable-danger">danger</a></h3><div class="item__code-wrapper"><pre class="item__code language-scss"><code>$danger: #BF0000;</code></pre><span class="color-preview--block" style="background: #BF0000;"></span></div><h3 class="item__sub-heading">Type</h3><p><code>Color</code></p></section><section class="main__item container item" id="colors-variable-danger-darker"><h3 class="item__heading"><a class="item__name" href="#variable-danger-darker">danger-darker</a></h3><div class="item__code-wrapper"><pre class="item__code language-scss"><code>$danger-darker: #AC0000;</code></pre><span class="color-preview--block" style="background: #AC0000;"></span></div><h3 class="item__sub-heading">Type</h3><p><code>Color</code></p></section><section class="main__item container item" id="colors-variable-palette"><h3 class="item__heading"><a class="item__name" href="#variable-palette">palette</a></h3><div class="item__code-wrapper"><pre class="item__code language-scss"><code>$palette: (
        &quot;primary&quot;: $primary,
        &quot;sedondary_1&quot;: $secondary_1,
        &quot;sedondary_2&quot;: $secondary_2,
        &quot;sedondary_3&quot;: $secondary_3,
        &quot;sedondary_4&quot;: $secondary_4,
        &quot;sedondary_5&quot;: $secondary_5,
        &quot;sedondary_6&quot;: $secondary_6,
        &quot;success&quot;: $success,
        &quot;danger&quot;: $danger,
        &quot;danger-darker&quot;: $danger-darker,
);</code></pre></div><h3 class="item__sub-heading">Type</h3><p><code>Color</code></p><h3 class="item__sub-heading">Used by</h3><ul class="list-unstyled"><li><span class="item__cross-type">[mixin]</span> <a href="#colors-mixin-shades_of_background_color"><code>shades_of_background_color</code></a></li><li><span class="item__cross-type">[mixin]</span> <a href="#colors-mixin-color"><code>color</code></a></li><li><span class="item__cross-type">[mixin]</span> <a href="#colors-mixin-bg-color"><code>bg-color</code></a></li><li><span class="item__cross-type">[mixin]</span> <a href="#colors-mixin-hover-color"><code>hover-color</code></a></li></ul></section></section></section><section class="main__section"><h1 class="main__heading" id="components"><div class="container">components</div></h1><section class="main__sub-section" id="components-css"><h2 class="main__heading--secondary"><div class="container">css</div></h2><section class="main__item container item" id="components-css-#megamenu"><h3 class="item__heading"><a class="item__name" href="#css-#megamenu">#megamenu</a></h3><div class="item__code-wrapper"><pre class="item__code item__code--togglable language-scss" data-current-state="collapsed" data-expanded="<code>#megamenu {
  font-family: &#39;Foundry Sterling W01&#39;;

  @include media-breakpoint-down(lg) {
    display: none !important;
  }

  h2 {
    font-family: inherit;
    font-size: 15px;
    font-weight: 500;
    margin: 0px;
    color: #009fda;
  }

  .level-0 {
    display: flex;
    .sub-level {
      border-top: 3px solid $primary;
      background-color: lighten($light, 6);
      position: absolute;
      visibility: hidden;
      box-sizing: border-box;
      &amp;:hover {
        z-index: 1000;
        visibility: visible;
      }
    }

    .main-level-li {
      &amp;.right {
        align-self: end;
      }

      text-align: center;
      .main-li-a {
        .label {
          display: flex;
          flex-direction: column;
          //padding: 0 15px;
          height: 1em;
          line-height: 1em;
        }

        line-height: 1.6em;

        .main-li-a-caret {
          line-height: 0.5em;
          visibility: hidden;
        }
      }

      .basket {
        height: 1em;
        line-height: 1em;
        padding-bottom: 0.5em;
        box-sizing: border-box;
      }

      &amp;:hover {
        color: $primary;
        .main-li-a {
          color: $primary;
        }

        .sub-level {
          visibility: visible;
        }
        .main-li-a-caret {
          visibility: visible;
        }
      }
    }

    .sub-level {
      .sub-level-li {
        a {
          text-decoration: none;
          margin: 0;
          font-size: 15px;
        }
      }
    }
  }

  ul {
    list-style: none;
    margin: 0;
    padding: 0;
    min-width: 250px;
    li {
      a {
        text-decoration: none;
        display: block;
      }
    }
  }
}</code>" data-collapsed="#megamenu { ... }"><code>#megamenu { ... }</code></pre></div><h3 class="item__sub-heading">TODO's</h3><ul class="list-unstyled"><li><p>refactor to more generic approach with Bootstrap and custom generic css classes/mixins</p></li></ul></section><section class="main__item container item" id="components-css-#mobile-menu"><h3 class="item__heading"><a class="item__name" href="#css-#mobile-menu">#mobile-menu</a></h3><div class="item__code-wrapper"><pre class="item__code item__code--togglable language-scss" data-current-state="collapsed" data-expanded="<code>#mobile-menu {
  @include media-breakpoint-up(lg) {
    display: none;
  }

  a {
    font-size: 18px;
  }

  .top-item {
    box-sizing: border-box;
    border-bottom: 1px solid #4d4d4d;
  }
}</code>" data-collapsed="#mobile-menu { ... }"><code>#mobile-menu { ... }</code></pre></div><h3 class="item__sub-heading">TODO's</h3><ul class="list-unstyled"><li><p>refactor to more generic approach with Bootstrap and custom generic css classes/mixins</p></li></ul></section><section class="main__item container item" id="components-css-#siteHead"><h3 class="item__heading"><a class="item__name" href="#css-#siteHead">#siteHead</a></h3><div class="item__code-wrapper"><pre class="item__code item__code--togglable language-scss" data-current-state="collapsed" data-expanded="<code>#siteHead {
  a {
    letter-spacing: -0.025em;
    padding: 0;
    line-height: 1.1;
    text-decoration: none;
    font-size: calc(2vw + 2vh + 2vmin);
    font-weight: 300;

    @include media-breakpoint-up(md) {
      font-size: 44px;
      font-weight: 100;
    }
  }

  img {
    width: 150px;
    @include media-breakpoint-down(md) {
      width: 55%;
    }
  }
}</code>" data-collapsed="#siteHead { ... }"><code>#siteHead { ... }</code></pre></div><h3 class="item__sub-heading">TODO's</h3><ul class="list-unstyled"><li><p>refactor to more generic approach with Bootstrap and custom generic css classes/mixins</p></li></ul></section><section class="main__item container item" id="components-css-#in-window-content"><h3 class="item__heading"><a class="item__name" href="#css-#in-window-content">#in-window-content</a></h3><div class="item__code-wrapper"><pre class="item__code item__code--togglable language-scss" data-current-state="collapsed" data-expanded="<code>#in-window-content {
  box-sizing: border-box;
  background-color: white;
  min-height: 85vh;
  position: relative;
  display: flex;
  justify-content: center;

  .in-window-message-container {
    .alert{
      padding: 1em !important;
    };
    .message-box {
      box-sizing: border-box;
      display: flex;
      justify-content: flex-start;
      margin-top: 2em;
      margin-bottom: 2em;

      .message-icon-box{
        padding-right: 1em;
      }
    }

    .in-window-close-button-container {
      display: flex;
      justify-content: center;
    }
  }

  .margin-top-1em {
    margin-top: 1em;
  }
}</code>" data-collapsed="#in-window-content { ... }"><code>#in-window-content { ... }</code></pre></div></section></section></section><section class="main__section"><h1 class="main__heading" id="global"><div class="container">global</div></h1><section class="main__sub-section" id="global-css"><h2 class="main__heading--secondary"><div class="container">css</div></h2><section class="main__item container item" id="global-css-body"><h3 class="item__heading"><a class="item__name" href="#css-body">body</a></h3><div class="item__code-wrapper"><pre class="item__code item__code--togglable language-scss" data-current-state="collapsed" data-expanded="<code>body {
  font-family: &quot;Foundry Sterling W01&quot;, sans-serif !important;
  color: $dark;
  background-color: $light;
  font-weight: 300;
  font-size: 16px;
  font-style: normal;
  position: relative;
}</code>" data-collapsed="body { ... }"><code>body { ... }</code></pre></div></section></section></section><section class="main__section"><h1 class="main__heading" id="layout"><div class="container">layout</div></h1><section class="main__sub-section" id="layout-mixin"><h2 class="main__heading--secondary"><div class="container">mixins</div></h2><section class="main__item container item" id="layout-mixin-media-breakpoint-down"><h3 class="item__heading"><a class="item__name" href="#mixin-media-breakpoint-down">media-breakpoint-down</a></h3><div class="item__code-wrapper"><pre class="item__code item__code--togglable language-scss" data-current-state="collapsed" data-expanded="@mixin media-breakpoint-down($size) { 
  @if $size == sm {
    @media (max-width: 575px) {
      @content;
    }
  } @else if $size == md {
    @media (max-width: 767px) {
      @content;
    }
  } @else if $size == lg {
    @media (max-width: 991px) {
      @content;
    }
  } @else if $size == xl {
    @media (max-width: 1199px) {
      @content;
    }
  } @else {
    @media {
      @content;
    }
  }
 }" data-collapsed="@mixin media-breakpoint-down($size) { ... }"><code>@mixin media-breakpoint-down($size) { ... }</code></pre></div><h3 class="item__sub-heading">Parameters</h3><table class="item__parameters"><thead><tr><th scope="col"><span class="visually-hidden">parameter </span>Name</th><th scope="col"><span class="visually-hidden">parameter </span>Description</th><th scope="col"><span class="visually-hidden">parameter </span>Type</th><th scope="col"><span class="visually-hidden">parameter </span>Default value</th></tr></thead><tbody><tr class="item__parameter"><th scope="row" data-label="name"><code>$size</code></th><td data-label="desc">&mdash;<span class="visually-hidden"> none</span></td><td data-label="type"><code>Sm</code> or <code>Md</code> or <code>Lg</code> or <code>Xl</code></td><td data-label="default">&mdash;<span class="visually-hidden"> none</span></td></tr></tbody></table><h3 class="item__sub-heading">Used by</h3><ul class="list-unstyled"><li><span class="item__cross-type">[mixin]</span> <a href="#layout-mixin-hide-down"><code>hide-down</code></a></li></ul></section><section class="main__item container item" id="layout-mixin-media-breakpoint-up"><h3 class="item__heading"><a class="item__name" href="#mixin-media-breakpoint-up">media-breakpoint-up</a></h3><div class="item__code-wrapper"><pre class="item__code item__code--togglable language-scss" data-current-state="collapsed" data-expanded="@mixin media-breakpoint-up($size) { 
  @if $size == sm {
    @media (min-width: 576px) {
      @content;
    }
  } @else if $size == md {
    @media (min-width: 768px) {
      @content;
    }
  } @else if $size == lg {
    @media (min-width: 992px) {
      @content;
    }
  } @else if $size == xl {
    @media (min-width: 1200px) {
      @content;
    }
  } @else if $size == xxl {
    @media (min-width: 1800px) {
      @content;
    }
  } @else {
    @media {
      @content;
    }
  }
 }" data-collapsed="@mixin media-breakpoint-up($size) { ... }"><code>@mixin media-breakpoint-up($size) { ... }</code></pre></div><h3 class="item__sub-heading">Parameters</h3><table class="item__parameters"><thead><tr><th scope="col"><span class="visually-hidden">parameter </span>Name</th><th scope="col"><span class="visually-hidden">parameter </span>Description</th><th scope="col"><span class="visually-hidden">parameter </span>Type</th><th scope="col"><span class="visually-hidden">parameter </span>Default value</th></tr></thead><tbody><tr class="item__parameter"><th scope="row" data-label="name"><code>$size</code></th><td data-label="desc">&mdash;<span class="visually-hidden"> none</span></td><td data-label="type"><code>Sm</code> or <code>Md</code> or <code>Lg</code> or <code>Xl</code></td><td data-label="default">&mdash;<span class="visually-hidden"> none</span></td></tr></tbody></table><h3 class="item__sub-heading">Used by</h3><ul class="list-unstyled"><li><span class="item__cross-type">[mixin]</span> <a href="#layout-mixin-hide-up"><code>hide-up</code></a></li></ul></section><section class="main__item container item" id="layout-mixin-hide"><h3 class="item__heading"><a class="item__name" href="#mixin-hide">hide</a></h3><div class="item__code-wrapper"><pre class="item__code item__code--togglable language-scss" data-current-state="collapsed" data-expanded="@mixin hide() { 
  position: fixed !important;
  /* keep it on viewport */
  top: 0 !important;
  left: 0 !important;
  /* give it non-zero size, VoiceOver on Safari requires at least 2 pixels
     before allowing buttons to be activated. */
  width: 4px !important;
  height: 4px !important;
  /* visually hide it with overflow and opacity */
  opacity: 0 !important;
  overflow: hidden !important;
  /* remove any margin or padding */
  border: none !important;
  margin: 0 !important;
  padding: 0 !important;
  /* ensure no other style sets display to none */
  display: block !important;
  visibility: visible !important;
 }" data-collapsed="@mixin hide() { ... }"><code>@mixin hide() { ... }</code></pre></div><h3 class="item__sub-heading">Parameters</h3><p>None.</p></section><section class="main__item container item" id="layout-mixin-hide-down"><h3 class="item__heading"><a class="item__name" href="#mixin-hide-down">hide-down</a></h3><div class="item__code-wrapper"><pre class="item__code item__code--togglable language-scss" data-current-state="collapsed" data-expanded="@mixin hide-down($size) { 
  @include media-breakpoint-down($size) {
    @include hide
  }
 }" data-collapsed="@mixin hide-down($size) { ... }"><code>@mixin hide-down($size) { ... }</code></pre></div><h3 class="item__sub-heading">Parameters</h3><table class="item__parameters"><thead><tr><th scope="col"><span class="visually-hidden">parameter </span>Name</th><th scope="col"><span class="visually-hidden">parameter </span>Description</th><th scope="col"><span class="visually-hidden">parameter </span>Type</th><th scope="col"><span class="visually-hidden">parameter </span>Default value</th></tr></thead><tbody><tr class="item__parameter"><th scope="row" data-label="name"><code>$size</code></th><td data-label="desc">&mdash;<span class="visually-hidden"> none</span></td><td data-label="type"><code>Sm</code> or <code>Md</code> or <code>Lg</code> or <code>Xl</code></td><td data-label="default">&mdash;<span class="visually-hidden"> none</span></td></tr></tbody></table><h3 class="item__sub-heading">Requires</h3><ul class="list-unstyled"><li class="item__description item__description--inline"><span class="item__cross-type">[mixin]</span> <a href="#layout-mixin-media-breakpoint-down"><code>media-breakpoint-down</code></a></li></ul></section><section class="main__item container item" id="layout-mixin-hide-up"><h3 class="item__heading"><a class="item__name" href="#mixin-hide-up">hide-up</a></h3><div class="item__code-wrapper"><pre class="item__code item__code--togglable language-scss" data-current-state="collapsed" data-expanded="@mixin hide-up($size) { 
  @include media-breakpoint-up($size) {
    @include hide
  }
 }" data-collapsed="@mixin hide-up($size) { ... }"><code>@mixin hide-up($size) { ... }</code></pre></div><h3 class="item__sub-heading">Parameters</h3><table class="item__parameters"><thead><tr><th scope="col"><span class="visually-hidden">parameter </span>Name</th><th scope="col"><span class="visually-hidden">parameter </span>Description</th><th scope="col"><span class="visually-hidden">parameter </span>Type</th><th scope="col"><span class="visually-hidden">parameter </span>Default value</th></tr></thead><tbody><tr class="item__parameter"><th scope="row" data-label="name"><code>$size</code></th><td data-label="desc">&mdash;<span class="visually-hidden"> none</span></td><td data-label="type"><code>Sm</code> or <code>Md</code> or <code>Lg</code> or <code>Xl</code></td><td data-label="default">&mdash;<span class="visually-hidden"> none</span></td></tr></tbody></table><h3 class="item__sub-heading">Requires</h3><ul class="list-unstyled"><li class="item__description item__description--inline"><span class="item__cross-type">[mixin]</span> <a href="#layout-mixin-media-breakpoint-up"><code>media-breakpoint-up</code></a></li></ul></section></section></section><section class="main__section"><h1 class="main__heading" id="typography"><div class="container">typography</div></h1><section class="main__sub-section" id="typography-mixin"><h2 class="main__heading--secondary"><div class="container">mixins</div></h2><section class="main__item container item" id="typography-mixin-font-weight"><h3 class="item__heading"><a class="item__name" href="#mixin-font-weight">font-weight</a></h3><div class="item__code-wrapper"><pre class="item__code item__code--togglable language-scss" data-current-state="collapsed" data-expanded="@mixin font-weight() { 
  @for $i from 1 through 9 {
    ._msg-font-weight-#{$i * 100} {
      font-weight: $i * 100 !important;
    }
  }
 }" data-collapsed="@mixin font-weight() { ... }"><code>@mixin font-weight() { ... }</code></pre></div><h3 class="item__sub-heading">Description</h3><div class="item__description"><p>generates font-weight utility classes</p></div><h3 class="item__sub-heading">Parameters</h3><p>None.</p><h3 class="item__sub-heading">Example</h3><div class="item__example example"><pre class="example__code language-html"><code>&lt;div class=&quot;_msg-font-weight-200&quot;&gt;...&lt;/div&gt;</code></pre></div><h3 class="item__sub-heading">Output</h3><div class="item__description"><p>_msg-font-weight-100, _msg-font-weight-200,... _msg-font-weight-900</p></div></section><section class="main__item container item" id="typography-mixin-font-size-em"><h3 class="item__heading"><a class="item__name" href="#mixin-font-size-em">font-size-em</a></h3><div class="item__code-wrapper"><pre class="item__code item__code--togglable language-scss" data-current-state="collapsed" data-expanded="@mixin font-size-em() { 
  @for $i from 1 through 9 {
    ._msg-font-size-0\.#{$i}em {
      font-size: $i / 10 + em !important;
    }
    ._msg-font-size-1\.#{$i}em {
      font-size: 1 + $i / 10 + em !important;
    }
  }
 }" data-collapsed="@mixin font-size-em() { ... }"><code>@mixin font-size-em() { ... }</code></pre></div><h3 class="item__sub-heading">Description</h3><div class="item__description"><p>generates font-size utility classes</p></div><h3 class="item__sub-heading">Parameters</h3><p>None.</p><h3 class="item__sub-heading">Example</h3><div class="item__example example"><pre class="example__code language-html"><code>&lt;div class=&quot;_msg-font-size-em-2&quot;&gt;...&lt;/div&gt;</code></pre></div><h3 class="item__sub-heading">Output</h3><div class="item__description"><p>_msg-font-size-em-1, _msg-font-size-em-2, ... _msg-font-size-em-9</p></div></section><section class="main__item container item" id="typography-mixin-line-height-em"><h3 class="item__heading"><a class="item__name" href="#mixin-line-height-em">line-height-em</a></h3><div class="item__code-wrapper"><pre class="item__code item__code--togglable language-scss" data-current-state="collapsed" data-expanded="@mixin line-height-em() { 
  @for $i from 1 through 9 {
    ._msg-line-height-0\.#{$i}em {
      line-height: $i / 10 + em !important;
    }
    ._msg-line-height-1\.#{$i}em {
      line-height: 1 + $i / 10 + em !important;
    }
  }
 }" data-collapsed="@mixin line-height-em() { ... }"><code>@mixin line-height-em() { ... }</code></pre></div><h3 class="item__sub-heading">Description</h3><div class="item__description"><p>generates line-height utility classes</p></div><h3 class="item__sub-heading">Parameters</h3><p>None.</p><h3 class="item__sub-heading">Example</h3><div class="item__example example"><pre class="example__code language-html"><code>&lt;div class=&quot;_msg-line-height-1.9em&quot;&gt;...&lt;/div&gt;</code></pre></div><h3 class="item__sub-heading">Output</h3><div class="item__description"><p>_msg-line-height-0.1em, _msg-line-height-0.9em, ... _msg-line-height-1.9em</p></div></section></section></section><section class="main__section"><h1 class="main__heading" id="utils"><div class="container">utils</div></h1><section class="main__sub-section" id="utils-css"><h2 class="main__heading--secondary"><div class="container">css</div></h2><section class="main__item container item" id="utils-css-._msg-ul-reset"><h3 class="item__heading"><a class="item__name" href="#css-._msg-ul-reset">._msg-ul-reset</a></h3><div class="item__code-wrapper"><pre class="item__code item__code--togglable language-scss" data-current-state="collapsed" data-expanded="<code>._msg-ul-reset {
  list-style: none;
  padding: 0;
  margin: 0;
  ul {
    list-style: none;
    padding: 0;
    margin: 0;
  }
  li {
    margin: 0;
    padding: 0;
  }
}</code>" data-collapsed="._msg-ul-reset { ... }"><code>._msg-ul-reset { ... }</code></pre></div><h3 class="item__sub-heading">Description</h3><div class="item__description"><p>resets the native styles of elements, for now only dealing wit ul/li/a</p></div><h3 class="item__sub-heading">Example</h3><div class="item__example example"><pre class="example__code language-html"><code>&lt;ul class=&quot;_msg-ul-reset&quot;&gt;...&lt;/ul&gt;</code></pre></div></section><section class="main__item container item" id="utils-css-._msg-a-reset"><h3 class="item__heading"><a class="item__name" href="#css-._msg-a-reset">._msg-a-reset</a></h3><div class="item__code-wrapper"><pre class="item__code item__code--togglable language-scss" data-current-state="collapsed" data-expanded="<code>._msg-a-reset {
  &amp;:hover {
    text-decoration: none;
  }
  a {
    &amp;:hover {
      text-decoration: none;
    }
  }
}</code>" data-collapsed="._msg-a-reset { ... }"><code>._msg-a-reset { ... }</code></pre></div><h3 class="item__sub-heading">Description</h3><div class="item__description"><p>resets the native styles of a element</p></div><h3 class="item__sub-heading">Example</h3><div class="item__example example"><pre class="example__code language-html"><code>&lt;a class=&quot;_msg-a-reset&quot;&gt;...&lt;/a&gt;</code></pre></div></section></section></section><footer class="footer" role="contentinfo"><div class="container"><div class="footer__project-info project-info"><!-- Name and URL --> <a class="project-info__name" href="https://bitbucket.org/made_simple/cms#readme">Cms</a><!-- Version --> <span class="project-info__version">- v1.0.0</span><!-- License --></div><a class="footer__watermark" href="http://sassdoc.com"><img src="assets/images/logo_light_inline.svg" alt="SassDoc Logo"></a></div></footer></article><script src="https://ajax.googleapis.com/ajax/libs/jquery/2.1.1/jquery.min.js"></script><script>window.jQuery || document.write('<script src="assets/js/vendor/jquery.min.js"><\/script>')</script><script src="assets/js/main.min.js"></script></body></html>